"""
News and Content Analysis Tools

Tools for collecting sports news, analyzing sentiment, and extracting
fantasy-relevant information from various news sources.
"""

import logging
import requests
import re
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class NewsScrapingTool:
    """Tool for scraping and analyzing sports news."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.news_sources = {
            'nrl_official': 'https://www.nrl.com/news',
            'fox_sports': 'https://www.foxsports.com.au/nrl',
            'nine_news': 'https://wwos.nine.com.au/nrl',
            'abc_sport': 'https://www.abc.net.au/sport/nrl'
        }
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'FantasyPro News Bot 1.0'
        })
    
    def scrape_nrl_news(self, hours_back: int = 24) -> List[Dict[str, Any]]:
        """Scrape recent NRL news articles."""
        articles = []
        
        try:
            # Mock news data for development
            mock_articles = [
                {
                    'id': 'news_001',
                    'title': 'Star halfback ruled out with injury',
                    'summary': 'Key fantasy player will miss 2-3 weeks with hamstring strain',
                    'content': 'The Penrith Panthers halfback has been ruled out for the next 2-3 weeks...',
                    'source': 'NRL Official',
                    'url': 'https://www.nrl.com/news/injury-update',
                    'published_at': datetime.now() - timedelta(hours=2),
                    'impact_players': ['Nathan Cleary'],
                    'impact_teams': ['Penrith Panthers'],
                    'category': 'injury',
                    'sentiment': 'negative',
                    'fantasy_relevance': 0.9
                },
                {
                    'id': 'news_002',
                    'title': 'Young gun gets first NRL start',
                    'summary': 'Rookie player named in starting lineup for weekend clash',
                    'content': 'The 19-year-old will make his NRL debut this weekend...',
                    'source': 'Fox Sports',
                    'url': 'https://www.foxsports.com.au/debut',
                    'published_at': datetime.now() - timedelta(hours=6),
                    'impact_players': ['Rookie Player'],
                    'impact_teams': ['Sydney Roosters'],
                    'category': 'team_news',
                    'sentiment': 'positive',
                    'fantasy_relevance': 0.6
                },
                {
                    'id': 'news_003',
                    'title': 'Coach confirms team changes',
                    'summary': 'Multiple changes to starting lineup ahead of crucial match',
                    'content': 'The coach has made several changes to the team...',
                    'source': 'Nine News',
                    'url': 'https://wwos.nine.com.au/team-changes',
                    'published_at': datetime.now() - timedelta(hours=12),
                    'impact_players': ['Player A', 'Player B'],
                    'impact_teams': ['Melbourne Storm'],
                    'category': 'team_news',
                    'sentiment': 'neutral',
                    'fantasy_relevance': 0.7
                }
            ]
            
            # Filter by time
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            articles = [
                article for article in mock_articles 
                if article['published_at'] > cutoff_time
            ]
            
            logger.info(f"Scraped {len(articles)} news articles")
            return articles
            
        except Exception as e:
            logger.error(f"Error scraping news: {e}")
            return []
    
    def extract_player_mentions(self, text: str) -> List[str]:
        """Extract player names mentioned in text."""
        # Simple pattern matching for common NRL player names
        # In production, this would use a comprehensive player database
        common_players = [
            'Nathan Cleary', 'James Tedesco', 'Kalyn Ponga', 'Cameron Munster',
            'Daly Cherry-Evans', 'Tom Trbojevic', 'Ryan Papenhuyzen',
            'Cody Walker', 'Mitchell Moses', 'Ben Hunt'
        ]
        
        mentioned_players = []
        text_lower = text.lower()
        
        for player in common_players:
            if player.lower() in text_lower:
                mentioned_players.append(player)
        
        return mentioned_players
    
    def categorize_news(self, article: Dict[str, Any]) -> str:
        """Categorize news article by type."""
        title_lower = article['title'].lower()
        content_lower = article.get('content', '').lower()
        
        # Injury-related keywords
        injury_keywords = ['injury', 'injured', 'hurt', 'strain', 'tear', 'break', 'fracture', 'concussion']
        if any(keyword in title_lower or keyword in content_lower for keyword in injury_keywords):
            return 'injury'
        
        # Team news keywords
        team_keywords = ['team', 'lineup', 'starting', 'bench', 'selection', 'named']
        if any(keyword in title_lower or keyword in content_lower for keyword in team_keywords):
            return 'team_news'
        
        # Performance keywords
        performance_keywords = ['performance', 'form', 'stats', 'record', 'milestone']
        if any(keyword in title_lower or keyword in content_lower for keyword in performance_keywords):
            return 'performance'
        
        # Transfer/contract keywords
        transfer_keywords = ['transfer', 'contract', 'signing', 'release', 'trade']
        if any(keyword in title_lower or keyword in content_lower for keyword in transfer_keywords):
            return 'transfer'
        
        return 'general'

class SentimentAnalyzer:
    """Tool for analyzing sentiment of news articles."""
    
    def __init__(self):
        # Simple sentiment word lists
        self.positive_words = {
            'excellent', 'outstanding', 'brilliant', 'superb', 'fantastic', 'amazing',
            'great', 'good', 'strong', 'impressive', 'dominant', 'successful',
            'win', 'victory', 'triumph', 'comeback', 'return', 'recover'
        }
        
        self.negative_words = {
            'terrible', 'awful', 'poor', 'bad', 'disappointing', 'struggling',
            'injured', 'hurt', 'suspended', 'banned', 'ruled out', 'sidelined',
            'loss', 'defeat', 'fail', 'error', 'mistake', 'concern', 'worry'
        }
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of text."""
        words = re.findall(r'\b\w+\b', text.lower())
        
        positive_count = sum(1 for word in words if word in self.positive_words)
        negative_count = sum(1 for word in words if word in self.negative_words)
        total_words = len(words)
        
        if total_words == 0:
            return {
                'sentiment': 'neutral',
                'score': 0.0,
                'confidence': 0.0
            }
        
        # Calculate sentiment score (-1 to 1)
        sentiment_score = (positive_count - negative_count) / total_words
        
        # Determine sentiment category
        if sentiment_score > 0.1:
            sentiment = 'positive'
        elif sentiment_score < -0.1:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'
        
        # Calculate confidence based on word count
        confidence = min(1.0, (positive_count + negative_count) / max(1, total_words * 0.1))
        
        return {
            'sentiment': sentiment,
            'score': round(sentiment_score, 3),
            'confidence': round(confidence, 3),
            'positive_words': positive_count,
            'negative_words': negative_count,
            'total_words': total_words
        }
    
    def analyze_fantasy_impact(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the fantasy sports impact of a news article."""
        sentiment = self.analyze_sentiment(article['title'] + ' ' + article.get('content', ''))
        
        # Base impact score
        impact_score = 0.5
        
        # Category-based impact
        category_impacts = {
            'injury': 0.9,
            'team_news': 0.7,
            'performance': 0.6,
            'transfer': 0.8,
            'general': 0.3
        }
        
        category = article.get('category', 'general')
        impact_score = category_impacts.get(category, 0.5)
        
        # Adjust based on sentiment for certain categories
        if category in ['injury', 'team_news']:
            if sentiment['sentiment'] == 'negative':
                impact_score = min(1.0, impact_score + 0.1)
        
        # Player mention impact
        mentioned_players = article.get('impact_players', [])
        if mentioned_players:
            impact_score = min(1.0, impact_score + len(mentioned_players) * 0.05)
        
        return {
            'impact_score': round(impact_score, 2),
            'category': category,
            'sentiment': sentiment,
            'affected_players': mentioned_players,
            'urgency': 'high' if impact_score > 0.8 else 'medium' if impact_score > 0.5 else 'low'
        }

class NewsAnalysisAgent:
    """Agent for comprehensive news analysis and fantasy impact assessment."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.scraper = NewsScrapingTool(api_key)
        self.sentiment_analyzer = SentimentAnalyzer()
        
    def analyze_recent_news(self, hours_back: int = 24) -> Dict[str, Any]:
        """Analyze recent news for fantasy impact."""
        logger.info(f"Analyzing news from the last {hours_back} hours...")
        
        # Scrape recent articles
        articles = self.scraper.scrape_nrl_news(hours_back)
        
        if not articles:
            return {
                'total_articles': 0,
                'analyzed_articles': [],
                'summary': {
                    'high_impact': 0,
                    'medium_impact': 0,
                    'low_impact': 0
                }
            }
        
        analyzed_articles = []
        impact_summary = {'high_impact': 0, 'medium_impact': 0, 'low_impact': 0}
        
        for article in articles:
            # Categorize article
            article['category'] = self.scraper.categorize_news(article)
            
            # Extract player mentions
            article['impact_players'] = self.scraper.extract_player_mentions(
                article['title'] + ' ' + article.get('content', '')
            )
            
            # Analyze fantasy impact
            impact_analysis = self.sentiment_analyzer.analyze_fantasy_impact(article)
            article.update(impact_analysis)
            
            analyzed_articles.append(article)
            
            # Update summary
            urgency = impact_analysis['urgency']
            if urgency == 'high':
                impact_summary['high_impact'] += 1
            elif urgency == 'medium':
                impact_summary['medium_impact'] += 1
            else:
                impact_summary['low_impact'] += 1
        
        # Sort by impact score
        analyzed_articles.sort(key=lambda x: x['impact_score'], reverse=True)
        
        logger.info(f"Analyzed {len(analyzed_articles)} articles")
        
        return {
            'total_articles': len(analyzed_articles),
            'analyzed_articles': analyzed_articles,
            'summary': impact_summary,
            'top_stories': analyzed_articles[:5],  # Top 5 by impact
            'analysis_timestamp': datetime.now().isoformat()
        }
    
    def get_player_news_summary(self, player_name: str, days_back: int = 7) -> Dict[str, Any]:
        """Get news summary for a specific player."""
        # Get recent articles
        articles = self.scraper.scrape_nrl_news(hours_back=days_back * 24)
        
        # Filter articles mentioning the player
        player_articles = []
        for article in articles:
            if player_name.lower() in article['title'].lower() or \
               player_name.lower() in article.get('content', '').lower():
                
                # Analyze this article
                article['category'] = self.scraper.categorize_news(article)
                impact_analysis = self.sentiment_analyzer.analyze_fantasy_impact(article)
                article.update(impact_analysis)
                player_articles.append(article)
        
        if not player_articles:
            return {
                'player': player_name,
                'articles_found': 0,
                'overall_sentiment': 'neutral',
                'fantasy_impact': 'low'
            }
        
        # Calculate overall sentiment
        sentiments = [article['sentiment']['sentiment'] for article in player_articles]
        positive_count = sentiments.count('positive')
        negative_count = sentiments.count('negative')
        
        if positive_count > negative_count:
            overall_sentiment = 'positive'
        elif negative_count > positive_count:
            overall_sentiment = 'negative'
        else:
            overall_sentiment = 'neutral'
        
        # Calculate average impact
        avg_impact = sum(article['impact_score'] for article in player_articles) / len(player_articles)
        
        return {
            'player': player_name,
            'articles_found': len(player_articles),
            'overall_sentiment': overall_sentiment,
            'average_impact_score': round(avg_impact, 2),
            'fantasy_impact': 'high' if avg_impact > 0.7 else 'medium' if avg_impact > 0.4 else 'low',
            'recent_articles': player_articles[:3],  # Most recent 3
            'analysis_period': f"{days_back} days"
        }

def main():
    """Demo function to test news analysis tools."""
    print("📰 Testing FantasyPro News Analysis Tools...")
    print("=" * 60)
    
    # Test news analysis
    news_agent = NewsAnalysisAgent()
    
    print("🔍 Analyzing recent news...")
    news_analysis = news_agent.analyze_recent_news(hours_back=24)
    
    print(f"   Total articles: {news_analysis['total_articles']}")
    print(f"   High impact: {news_analysis['summary']['high_impact']}")
    print(f"   Medium impact: {news_analysis['summary']['medium_impact']}")
    print(f"   Low impact: {news_analysis['summary']['low_impact']}")
    
    if news_analysis['top_stories']:
        print("\n📈 Top Stories:")
        for story in news_analysis['top_stories'][:3]:
            print(f"   • {story['title']}")
            print(f"     Impact: {story['impact_score']:.2f} | Category: {story['category']}")
            print(f"     Sentiment: {story['sentiment']['sentiment']}")
    
    print("\n👤 Player-specific news analysis...")
    player_news = news_agent.get_player_news_summary("Nathan Cleary", days_back=7)
    print(f"   Player: {player_news['player']}")
    print(f"   Articles found: {player_news['articles_found']}")
    print(f"   Overall sentiment: {player_news['overall_sentiment']}")
    print(f"   Fantasy impact: {player_news['fantasy_impact']}")
    
    print("\n✅ News analysis tools test completed!")

if __name__ == "__main__":
    main()
