#!/usr/bin/env python3
"""
SuperCoach Ownership Explorer
Explores authenticated SuperCoach interface to find ownership percentages and rankings
"""

import time
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)

class SuperCoachOwnershipExplorer:
    """Explore SuperCoach authenticated interface for ownership data"""
    
    def __init__(self, email: str = "<EMAIL>", password: str = "SuperKenny123!"):
        self.email = email
        self.password = password
        self.driver = None
        self.wait = None
        self.is_authenticated = False
        
        # URLs to explore for ownership data
        self.exploration_urls = [
            'https://www.supercoach.com.au/nrl/classic/team/select',
            'https://www.supercoach.com.au/nrl/classic/stats',
            'https://www.supercoach.com.au/nrl/classic/stats/ownership',
            'https://www.supercoach.com.au/nrl/classic/stats/players',
            'https://www.supercoach.com.au/nrl/classic/ladder',
            'https://www.supercoach.com.au/nrl/classic/rankings',
            'https://www.supercoach.com.au/nrl/stats',
            'https://www.supercoach.com.au/nrl/players',
            'https://www.supercoach.com.au/nrl/ownership',
            'https://myaccount.supercoach.com.au/nrl/classic/team',
            'https://myaccount.supercoach.com.au/nrl/classic/stats'
        ]
        
        self.cache_dir = Path("data/ownership_exploration")
        self.cache_dir.mkdir(exist_ok=True)
    
    def setup_driver(self) -> bool:
        """Setup Chrome driver with anti-detection"""
        try:
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Run in visible mode for exploration
            # options.add_argument('--headless')
            
            self.driver = webdriver.Chrome(
                service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
                options=options
            )
            
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 20)
            
            logger.info("✅ Chrome driver setup successful")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error setting up Chrome driver: {e}")
            return False
    
    def authenticate(self) -> bool:
        """Authenticate with SuperCoach using Auth0"""
        try:
            if not self.driver:
                if not self.setup_driver():
                    return False
            
            logger.info("🔐 Starting SuperCoach Auth0 authentication")
            
            # Navigate to Auth0 login directly
            auth0_url = "https://login.newscorpaustralia.com/login?state=hKFo2SBER3lEYktxVFNnN2RHSS1LLVlUQVNfOXlqclBtUng5VaFupWxvZ2luo3RpZNkgT2lsanBRUkhpY1lfM2JwOWJ6T19TNUFwZmx0TEhoY2SjY2lk2SBaWUNvdGxpaHFhR3VhcVNzU3Z1MEwydnhEZFFYQ3cxNg&client=ZYCotlihqaGuaqSsSvu0L2vxDdQXCw16&protocol=oauth2&response_type=token%20id_token&scope=openid%20profile&audience=newscorpaustralia&site=supercoach&redirect_uri=https%3A%2F%2Fwww.supercoach.com.au%2Fassets%2Fsites%2Fnews%2Fauth0%2Fcallback.html%3FredirectUri%3Dhttps%253A%252F%252Fwww.supercoach.com.au%252Fnrl&prevent_sign_up=&nonce=MwchNFYLpvljaSzA0FtG0kx5JhtggrZ5&auth0Client=eyJuYW1lIjoiYXV0aDAuanMiLCJ2ZXJzaW9uIjoiOS4yOC4wIn0%3D"
            self.driver.get(auth0_url)
            time.sleep(5)
            
            # Find and fill email field
            email_field = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'input[type="email"]')))
            email_field.clear()
            email_field.send_keys(self.email)
            time.sleep(1)
            
            # Find and fill password field
            password_field = self.driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
            password_field.clear()
            password_field.send_keys(self.password)
            time.sleep(1)
            
            # Submit form
            submit_button = self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            self.driver.execute_script("arguments[0].click();", submit_button)
            time.sleep(8)
            
            # Check if authentication was successful
            current_url = self.driver.current_url.lower()
            if 'supercoach.com.au' in current_url and 'login.newscorpaustralia.com' not in current_url:
                self.is_authenticated = True
                logger.info("✅ SuperCoach Auth0 authentication successful")
                return True
            else:
                logger.error("❌ SuperCoach Auth0 authentication failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error during Auth0 authentication: {e}")
            return False
    
    def explore_ownership_data(self) -> Dict[str, Any]:
        """Explore SuperCoach interface for ownership data after authentication"""
        try:
            if not self.authenticate():
                logger.error("Authentication failed")
                return {}

            exploration_results = {
                'exploration_timestamp': datetime.now().isoformat(),
                'spa_navigation_attempts': {},
                'ownership_data_found': {},
                'potential_selectors': []
            }

            # Since SuperCoach appears to be a SPA, try navigating through the interface
            logger.info("🔍 Exploring SuperCoach SPA interface for ownership data")

            # Start from the main authenticated page
            self.driver.get('https://www.supercoach.com.au/nrl')
            time.sleep(10)  # Wait longer for SPA to load

            # Look for navigation elements that might lead to ownership data
            nav_attempts = self._explore_spa_navigation()
            exploration_results['spa_navigation_attempts'] = nav_attempts

            # Try direct JavaScript execution to find data
            js_data = self._extract_data_via_javascript()
            if js_data:
                exploration_results['javascript_data'] = js_data

            # Look for API calls in network traffic
            api_data = self._monitor_network_requests()
            if api_data:
                exploration_results['api_requests'] = api_data

            # Save exploration results
            self._save_exploration_results(exploration_results)

            return exploration_results

        except Exception as e:
            logger.error(f"Error in ownership exploration: {e}")
            return {}
        finally:
            if self.driver:
                self.driver.quit()

    def _explore_spa_navigation(self) -> Dict[str, Any]:
        """Explore SPA navigation to find ownership data"""
        try:
            nav_results = {}

            # Look for navigation links/buttons
            nav_selectors = [
                'a[href*="stats"]', 'a[href*="ownership"]', 'a[href*="ladder"]',
                'button:contains("Stats")', 'button:contains("Ownership")',
                'button:contains("Ladder")', '.nav-link', '.menu-item',
                '[data-testid*="nav"]', '[data-testid*="menu"]'
            ]

            for selector in nav_selectors:
                try:
                    if 'contains' in selector:
                        # Convert to XPath
                        text_content = selector.split('contains(')[1].split(')')[0].strip('"')
                        xpath = f"//button[contains(text(), '{text_content}')]"
                        elements = self.driver.find_elements(By.XPATH, xpath)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if elements:
                        nav_results[selector] = []
                        for element in elements[:5]:  # Limit to first 5
                            try:
                                element_info = {
                                    'text': element.text.strip(),
                                    'href': element.get_attribute('href'),
                                    'onclick': element.get_attribute('onclick'),
                                    'data_attributes': {}
                                }

                                # Get data attributes
                                for attr in element.get_property('attributes'):
                                    if attr['name'].startswith('data-'):
                                        element_info['data_attributes'][attr['name']] = attr['value']

                                nav_results[selector].append(element_info)

                                # Try clicking promising elements
                                if any(keyword in element.text.lower() for keyword in ['stats', 'ownership', 'ladder']):
                                    logger.info(f"Trying to click: {element.text}")
                                    try:
                                        self.driver.execute_script("arguments[0].click();", element)
                                        time.sleep(5)

                                        # Check if we found ownership data
                                        ownership_data = self._extract_ownership_indicators("spa_navigation")
                                        if ownership_data and ownership_data['data']:
                                            nav_results[f'{selector}_clicked_data'] = ownership_data['data']

                                        # Go back
                                        self.driver.back()
                                        time.sleep(3)

                                    except Exception as e:
                                        logger.debug(f"Error clicking element: {e}")
                                        continue

                            except Exception as e:
                                logger.debug(f"Error processing element: {e}")
                                continue

                except Exception as e:
                    logger.debug(f"Error with selector {selector}: {e}")
                    continue

            return nav_results

        except Exception as e:
            logger.error(f"Error exploring SPA navigation: {e}")
            return {}

    def _extract_data_via_javascript(self) -> Dict[str, Any]:
        """Try to extract data using JavaScript"""
        try:
            js_results = {}

            # Try to access common JavaScript data stores
            js_queries = [
                "return window.__INITIAL_STATE__ || null;",
                "return window.__REDUX_STATE__ || null;",
                "return window.APP_DATA || null;",
                "return window.SUPERCOACH_DATA || null;",
                "return window.localStorage.getItem('supercoach') || null;",
                "return window.sessionStorage.getItem('supercoach') || null;",
                "return Object.keys(window).filter(key => key.includes('data') || key.includes('state') || key.includes('supercoach'));",
                "return document.querySelectorAll('[data-ownership], [data-picked], .ownership, .picked').length;",
                "return Array.from(document.querySelectorAll('*')).filter(el => el.textContent.includes('%')).slice(0, 10).map(el => ({tag: el.tagName, text: el.textContent.trim(), class: el.className}));"
            ]

            for i, query in enumerate(js_queries):
                try:
                    result = self.driver.execute_script(query)
                    if result:
                        js_results[f'query_{i}'] = result
                        logger.info(f"JavaScript query {i} returned data")
                except Exception as e:
                    logger.debug(f"JavaScript query {i} failed: {e}")
                    continue

            return js_results

        except Exception as e:
            logger.error(f"Error extracting data via JavaScript: {e}")
            return {}

    def _monitor_network_requests(self) -> Dict[str, Any]:
        """Monitor network requests for API calls"""
        try:
            # Get browser logs to see network requests
            logs = self.driver.get_log('performance')

            api_requests = []
            for log in logs:
                try:
                    message = json.loads(log['message'])
                    if message.get('message', {}).get('method') == 'Network.responseReceived':
                        response = message['message']['params']['response']
                        url = response.get('url', '')

                        # Look for API endpoints that might contain ownership data
                        if any(keyword in url.lower() for keyword in ['api', 'stats', 'ownership', 'player', 'data']):
                            api_requests.append({
                                'url': url,
                                'status': response.get('status'),
                                'mimeType': response.get('mimeType'),
                                'timestamp': log['timestamp']
                            })

                except Exception as e:
                    continue

            return {'api_requests': api_requests}

        except Exception as e:
            logger.error(f"Error monitoring network requests: {e}")
            return {}
    
    def _extract_ownership_indicators(self, url: str) -> Dict[str, Any]:
        """Extract ownership-related indicators from current page"""
        try:
            page_source = self.driver.page_source.lower()
            
            # Look for ownership-related keywords
            ownership_keywords = [
                'ownership', 'owned', 'picked', '% teams', 'percentage',
                'selected by', 'popularity', 'captain %', 'vice captain %'
            ]
            
            indicators = []
            for keyword in ownership_keywords:
                if keyword in page_source:
                    indicators.append(keyword)
            
            # Look for percentage patterns
            import re
            percentage_patterns = re.findall(r'\d+\.?\d*%', self.driver.page_source)
            
            # Look for tables that might contain ownership data
            tables = self.driver.find_elements(By.CSS_SELECTOR, 'table')
            table_data = []
            
            for i, table in enumerate(tables[:5]):  # Limit to first 5 tables
                try:
                    # Get table headers
                    headers = []
                    header_elements = table.find_elements(By.CSS_SELECTOR, 'th, thead td')
                    for header in header_elements:
                        header_text = header.text.strip().lower()
                        headers.append(header_text)
                        
                        # Check if header suggests ownership data
                        if any(keyword in header_text for keyword in ['ownership', 'owned', 'picked', '%']):
                            indicators.append(f"table_{i}_header: {header_text}")
                    
                    # Get first few rows of data
                    rows = table.find_elements(By.CSS_SELECTOR, 'tbody tr, tr')[:5]
                    row_data = []
                    for row in rows:
                        cells = row.find_elements(By.CSS_SELECTOR, 'td, th')
                        cell_texts = [cell.text.strip() for cell in cells]
                        if cell_texts and any(cell for cell in cell_texts):
                            row_data.append(cell_texts)
                    
                    if headers or row_data:
                        table_data.append({
                            'table_index': i,
                            'headers': headers,
                            'sample_rows': row_data[:3]
                        })
                
                except Exception as e:
                    logger.debug(f"Error extracting table {i}: {e}")
                    continue
            
            # Look for specific ownership selectors
            ownership_selectors = [
                '.ownership', '.owned', '.picked', '.percentage',
                '[data-ownership]', '[data-picked]', '.stats-ownership',
                '.player-ownership', '.team-ownership'
            ]
            
            found_elements = []
            for selector in ownership_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        for element in elements[:3]:
                            found_elements.append({
                                'selector': selector,
                                'text': element.text.strip(),
                                'attributes': element.get_attribute('outerHTML')[:200]
                            })
                except:
                    continue
            
            return {
                'indicators': indicators,
                'data': {
                    'percentage_patterns': percentage_patterns[:10],
                    'tables': table_data,
                    'ownership_elements': found_elements,
                    'page_contains_ownership_keywords': len(indicators) > 0
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting ownership indicators: {e}")
            return {'indicators': [], 'data': {}}
    
    def _save_page_source(self, requested_url: str, actual_url: str):
        """Save page source for manual inspection"""
        try:
            # Create safe filename
            safe_name = requested_url.replace('https://', '').replace('/', '_').replace(':', '_')
            filename = f"page_source_{safe_name}.html"
            filepath = self.cache_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"<!-- Requested URL: {requested_url} -->\n")
                f.write(f"<!-- Actual URL: {actual_url} -->\n")
                f.write(f"<!-- Timestamp: {datetime.now().isoformat()} -->\n")
                f.write(self.driver.page_source)
            
            logger.debug(f"Saved page source to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving page source: {e}")
    
    def _save_exploration_results(self, results: Dict[str, Any]):
        """Save exploration results"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_file = self.cache_dir / f"ownership_exploration_{timestamp}.json"
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            # Also save as latest
            latest_file = self.cache_dir / "ownership_exploration_latest.json"
            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Saved exploration results to {results_file}")
            
        except Exception as e:
            logger.error(f"Error saving exploration results: {e}")

def main():
    """Test the ownership explorer"""
    print("🔍 Testing SuperCoach Ownership Explorer")
    
    explorer = SuperCoachOwnershipExplorer()
    results = explorer.explore_ownership_data()
    
    if results:
        print("✅ Exploration completed!")
        
        urls_explored = len(results.get('urls_explored', {}))
        ownership_found = len(results.get('ownership_data_found', {}))
        
        print(f"📊 URLs explored: {urls_explored}")
        print(f"🎯 URLs with ownership data: {ownership_found}")
        
        if ownership_found > 0:
            print("🎉 Found potential ownership data sources:")
            for url, data in results.get('ownership_data_found', {}).items():
                print(f"  - {url}")
                if data.get('tables'):
                    print(f"    Tables: {len(data['tables'])}")
                if data.get('percentage_patterns'):
                    print(f"    Percentages found: {len(data['percentage_patterns'])}")
    else:
        print("❌ Exploration failed")

if __name__ == "__main__":
    main()
