import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { motion, AnimatePresence } from 'framer-motion';
import ThemeToggle from './ThemeToggle';
import {
  HomeIcon,
  ChartBarIcon,
  UserGroupIcon,
  TrophyIcon,
  CogIcon,
  BellIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowTrendingUpIcon,
  ExclamationTriangleIcon,
  FireIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, current: router.pathname === '/dashboard' },
    { name: 'My Team', href: '/my-team', icon: UserGroupIcon, current: router.pathname === '/my-team' },
    { name: 'Players', href: '/players', icon: TrophyIcon, current: router.pathname === '/players' },
    { name: 'Analytics', href: '/analytics', icon: ChartBarIcon, current: router.pathname === '/analytics' },
    { name: 'Trades', href: '/trades', icon: ArrowTrendingUpIcon, current: router.pathname === '/trades' },
    { name: 'Injuries', href: '/injuries', icon: ExclamationTriangleIcon, current: router.pathname === '/injuries' },
    { name: 'Captain', href: '/captain', icon: FireIcon, current: router.pathname === '/captain' },
    { name: 'Pricing', href: '/pricing', icon: CurrencyDollarIcon, current: router.pathname === '/pricing' },
  ];

  const secondaryNavigation = [
    { name: 'Settings', href: '/settings', icon: CogIcon },
    { name: 'API Test', href: '/api-test', icon: BellIcon },
  ];

  return (
    <div className="min-h-screen themed-bg-primary theme-transition">
      {/* Mobile sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <>
            <motion.div
              className="fixed inset-0 z-40 lg:hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div className="fixed inset-0 themed-bg-overlay" onClick={() => setSidebarOpen(false)} />
              <motion.div
                className="fixed inset-y-0 left-0 z-50 w-64 themed-bg-secondary themed-border"
                initial={{ x: -256 }}
                animate={{ x: 0 }}
                exit={{ x: -256 }}
                transition={{ type: "spring", damping: 30, stiffness: 300 }}
              >
                <div className="flex items-center justify-between h-16 px-6 themed-border">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center themed-glow-primary glow-pulse-green">
                      <span className="themed-text-inverse font-bold text-sm">FP</span>
                    </div>
                    <span className="text-xl font-bold themed-text-primary">FantasyPro</span>
                  </div>
                  <button
                    onClick={() => setSidebarOpen(false)}
                    className="text-slate-400 hover:text-white"
                  >
                    <XMarkIcon className="w-6 h-6" />
                  </button>
                </div>
                <nav className="mt-6 px-3">
                  <div className="space-y-1">
                    {navigation.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={item.current ? 'nav-link-active' : 'nav-link-inactive'}
                        onClick={() => setSidebarOpen(false)}
                      >
                        <item.icon className="w-5 h-5 mr-3" />
                        {item.name}
                      </Link>
                    ))}
                  </div>
                  <div className="mt-8 pt-6 border-t border-slate-700">
                    <div className="space-y-1">
                      {secondaryNavigation.map((item) => (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="nav-link-inactive"
                          onClick={() => setSidebarOpen(false)}
                        >
                          <item.icon className="w-5 h-5 mr-3" />
                          {item.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                </nav>
              </motion.div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-slate-800 border-r border-slate-700 px-6 pb-4">
          <div className="flex h-16 shrink-0 items-center border-b border-slate-700">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center shadow-glow-green glow-pulse-green">
                <span className="text-white font-bold text-sm">FP</span>
              </div>
              <span className="text-xl font-bold text-white">FantasyPro</span>
            </div>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className={`${item.current ? 'nav-link-active shadow-glow-green' : 'nav-link-inactive'} magnetic`}
                      >
                        <item.icon className="w-5 h-5 mr-3" />
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
              <li className="mt-auto">
                <ul role="list" className="-mx-2 space-y-1">
                  {secondaryNavigation.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="nav-link-inactive"
                      >
                        <item.icon className="w-5 h-5 mr-3" />
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top navigation */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-slate-700 bg-slate-800 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-slate-400 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" />
          </button>

          {/* Separator */}
          <div className="h-6 w-px bg-slate-700 lg:hidden" />

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Live status indicator */}
              <div className="flex items-center space-x-2 bg-green-900/20 px-3 py-1 rounded-lg status-live shadow-glow-green">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-400">SuperCoach Live</span>
                <span className="text-xs text-slate-400">Real Data</span>
              </div>
            </div>
            
            <div className="flex items-center gap-x-4 lg:gap-x-6 ml-auto">
              {/* Theme Toggle */}
              <ThemeToggle variant="dropdown" size="md" />

              {/* Notifications */}
              <button
                type="button"
                className="-m-2.5 p-2.5 themed-text-tertiary hover:themed-text-primary magnetic hover:themed-glow-secondary transition-all duration-300"
              >
                <span className="sr-only">View notifications</span>
                <BellIcon className="h-6 w-6" />
              </button>

              {/* Last updated */}
              <div className="text-sm themed-text-tertiary">
                Last updated: {new Date().toLocaleTimeString()} • Live
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-8">
          <div className="px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
