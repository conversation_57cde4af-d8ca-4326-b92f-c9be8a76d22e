#!/usr/bin/env python3
"""
FantasyPro White-label Customization System

A flexible theming and branding system to support multiple clients
with custom configurations, branding, and feature sets.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
from pathlib import Path
import yaml

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logging_config import setup_logging

logger = setup_logging(__name__)

@dataclass
class BrandingConfig:
    """Branding configuration for white-label clients."""
    client_id: str
    client_name: str
    logo_url: str
    favicon_url: str
    primary_color: str
    secondary_color: str
    accent_color: str
    background_color: str
    text_color: str
    font_family: str
    custom_css: Optional[str] = None
    
@dataclass
class FeatureConfig:
    """Feature configuration for white-label clients."""
    client_id: str
    enabled_features: List[str]
    disabled_features: List[str]
    custom_features: Dict[str, Any]
    api_limits: Dict[str, int]
    subscription_tier: str
    
@dataclass
class ContentConfig:
    """Content configuration for white-label clients."""
    client_id: str
    welcome_message: str
    help_text: Dict[str, str]
    custom_pages: Dict[str, str]
    terms_url: str
    privacy_url: str
    support_email: str
    social_links: Dict[str, str]

class WhiteLabelManager:
    """Manages white-label configurations for multiple clients."""
    
    def __init__(self, config_dir: str = "customization/configs"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Default configurations
        self.default_branding = BrandingConfig(
            client_id="default",
            client_name="FantasyPro",
            logo_url="/assets/logo.png",
            favicon_url="/assets/favicon.ico",
            primary_color="#667eea",
            secondary_color="#764ba2",
            accent_color="#f093fb",
            background_color="#f8fafc",
            text_color="#1a202c",
            font_family="Inter, sans-serif"
        )
        
        self.default_features = FeatureConfig(
            client_id="default",
            enabled_features=[
                "player_analysis", "team_optimization", "trade_suggestions",
                "real_time_updates", "weather_analysis", "news_alerts",
                "performance_tracking", "league_management"
            ],
            disabled_features=[],
            custom_features={},
            api_limits={
                "requests_per_hour": 1000,
                "data_exports_per_day": 10,
                "ai_analyses_per_day": 100
            },
            subscription_tier="premium"
        )
        
        self.default_content = ContentConfig(
            client_id="default",
            welcome_message="Welcome to FantasyPro - Your AI-powered fantasy sports advantage!",
            help_text={
                "dashboard": "Your dashboard shows key stats and recommendations",
                "players": "Browse and analyze player performance data",
                "teams": "Optimize your team composition and strategy"
            },
            custom_pages={},
            terms_url="/terms",
            privacy_url="/privacy",
            support_email="<EMAIL>",
            social_links={
                "twitter": "https://twitter.com/fantasypro",
                "facebook": "https://facebook.com/fantasypro"
            }
        )
        
        # Load existing configurations
        self.client_configs = self._load_all_configs()
        
    def _load_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """Load all client configurations from disk."""
        configs = {}
        
        for config_file in self.config_dir.glob("*.json"):
            try:
                with open(config_file, 'r') as f:
                    client_config = json.load(f)
                    client_id = client_config.get('client_id')
                    if client_id:
                        configs[client_id] = client_config
                        logger.info(f"Loaded config for client: {client_id}")
            except Exception as e:
                logger.error(f"Error loading config {config_file}: {e}")
        
        return configs
    
    def create_client_config(self, client_id: str, 
                           branding: Optional[BrandingConfig] = None,
                           features: Optional[FeatureConfig] = None,
                           content: Optional[ContentConfig] = None) -> Dict[str, Any]:
        """Create a new client configuration."""
        
        # Use defaults if not provided
        if branding is None:
            branding = self.default_branding
            branding.client_id = client_id
        
        if features is None:
            features = self.default_features
            features.client_id = client_id
        
        if content is None:
            content = self.default_content
            content.client_id = client_id
        
        # Create complete configuration
        client_config = {
            'client_id': client_id,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'branding': asdict(branding),
            'features': asdict(features),
            'content': asdict(content),
            'status': 'active'
        }
        
        # Save configuration
        self._save_client_config(client_id, client_config)
        self.client_configs[client_id] = client_config
        
        logger.info(f"Created configuration for client: {client_id}")
        return client_config
    
    def update_client_config(self, client_id: str, 
                           updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing client configuration."""
        
        if client_id not in self.client_configs:
            raise ValueError(f"Client {client_id} not found")
        
        config = self.client_configs[client_id].copy()
        
        # Update configuration sections
        for section, data in updates.items():
            if section in ['branding', 'features', 'content']:
                if section in config:
                    config[section].update(data)
                else:
                    config[section] = data
            else:
                config[section] = data
        
        config['updated_at'] = datetime.now().isoformat()
        
        # Save updated configuration
        self._save_client_config(client_id, config)
        self.client_configs[client_id] = config
        
        logger.info(f"Updated configuration for client: {client_id}")
        return config
    
    def get_client_config(self, client_id: str) -> Dict[str, Any]:
        """Get configuration for a specific client."""
        if client_id in self.client_configs:
            return self.client_configs[client_id]
        
        # Return default configuration if client not found
        logger.warning(f"Client {client_id} not found, using default config")
        return self.create_client_config("default")
    
    def _save_client_config(self, client_id: str, config: Dict[str, Any]):
        """Save client configuration to disk."""
        config_file = self.config_dir / f"{client_id}.json"
        
        try:
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            logger.debug(f"Saved config for {client_id} to {config_file}")
        except Exception as e:
            logger.error(f"Error saving config for {client_id}: {e}")
    
    def generate_css_theme(self, client_id: str) -> str:
        """Generate CSS theme for a client."""
        config = self.get_client_config(client_id)
        branding = config.get('branding', {})
        
        css_template = """
/* FantasyPro White-label Theme for {client_name} */
:root {{
    --primary-color: {primary_color};
    --secondary-color: {secondary_color};
    --accent-color: {accent_color};
    --background-color: {background_color};
    --text-color: {text_color};
    --font-family: {font_family};
}}

body {{
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-color);
}}

.btn-primary {{
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}}

.btn-primary:hover {{
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}}

.navbar {{
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}}

.card {{
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}}

.accent {{
    color: var(--accent-color);
}}

.logo {{
    background-image: url('{logo_url}');
}}

{custom_css}
        """.format(
            client_name=branding.get('client_name', 'FantasyPro'),
            primary_color=branding.get('primary_color', '#667eea'),
            secondary_color=branding.get('secondary_color', '#764ba2'),
            accent_color=branding.get('accent_color', '#f093fb'),
            background_color=branding.get('background_color', '#f8fafc'),
            text_color=branding.get('text_color', '#1a202c'),
            font_family=branding.get('font_family', 'Inter, sans-serif'),
            logo_url=branding.get('logo_url', '/assets/logo.png'),
            custom_css=branding.get('custom_css', '')
        )
        
        return css_template.strip()
    
    def generate_config_js(self, client_id: str) -> str:
        """Generate JavaScript configuration for a client."""
        config = self.get_client_config(client_id)
        
        js_config = {
            'clientId': client_id,
            'branding': config.get('branding', {}),
            'features': config.get('features', {}),
            'content': config.get('content', {}),
            'apiLimits': config.get('features', {}).get('api_limits', {})
        }
        
        js_template = f"""
// FantasyPro White-label Configuration
window.FantasyProConfig = {json.dumps(js_config, indent=2)};

// Feature flags
window.FeatureFlags = {{
    isEnabled: function(feature) {{
        return window.FantasyProConfig.features.enabled_features.includes(feature);
    }},
    isDisabled: function(feature) {{
        return window.FantasyProConfig.features.disabled_features.includes(feature);
    }}
}};
        """
        
        return js_template.strip()
    
    def list_clients(self) -> List[Dict[str, Any]]:
        """List all configured clients."""
        clients = []
        
        for client_id, config in self.client_configs.items():
            clients.append({
                'client_id': client_id,
                'client_name': config.get('branding', {}).get('client_name', client_id),
                'status': config.get('status', 'unknown'),
                'subscription_tier': config.get('features', {}).get('subscription_tier', 'unknown'),
                'created_at': config.get('created_at'),
                'updated_at': config.get('updated_at')
            })
        
        return sorted(clients, key=lambda x: x['client_name'])
    
    def delete_client_config(self, client_id: str) -> bool:
        """Delete a client configuration."""
        if client_id not in self.client_configs:
            return False
        
        # Remove from memory
        del self.client_configs[client_id]
        
        # Remove from disk
        config_file = self.config_dir / f"{client_id}.json"
        try:
            if config_file.exists():
                config_file.unlink()
            logger.info(f"Deleted configuration for client: {client_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting config for {client_id}: {e}")
            return False

def create_sample_clients():
    """Create sample client configurations for demonstration."""
    manager = WhiteLabelManager()
    
    # Sample Client 1: Premium Sports Analytics
    premium_branding = BrandingConfig(
        client_id="premium_sports",
        client_name="Premium Sports Analytics",
        logo_url="/assets/premium_logo.png",
        favicon_url="/assets/premium_favicon.ico",
        primary_color="#1e40af",
        secondary_color="#3b82f6",
        accent_color="#60a5fa",
        background_color="#f8fafc",
        text_color="#1e293b",
        font_family="Roboto, sans-serif"
    )
    
    premium_features = FeatureConfig(
        client_id="premium_sports",
        enabled_features=[
            "player_analysis", "team_optimization", "trade_suggestions",
            "real_time_updates", "weather_analysis", "news_alerts",
            "performance_tracking", "league_management", "advanced_analytics",
            "custom_reports", "api_access"
        ],
        disabled_features=[],
        custom_features={
            "advanced_ml_models": True,
            "custom_dashboards": True,
            "white_label_api": True
        },
        api_limits={
            "requests_per_hour": 5000,
            "data_exports_per_day": 50,
            "ai_analyses_per_day": 500
        },
        subscription_tier="enterprise"
    )
    
    premium_content = ContentConfig(
        client_id="premium_sports",
        welcome_message="Welcome to Premium Sports Analytics - Professional fantasy sports intelligence!",
        help_text={
            "dashboard": "Your professional dashboard with advanced analytics",
            "players": "Deep player analysis with ML-powered insights",
            "teams": "Enterprise-grade team optimization tools"
        },
        custom_pages={
            "about": "Premium Sports Analytics provides professional-grade fantasy sports intelligence.",
            "enterprise": "Contact us for enterprise solutions and custom integrations."
        },
        terms_url="/premium/terms",
        privacy_url="/premium/privacy",
        support_email="<EMAIL>",
        social_links={
            "twitter": "https://twitter.com/premiumsports",
            "linkedin": "https://linkedin.com/company/premiumsports"
        }
    )
    
    manager.create_client_config("premium_sports", premium_branding, premium_features, premium_content)
    
    # Sample Client 2: Fantasy League Manager
    league_branding = BrandingConfig(
        client_id="fantasy_league",
        client_name="Fantasy League Manager",
        logo_url="/assets/league_logo.png",
        favicon_url="/assets/league_favicon.ico",
        primary_color="#059669",
        secondary_color="#10b981",
        accent_color="#34d399",
        background_color="#f0fdf4",
        text_color="#064e3b",
        font_family="Open Sans, sans-serif"
    )
    
    league_features = FeatureConfig(
        client_id="fantasy_league",
        enabled_features=[
            "player_analysis", "team_optimization", "league_management",
            "performance_tracking", "basic_analytics"
        ],
        disabled_features=["advanced_analytics", "api_access"],
        custom_features={
            "league_chat": True,
            "draft_assistant": True,
            "social_features": True
        },
        api_limits={
            "requests_per_hour": 1000,
            "data_exports_per_day": 5,
            "ai_analyses_per_day": 50
        },
        subscription_tier="standard"
    )
    
    manager.create_client_config("fantasy_league", league_branding, league_features)
    
    return manager

def main():
    """Demo function to test the white-label system."""
    print("🎨 Testing FantasyPro White-label Customization System...")
    print("=" * 60)
    
    # Create sample clients
    manager = create_sample_clients()
    
    # List all clients
    print("👥 Configured Clients:")
    clients = manager.list_clients()
    for client in clients:
        print(f"   • {client['client_name']} ({client['client_id']})")
        print(f"     Tier: {client['subscription_tier']} | Status: {client['status']}")
    
    # Generate theme for a client
    print("\n🎨 Generated CSS Theme for Premium Sports:")
    css_theme = manager.generate_css_theme("premium_sports")
    print(css_theme[:300] + "..." if len(css_theme) > 300 else css_theme)
    
    # Generate JS config
    print("\n⚙️  Generated JS Config for Fantasy League:")
    js_config = manager.generate_config_js("fantasy_league")
    print(js_config[:300] + "..." if len(js_config) > 300 else js_config)
    
    print("\n✅ White-label customization system test completed!")

if __name__ == "__main__":
    main()
