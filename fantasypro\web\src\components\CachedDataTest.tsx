import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface Player {
  id: string;
  name: string;
  position: string;
  team: string;
  price: number;
  points: number;
  average: number;
  form: number;
  ownership?: number;
  breakeven?: number;
  games_played?: number;
  source: string;
  last_updated: string;
}

interface CacheStats {
  total_players: number;
  teams: string[];
  positions: string[];
  last_updated: string;
  cache_age_minutes: number;
}

const CachedDataTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [players, setPlayers] = useState<Player[]>([]);
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');

  const testCachedData = async (filterType: string = 'all') => {
    setLoading(true);
    setError(null);

    try {
      console.log(`🧪 Testing cached data with filter: ${filterType}`);
      
      let url = '/api/players';
      
      switch (filterType) {
        case 'stats':
          url += '?stats=true';
          break;
        case 'top':
          url += '?top=true&limit=10';
          break;
        case 'broncos':
          url += '?team=broncos';
          break;
        case 'flb':
          url += '?position=FLB';
          break;
        case 'search':
          url += '?search=walsh';
          break;
        default:
          url += '?limit=20';
      }
      
      const response = await fetch(url);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Cached Data Success!', data);
        
        if (data.success) {
          setPlayers(data.data.players || []);
          
          if (data.data.cache_info) {
            setCacheStats({
              total_players: data.data.total,
              teams: data.data.filters?.teams || [],
              positions: data.data.filters?.positions || [],
              last_updated: data.data.cache_info.last_updated,
              cache_age_minutes: data.data.cache_info.cache_age_minutes
            });
          }
        } else {
          setError(data.error || 'API returned error');
        }
      } else {
        const errorData = await response.json();
        console.error('❌ Cached Data Error:', errorData);
        setError(errorData.error || 'Request failed');
      }
      
    } catch (err) {
      console.error('💥 Request Error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testCachedData('stats'); // Load stats on mount
  }, []);

  const formatCurrency = (amount: number) => {
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  const formatPosition = (pos: string) => {
    const positionMap: { [key: string]: string } = {
      'FLB': 'Fullback',
      'CTW': 'Centre/Wing',
      'HFB': 'Halfback',
      '5/8': 'Five-Eighth',
      'HOK': 'Hooker',
      'FRF': 'Front Row',
      '2RF': 'Second Row',
      'LCK': 'Lock'
    };
    return positionMap[pos] || pos;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card-premium p-6"
    >
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold themed-text-primary">Cached NRL Data Test</h2>
          <p className="text-sm themed-text-tertiary">Testing integration with cached player data</p>
        </div>
        
        <div className="flex space-x-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-2 bg-slate-700 border border-slate-600 rounded text-sm themed-text-primary"
          >
            <option value="all">All Players (20)</option>
            <option value="stats">Cache Stats</option>
            <option value="top">Top 10 Players</option>
            <option value="broncos">Broncos Players</option>
            <option value="flb">Fullbacks</option>
            <option value="search">Search "Walsh"</option>
          </select>
          
          <button
            onClick={() => testCachedData(filter)}
            disabled={loading}
            className="btn-primary btn-ripple"
          >
            {loading ? 'Testing...' : 'Test Data'}
          </button>
        </div>
      </div>

      {/* Cache Statistics */}
      {cacheStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center p-4 bg-blue-500/10 rounded-lg">
            <div className="text-2xl font-bold text-blue-400">{cacheStats.total_players}</div>
            <div className="text-sm themed-text-secondary">Total Players</div>
          </div>
          
          <div className="text-center p-4 bg-green-500/10 rounded-lg">
            <div className="text-2xl font-bold text-green-400">{cacheStats.teams.length}</div>
            <div className="text-sm themed-text-secondary">Teams</div>
          </div>
          
          <div className="text-center p-4 bg-purple-500/10 rounded-lg">
            <div className="text-2xl font-bold text-purple-400">{cacheStats.positions.length}</div>
            <div className="text-sm themed-text-secondary">Positions</div>
          </div>
          
          <div className="text-center p-4 bg-yellow-500/10 rounded-lg">
            <div className="text-2xl font-bold text-yellow-400">{cacheStats.cache_age_minutes}</div>
            <div className="text-sm themed-text-secondary">Cache Age (min)</div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="loading-spinner w-8 h-8"></div>
          <span className="ml-3 themed-text-secondary">Loading cached data...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg mb-6">
          <h3 className="font-semibold text-red-400 mb-2">❌ Error Loading Data</h3>
          <p className="text-sm text-red-300">{error}</p>
        </div>
      )}

      {/* Players Data */}
      {players.length > 0 && (
        <div className="space-y-4">
          <h3 className="font-semibold themed-text-primary">
            Players ({players.length})
          </h3>
          
          <div className="grid gap-3 max-h-96 overflow-y-auto">
            {players.map((player, index) => (
              <div key={player.id} className="flex items-center justify-between p-3 bg-slate-700 rounded">
                <div className="flex-1">
                  <div className="font-medium themed-text-primary">{player.name}</div>
                  <div className="text-sm themed-text-tertiary">
                    {player.team} • {formatPosition(player.position)}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-sm font-medium themed-text-primary">
                    {player.price > 0 ? formatCurrency(player.price) : 'N/A'}
                  </div>
                  <div className="text-xs themed-text-tertiary">
                    Avg: {player.average.toFixed(1)} • BE: {player.breakeven || 'N/A'}
                  </div>
                </div>
                
                <div className="ml-4 text-right">
                  <div className="text-xs themed-text-tertiary">
                    {player.games_played} games
                  </div>
                  <div className="text-xs themed-text-tertiary">
                    {player.points} pts
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Teams and Positions Info */}
      {cacheStats && (
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold themed-text-primary mb-3">Teams ({cacheStats.teams.length})</h4>
            <div className="text-xs themed-text-tertiary space-y-1 max-h-32 overflow-y-auto">
              {cacheStats.teams.map(team => (
                <div key={team}>{team}</div>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold themed-text-primary mb-3">Positions ({cacheStats.positions.length})</h4>
            <div className="text-xs themed-text-tertiary space-y-1">
              {cacheStats.positions.map(pos => (
                <div key={pos}>{pos} - {formatPosition(pos)}</div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {players.length > 0 && !loading && !error && (
        <div className="mt-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
          <h4 className="font-semibold text-green-400 mb-2">🎉 Cached Data Integration Working!</h4>
          <ul className="text-sm text-green-300 space-y-1">
            <li>✅ Successfully loaded {players.length} players from cache</li>
            <li>✅ Data includes prices, averages, break-evens, and team info</li>
            <li>✅ Ready to replace mock data in FantasyPro components</li>
            <li>✅ API endpoints working correctly</li>
          </ul>
        </div>
      )}
    </motion.div>
  );
};

export default CachedDataTest;
