#!/usr/bin/env python3
"""
Quick test to check website access and structure
"""

import asyncio
import aiohttp
from bs4 import BeautifulSoup

async def test_website_access():
    """Test basic website access"""
    url = "https://www.nrlsupercoachstats.com"
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"🔍 Testing access to {url}")
            
            async with session.get(url) as response:
                print(f"📡 Response status: {response.status}")
                
                if response.status == 200:
                    content = await response.text()
                    print(f"📄 Content length: {len(content)} characters")
                    
                    # Parse with BeautifulSoup
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    # Check for common elements
                    title = soup.find('title')
                    if title:
                        print(f"📝 Page title: {title.get_text()}")
                    
                    # Look for team-related content
                    team_links = soup.find_all('a', href=True)
                    team_count = 0
                    for link in team_links[:10]:  # Check first 10 links
                        if any(team in link.get('href', '').lower() for team in ['team', 'bris', 'melb', 'penr']):
                            team_count += 1
                            print(f"🏈 Found team link: {link.get('href')}")
                    
                    print(f"🏈 Found {team_count} potential team links")
                    
                    # Check for specific pages
                    test_pages = [
                        "/TeamBEs.php",
                        "/TeamPrices.php", 
                        "/stats.php",
                        "/drawV2.php"
                    ]
                    
                    for page in test_pages:
                        test_url = url + page
                        try:
                            async with session.get(test_url) as page_response:
                                print(f"📊 {page}: Status {page_response.status}")
                                if page_response.status == 200:
                                    page_content = await page_response.text()
                                    page_soup = BeautifulSoup(page_content, 'html.parser')
                                    
                                    # Look for tables or data
                                    tables = page_soup.find_all('table')
                                    print(f"   📋 Found {len(tables)} tables")
                                    
                                    # Look for team images
                                    team_images = page_soup.find_all('img', src=True)
                                    team_img_count = 0
                                    for img in team_images:
                                        if 'team' in img.get('src', '').lower():
                                            team_img_count += 1
                                    print(f"   🖼️ Found {team_img_count} team images")
                                    
                        except Exception as e:
                            print(f"❌ Error accessing {page}: {e}")
                        
                        await asyncio.sleep(1)  # Be respectful
                    
                else:
                    print(f"❌ Failed to access website: HTTP {response.status}")
                    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_website_access())
