#!/usr/bin/env python3
"""
Test dashboard endpoint directly
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.supercoach_db import supercoach_db

def test_dashboard_data():
    """Test the dashboard data retrieval"""
    print("🔍 Testing dashboard data retrieval...")
    
    try:
        # Test basic database operations
        print("  📊 Testing get_all_players...")
        all_players = supercoach_db.get_all_players()
        print(f"    ✅ Found {len(all_players)} players")
        
        if all_players:
            print(f"    🏈 Sample player: {all_players[0].name}")
            print(f"    📊 Active status: {all_players[0].is_active}")
        
        # Test active players filter
        print("  📊 Testing active players filter...")
        active_players = [p for p in all_players if p.is_active]
        print(f"    ✅ Found {len(active_players)} active players")
        
        # Test top players
        print("  🏆 Testing get_top_players_by_metric...")
        try:
            top_scorers = supercoach_db.get_top_players_by_metric('points', limit=5)
            print(f"    ✅ Found {len(top_scorers)} top scorers")
        except Exception as e:
            print(f"    ❌ Error getting top scorers: {e}")
        
        # Test price risers/fallers
        print("  💰 Testing get_price_risers_fallers...")
        try:
            price_risers, price_fallers = supercoach_db.get_price_risers_fallers(limit=5)
            print(f"    ✅ Found {len(price_risers)} price risers, {len(price_fallers)} price fallers")
        except Exception as e:
            print(f"    ❌ Error getting price changes: {e}")
        
        print("🎉 Dashboard data test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing dashboard data: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_dashboard_data()
