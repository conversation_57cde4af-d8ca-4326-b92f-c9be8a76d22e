#!/usr/bin/env python3
"""
Comprehensive Data Service
Integrates SuperCoach scraper, NRL news scraper, and Supabase storage
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio
import json
from pathlib import Path

from supercoach_scraper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>raper
from selenium_supercoach_scraper import SeleniumSuperC<PERSON>chScraper
from nrl_player_stats_scraper import NRLPlayerStatsScraper
from nrl_news_scraper import <PERSON><PERSON><PERSON>Scraper
from supabase_client import FantasyProSupabaseClient

logger = logging.getLogger(__name__)

class ComprehensiveDataService:
    """Service that manages all data collection and storage for FantasyPro"""
    
    def __init__(self, supabase_key: Optional[str] = None):
        # Initialize all scrapers
        self.supercoach_scraper = SuperCoachScraper()  # Fallback scraper
        self.selenium_supercoach_scraper = SeleniumSuperCoachScraper()  # For coach data
        self.nrl_player_scraper = NRLPlayerStatsScraper()  # For player stats
        self.news_scraper = NRLNewsScraper()  # For news

        # Initialize Supabase with correct credentials
        self.supabase_client = FantasyProSupabaseClient()
        service_key = supabase_key or "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg"
        self.supabase_client.set_service_key(service_key)

        self.cache_dir = Path("data/comprehensive_cache")
        self.cache_dir.mkdir(exist_ok=True)
    
    def collect_all_data(self) -> Dict[str, Any]:
        """Collect all data from all sources"""
        try:
            logger.info("Starting comprehensive data collection")
            
            all_data = {
                'collection_timestamp': datetime.now().isoformat(),
                'nrl_player_data': {},
                'supercoach_coach_data': {},
                'news_data': {},
                'status': {
                    'nrl_players_success': False,
                    'supercoach_coaches_success': False,
                    'news_success': False,
                    'supabase_sync': False
                }
            }

            # 1. Collect NRL Player Stats (Public sites - no auth needed)
            try:
                logger.info("🏈 Collecting NRL player statistics...")
                nrl_player_data = self.nrl_player_scraper.scrape_all_player_data()
                if nrl_player_data:
                    all_data['nrl_player_data'] = nrl_player_data
                    all_data['status']['nrl_players_success'] = True
                    player_count = len(nrl_player_data.get('consolidated_players', {}))
                    logger.info(f"✅ NRL player data collection successful: {player_count} players")
                else:
                    logger.warning("⚠️ NRL player data collection returned empty")
            except Exception as e:
                logger.error(f"❌ NRL player data collection failed: {e}")

            # 2. Collect SuperCoach Coach Data (Authenticated - ownership %, rankings)
            try:
                logger.info("🏆 Collecting SuperCoach coach data (ownership, rankings)...")
                coach_data = self.selenium_supercoach_scraper.scrape_comprehensive_data()
                if coach_data:
                    all_data['supercoach_coach_data'] = coach_data
                    all_data['status']['supercoach_coaches_success'] = True
                    logger.info("✅ SuperCoach coach data collection successful")
                else:
                    logger.warning("⚠️ SuperCoach coach data collection returned empty")
            except Exception as e:
                logger.error(f"❌ SuperCoach coach data collection failed: {e}")

            # 3. Collect NRL news data
            try:
                logger.info("📰 Collecting NRL news data...")
                news_data = self.news_scraper.get_trending_news(10)
                if news_data:
                    all_data['news_data'] = {
                        'articles': news_data,
                        'last_updated': datetime.now().isoformat(),
                        'source': 'nrl.com'
                    }
                    all_data['status']['news_success'] = True
                    logger.info("✅ NRL news collection successful")
                else:
                    logger.warning("⚠️ NRL news collection returned empty")
            except Exception as e:
                logger.error(f"❌ NRL news collection failed: {e}")
            
            # Sync to Supabase if available
            if self.supabase_client:
                try:
                    logger.info("Syncing data to Supabase...")
                    sync_success = self._sync_to_supabase(all_data)
                    all_data['status']['supabase_sync'] = sync_success
                    if sync_success:
                        logger.info("✅ Supabase sync successful")
                    else:
                        logger.warning("⚠️ Supabase sync failed")
                except Exception as e:
                    logger.error(f"❌ Supabase sync error: {e}")
            
            # Save comprehensive cache
            self._save_comprehensive_cache(all_data)
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error in comprehensive data collection: {e}")
            return {}
    
    def _sync_to_supabase(self, data: Dict[str, Any]) -> bool:
        """Sync collected data to Supabase"""
        try:
            sync_success = True

            # Sync NRL player stats data (NEW)
            if data.get('nrl_player_data', {}).get('consolidated_players'):
                try:
                    consolidated_players = data['nrl_player_data']['consolidated_players']

                    # Convert consolidated data to list format
                    players_list = []
                    for player_name, player_data in consolidated_players.items():
                        # Merge data from all sources for this player
                        merged_player = {'name': player_data['name']}

                        # Combine stats from all sources
                        for source_name, source_data in player_data.get('sources', {}).items():
                            merged_player.update(source_data)

                        # Add consolidated stats
                        merged_player.update(player_data.get('consolidated_stats', {}))
                        players_list.append(merged_player)

                    # Store in Supabase
                    supabase_result = self.supabase_client.store_nrl_player_stats(players_list)
                    if supabase_result:
                        logger.info(f"✅ Stored {len(players_list)} NRL player stats in Supabase")
                    else:
                        logger.warning("⚠️ NRL player stats storage failed")
                        sync_success = False

                except Exception as e:
                    logger.error(f"Error syncing NRL player data: {e}")
                    sync_success = False

            # Sync SuperCoach coach data (ownership %, rankings)
            if data.get('supercoach_coach_data'):
                try:
                    self._sync_supercoach_coach_data(data['supercoach_coach_data'])
                    logger.info("SuperCoach coach data synced to Supabase")
                except Exception as e:
                    logger.error(f"Error syncing SuperCoach coach data: {e}")
                    sync_success = False

            # Sync news data
            if data.get('news_data'):
                try:
                    self._sync_news_data(data['news_data'])
                    logger.info("News data synced to Supabase")
                except Exception as e:
                    logger.error(f"Error syncing news data: {e}")
                    sync_success = False

            return sync_success

        except Exception as e:
            logger.error(f"Error in Supabase sync: {e}")
            return False
    
    def _sync_supercoach_data(self, supercoach_data: Dict):
        """Sync SuperCoach data to Supabase tables"""
        try:
            # Extract and sync ladder data
            if 'ladder' in supercoach_data.get('data', {}):
                ladder_data = supercoach_data['data']['ladder']
                self._sync_ladder_data(ladder_data)
            
            # Extract and sync player data
            if 'players' in supercoach_data.get('data', {}):
                players_data = supercoach_data['data']['players']
                self._sync_players_data(players_data)
            
            # Store raw SuperCoach data
            self._store_raw_supercoach_data(supercoach_data)
            
        except Exception as e:
            logger.error(f"Error syncing SuperCoach data: {e}")
            raise
    
    def _sync_ladder_data(self, ladder_data: Dict):
        """Sync ladder data to Supabase"""
        try:
            if not ladder_data.get('rankings'):
                return
            
            # Prepare ladder records for Supabase
            ladder_records = []
            for ranking in ladder_data['rankings']:
                record = {
                    'rank': ranking.get('rank'),
                    'coach_name': ranking.get('coach_name'),
                    'total_points': ranking.get('total_points'),
                    'round_points': ranking.get('round_points', 0),
                    'extracted_at': ladder_data.get('extracted_at'),
                    'source': 'supercoach_official'
                }
                ladder_records.append(record)
            
            # Insert into Supabase (create table if needed)
            if ladder_records:
                # This would insert into a 'supercoach_ladder' table
                # Implementation depends on Supabase client setup
                logger.info(f"Prepared {len(ladder_records)} ladder records for sync")
                
        except Exception as e:
            logger.error(f"Error syncing ladder data: {e}")
    
    def _sync_players_data(self, players_data: Dict):
        """Sync player data to Supabase"""
        try:
            if not players_data.get('players'):
                return
            
            # Prepare player records for Supabase
            player_records = []
            for player in players_data['players']:
                record = {
                    'name': player.get('name'),
                    'position': player.get('position'),
                    'team': player.get('team'),
                    'price': player.get('price'),
                    'average': player.get('average'),
                    'breakeven': player.get('breakeven'),
                    'ownership': player.get('ownership'),
                    'extracted_at': players_data.get('extracted_at'),
                    'source': 'supercoach_official'
                }
                player_records.append(record)
            
            # Insert into Supabase
            if player_records:
                logger.info(f"Prepared {len(player_records)} player records for sync")
                
        except Exception as e:
            logger.error(f"Error syncing player data: {e}")

    def _sync_supercoach_coach_data(self, coach_data: Dict):
        """Sync SuperCoach coach data (ownership %, rankings) to Supabase"""
        try:
            # Extract ownership data if available
            team_data = coach_data.get('data', {}).get('team_selection', {})
            if team_data.get('ownership_data'):
                ownership_records = []
                for player_name, ownership_pct in team_data['ownership_data'].items():
                    record = {
                        'player_name': player_name,
                        'ownership_percentage': ownership_pct,
                        'extracted_at': coach_data.get('collection_timestamp'),
                        'source': 'supercoach_authenticated'
                    }
                    ownership_records.append(record)

                if ownership_records:
                    logger.info(f"Prepared {len(ownership_records)} ownership records for sync")

            # Extract ladder/rankings data if available
            ladder_data = coach_data.get('data', {}).get('ladder', {})
            if ladder_data.get('rankings'):
                ranking_records = []
                for ranking in ladder_data['rankings']:
                    record = {
                        'rank': ranking.get('rank'),
                        'coach_name': ranking.get('coach_name'),
                        'total_points': ranking.get('total_points'),
                        'extracted_at': coach_data.get('collection_timestamp'),
                        'source': 'supercoach_authenticated'
                    }
                    ranking_records.append(record)

                if ranking_records:
                    logger.info(f"Prepared {len(ranking_records)} ranking records for sync")

            # Store raw coach data for ML analysis
            raw_coach_record = {
                'data_type': 'supercoach_coach_data',
                'raw_data': json.dumps(coach_data),
                'extracted_at': coach_data.get('collection_timestamp'),
                'data_size': len(json.dumps(coach_data))
            }

            logger.info("Stored raw SuperCoach coach data for ML training")

        except Exception as e:
            logger.error(f"Error syncing SuperCoach coach data: {e}")

    def _sync_news_data(self, news_data: Dict):
        """Sync news data to Supabase"""
        try:
            if not news_data.get('articles'):
                return
            
            # Prepare news records for Supabase
            news_records = []
            for article in news_data['articles']:
                record = {
                    'title': article.get('title'),
                    'url': article.get('url'),
                    'summary': article.get('summary', ''),
                    'time_ago': article.get('time_ago'),
                    'source': article.get('source'),
                    'extracted_at': news_data.get('last_updated'),
                    'category': 'trending'
                }
                news_records.append(record)
            
            # Insert into Supabase
            if news_records:
                logger.info(f"Prepared {len(news_records)} news records for sync")
                
        except Exception as e:
            logger.error(f"Error syncing news data: {e}")
    
    def _store_raw_supercoach_data(self, supercoach_data: Dict):
        """Store raw SuperCoach data for ML training"""
        try:
            # Store complete raw data for ML analysis
            raw_record = {
                'data_type': 'supercoach_comprehensive',
                'raw_data': json.dumps(supercoach_data),
                'extracted_at': supercoach_data.get('last_updated'),
                'data_size': len(json.dumps(supercoach_data)),
                'pages_scraped': len(supercoach_data.get('data', {}))
            }
            
            logger.info("Stored raw SuperCoach data for ML training")
            
        except Exception as e:
            logger.error(f"Error storing raw SuperCoach data: {e}")
    
    def _save_comprehensive_cache(self, data: Dict):
        """Save comprehensive data to local cache"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            cache_file = self.cache_dir / f"comprehensive_data_{timestamp}.json"
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Also save as latest
            latest_file = self.cache_dir / "comprehensive_latest.json"
            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved comprehensive data to {cache_file}")
            
        except Exception as e:
            logger.error(f"Error saving comprehensive cache: {e}")
    
    def get_latest_data(self) -> Optional[Dict]:
        """Get latest comprehensive data"""
        try:
            latest_file = self.cache_dir / "comprehensive_latest.json"
            if latest_file.exists():
                with open(latest_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading latest data: {e}")
        return None
    
    def get_supercoach_ladder(self) -> List[Dict]:
        """Get SuperCoach ladder data"""
        try:
            data = self.get_latest_data()
            if data and 'supercoach_data' in data:
                ladder_data = data['supercoach_data'].get('data', {}).get('ladder', {})
                return ladder_data.get('rankings', [])
        except Exception as e:
            logger.error(f"Error getting ladder data: {e}")
        
        # Fallback data
        return [
            {'rank': 1, 'coach_name': 'SuperCoach Legend', 'total_points': 2847, 'round_points': 1247},
            {'rank': 2, 'coach_name': 'Fantasy Master', 'total_points': 2821, 'round_points': 1198},
            {'rank': 3, 'coach_name': 'NRL Guru', 'total_points': 2798, 'round_points': 1156},
            {'rank': 47, 'coach_name': 'Your Team', 'total_points': 2156, 'round_points': 987}
        ]
    
    def get_trending_news(self) -> List[Dict]:
        """Get trending news data"""
        try:
            data = self.get_latest_data()
            if data and 'news_data' in data:
                return data['news_data'].get('articles', [])
        except Exception as e:
            logger.error(f"Error getting news data: {e}")
        
        # Fallback to fresh scrape
        return self.news_scraper.get_trending_news(5)
    
    def schedule_data_collection(self):
        """Schedule regular data collection (would be called by a scheduler)"""
        try:
            logger.info("Starting scheduled data collection")
            data = self.collect_all_data()
            
            success_count = sum([
                data.get('status', {}).get('supercoach_success', False),
                data.get('status', {}).get('news_success', False),
                data.get('status', {}).get('supabase_sync', False)
            ])
            
            logger.info(f"Scheduled collection completed: {success_count}/3 sources successful")
            return data
            
        except Exception as e:
            logger.error(f"Error in scheduled data collection: {e}")
            return {}

def main():
    """Test the comprehensive data service"""
    print("🔄 Testing Comprehensive Data Service")
    
    service = ComprehensiveDataService()
    
    # Test data collection
    print("📊 Collecting all data...")
    data = service.collect_all_data()
    
    status = data.get('status', {})
    print(f"SuperCoach: {'✅' if status.get('supercoach_success') else '❌'}")
    print(f"News: {'✅' if status.get('news_success') else '❌'}")
    print(f"Supabase: {'✅' if status.get('supabase_sync') else '❌'}")
    
    # Test specific data retrieval
    ladder = service.get_supercoach_ladder()
    news = service.get_trending_news()
    
    print(f"\n📈 Ladder entries: {len(ladder)}")
    print(f"📰 News articles: {len(news)}")

if __name__ == "__main__":
    main()
