#!/usr/bin/env python3
"""
Supabase Client for FantasyPro
Manages NRL player and team data in Supabase cloud database
"""

import os
import json
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from sportradar_client import SportRadarNRLClient

logger = logging.getLogger(__name__)

class FantasyProSupabaseClient:
    def __init__(self):
        # Supabase project details
        self.project_id = "fuxpdgsixnbbsdspusmp"
        self.supabase_url = f"https://{self.project_id}.supabase.co"
        
        # You'll need to get these from your Supabase project settings
        # For now, we'll use the REST API with service key
        self.headers = {
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
        
        # Note: In production, you should use environment variables for keys
        # self.service_key = os.getenv('SUPABASE_SERVICE_KEY')
        
    def set_service_key(self, service_key: str):
        """Set the Supabase service key for authentication"""
        self.headers['Authorization'] = f'Bearer {service_key}'
        self.headers['apikey'] = service_key

    def _make_request(self, method: str, endpoint: str, data: Dict = None) -> Optional[Dict]:
        """Make a request to Supabase REST API"""
        url = f"{self.supabase_url}/rest/v1/{endpoint}"
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=self.headers)
            elif method == 'POST':
                response = requests.post(url, headers=self.headers, json=data)
            elif method == 'PUT':
                response = requests.put(url, headers=self.headers, json=data)
            elif method == 'DELETE':
                response = requests.delete(url, headers=self.headers)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response.raise_for_status()
            return response.json() if response.content else {}
            
        except Exception as e:
            logger.error(f"Supabase request failed: {e}")
            return None

    def is_cache_fresh(self, max_age_hours: int = 24) -> bool:
        """Check if cached data is still fresh"""
        try:
            result = self._make_request('GET', 'cache_metadata?key=eq.last_full_update')
            
            if not result or len(result) == 0:
                return False
            
            last_update_str = result[0]['value']
            last_update = datetime.fromisoformat(last_update_str.replace('Z', '+00:00'))
            age = datetime.now() - last_update.replace(tzinfo=None)
            
            return age < timedelta(hours=max_age_hours)
        except Exception as e:
            logger.error(f"Error checking cache freshness: {e}")
            return False

    def update_from_sportradar(self, api_key: str) -> Dict:
        """Update Supabase with fresh data from SportRadar"""
        logger.info("Updating Supabase from SportRadar...")

        try:
            client = SportRadarNRLClient(api_key)

            # Get all teams, players, and injuries
            all_players = client.get_all_players()
            teams = client.get_team_list()
            injuries = client.get_injury_reports()

            # Store in Supabase
            teams_result = self.store_teams(teams)
            players_result = self.store_players(all_players)
            injuries_result = self.store_injuries(injuries)

            # Update metadata
            self.update_cache_metadata('last_full_update', datetime.now().isoformat())
            self.update_cache_metadata('total_players', str(len(all_players)))
            self.update_cache_metadata('total_teams', str(len(teams)))
            self.update_cache_metadata('total_injuries', str(len(injuries)))

            result = {
                'status': 'success',
                'players_count': len(all_players),
                'teams_count': len(teams),
                'injuries_count': len(injuries),
                'last_updated': datetime.now().isoformat(),
                'teams_stored': teams_result is not None,
                'players_stored': players_result is not None,
                'injuries_stored': injuries_result is not None
            }

            logger.info(f"Supabase updated: {len(all_players)} players, {len(teams)} teams, {len(injuries)} injuries")
            return result

        except Exception as e:
            logger.error(f"Error updating Supabase from SportRadar: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'last_updated': datetime.now().isoformat()
            }

    def store_teams(self, teams: List[Dict]) -> Optional[Dict]:
        """Store teams in Supabase"""
        try:
            # Clear existing teams
            self._make_request('DELETE', 'nrl_teams')
            
            # Insert new teams
            teams_data = []
            for team in teams:
                teams_data.append({
                    'id': team.get('id'),
                    'name': team.get('name'),
                    'abbreviation': team.get('abbreviation'),
                    'city': team.get('city'),
                    'venue': team.get('venue'),
                    'sportradar_id': team.get('id')
                })
            
            if teams_data:
                result = self._make_request('POST', 'nrl_teams', teams_data)
                logger.info(f"Stored {len(teams_data)} teams in Supabase")
                return result
            
        except Exception as e:
            logger.error(f"Error storing teams: {e}")
            return None

    def store_players(self, players: List[Dict]) -> Optional[Dict]:
        """Store players in Supabase"""
        try:
            # Clear existing players
            self._make_request('DELETE', 'nrl_players')
            
            # Insert new players in batches (Supabase has limits)
            batch_size = 100
            total_stored = 0
            
            for i in range(0, len(players), batch_size):
                batch = players[i:i + batch_size]
                players_data = []
                
                for player in batch:
                    # Convert date_of_birth to proper format
                    dob = player.get('date_of_birth')
                    if dob and isinstance(dob, str):
                        try:
                            # Ensure it's in YYYY-MM-DD format
                            datetime.strptime(dob, '%Y-%m-%d')
                        except:
                            dob = None
                    
                    players_data.append({
                        'sportradar_id': player.get('sportradar_id'),
                        'name': player.get('name'),
                        'team_id': player.get('team_id'),
                        'team_name': player.get('team'),
                        'position': player.get('position'),
                        'jersey_number': player.get('jersey_number'),
                        'height': player.get('height'),
                        'weight': player.get('weight'),
                        'age': player.get('age'),
                        'date_of_birth': dob,
                        'statistics': player.get('statistics', {})
                    })
                
                if players_data:
                    result = self._make_request('POST', 'nrl_players', players_data)
                    if result is not None:
                        total_stored += len(players_data)
                        logger.info(f"Stored batch {i//batch_size + 1}: {len(players_data)} players")
            
            logger.info(f"Total players stored in Supabase: {total_stored}")
            return {'total_stored': total_stored}
            
        except Exception as e:
            logger.error(f"Error storing players: {e}")
            return None

    def store_injuries(self, injuries: List[Dict]) -> Optional[Dict]:
        """Store injury reports in Supabase"""
        try:
            # Clear existing injuries (keep only recent ones)
            cutoff_date = (datetime.now() - timedelta(days=30)).isoformat()
            self._make_request('DELETE', f'injury_reports?date_reported=lt.{cutoff_date}')

            if not injuries:
                return {'total_stored': 0}

            # Insert new injuries
            injuries_data = []
            for injury in injuries:
                injuries_data.append({
                    'player_id': injury.get('player_id'),
                    'player_name': injury.get('player_name'),
                    'team': injury.get('team'),
                    'injury_type': injury.get('injury_type'),
                    'injury_status': injury.get('injury_status'),
                    'expected_return': injury.get('expected_return'),
                    'severity': injury.get('severity'),
                    'date_reported': injury.get('date_reported'),
                    'description': injury.get('description'),
                    'source': injury.get('source', 'sportradar')
                })

            if injuries_data:
                result = self._make_request('POST', 'injury_reports', injuries_data)
                logger.info(f"Stored {len(injuries_data)} injury reports in Supabase")
                return {'total_stored': len(injuries_data)}

            return {'total_stored': 0}

        except Exception as e:
            logger.error(f"Error storing injuries: {e}")
            return None

    def get_all_injuries(self, limit: int = 100) -> List[Dict]:
        """Get all injury reports from Supabase"""
        try:
            result = self._make_request('GET', f'injury_reports?limit={limit}&order=date_reported.desc')

            if result:
                injuries = []
                for row in result:
                    injuries.append({
                        'id': row['id'],
                        'player_id': row['player_id'],
                        'player_name': row['player_name'],
                        'team': row['team'],
                        'injury_type': row['injury_type'],
                        'injury_status': row['injury_status'],
                        'expected_return': row['expected_return'],
                        'severity': row['severity'],
                        'date_reported': row['date_reported'],
                        'description': row['description'],
                        'source': row['source']
                    })
                return injuries

            return []
        except Exception as e:
            logger.error(f"Error getting injuries from Supabase: {e}")
            return []

    def get_all_players(self, limit: int = 1000) -> List[Dict]:
        """Get all players from Supabase"""
        try:
            result = self._make_request('GET', f'nrl_players?limit={limit}&order=name')
            
            if result:
                players = []
                for row in result:
                    players.append({
                        'id': row['id'],
                        'sportradar_id': row['sportradar_id'],
                        'name': row['name'],
                        'team_id': row['team_id'],
                        'team': row['team_name'],
                        'position': row['position'],
                        'jersey_number': row['jersey_number'],
                        'height': row['height'],
                        'weight': row['weight'],
                        'age': row['age'],
                        'date_of_birth': row['date_of_birth'],
                        'statistics': row.get('statistics', {}),
                        'source': 'supabase'
                    })
                return players
            
            return []
        except Exception as e:
            logger.error(f"Error getting players from Supabase: {e}")
            return []

    def get_all_teams(self) -> List[Dict]:
        """Get all teams from Supabase"""
        try:
            result = self._make_request('GET', 'nrl_teams?order=name')
            
            if result:
                teams = []
                for row in result:
                    teams.append({
                        'id': row['id'],
                        'name': row['name'],
                        'abbreviation': row['abbreviation'],
                        'city': row['city'],
                        'venue': row['venue'],
                        'sportradar_id': row['sportradar_id'],
                        'source': 'supabase'
                    })
                return teams
            
            return []
        except Exception as e:
            logger.error(f"Error getting teams from Supabase: {e}")
            return []

    def search_players(self, query: str, limit: int = 20) -> List[Dict]:
        """Search players by name or team"""
        try:
            # Use Supabase text search
            encoded_query = query.replace(' ', '%20')
            result = self._make_request('GET', 
                f'nrl_players?or=(name.ilike.%25{encoded_query}%25,team_name.ilike.%25{encoded_query}%25)&limit={limit}&order=name')
            
            if result:
                players = []
                for row in result:
                    players.append({
                        'id': row['id'],
                        'sportradar_id': row['sportradar_id'],
                        'name': row['name'],
                        'team_id': row['team_id'],
                        'team': row['team_name'],
                        'position': row['position'],
                        'jersey_number': row['jersey_number'],
                        'height': row['height'],
                        'weight': row['weight'],
                        'age': row['age'],
                        'date_of_birth': row['date_of_birth'],
                        'source': 'supabase'
                    })
                return players
            
            return []
        except Exception as e:
            logger.error(f"Error searching players: {e}")
            return []

    def get_team_players(self, team_id: str) -> List[Dict]:
        """Get players from a specific team"""
        try:
            result = self._make_request('GET', f'nrl_players?team_id=eq.{team_id}&order=jersey_number,name')
            
            if result:
                players = []
                for row in result:
                    players.append({
                        'id': row['id'],
                        'sportradar_id': row['sportradar_id'],
                        'name': row['name'],
                        'team_id': row['team_id'],
                        'team': row['team_name'],
                        'position': row['position'],
                        'jersey_number': row['jersey_number'],
                        'height': row['height'],
                        'weight': row['weight'],
                        'age': row['age'],
                        'date_of_birth': row['date_of_birth'],
                        'source': 'supabase'
                    })
                return players
            
            return []
        except Exception as e:
            logger.error(f"Error getting team players: {e}")
            return []

    def store_nrl_player_stats(self, players_data: List[Dict]) -> bool:
        """Store comprehensive NRL player statistics in Supabase"""
        try:
            if not players_data:
                logger.warning("No player data to store")
                return False

            # Transform data for storage with enhanced fields
            transformed_data = []
            for player in players_data:
                transformed_player = {
                    'name': player.get('name', ''),
                    'position': player.get('position', ''),
                    'team': player.get('team', ''),
                    'price': self._safe_int(player.get('price', 0)),
                    'breakeven': self._safe_int(player.get('breakeven', 0)),
                    'average_points': self._safe_float(player.get('average', 0)),
                    'total_points': self._safe_int(player.get('total_points', 0)),
                    'games_played': self._safe_int(player.get('games_played', 0)),
                    'minutes_played': self._safe_int(player.get('minutes', 0)),

                    # Attacking stats
                    'tries': self._safe_int(player.get('tries', 0)),
                    'try_assists': self._safe_int(player.get('try_assists', 0)),
                    'linebreaks': self._safe_int(player.get('linebreaks', 0)),
                    'linebreak_assists': self._safe_int(player.get('linebreak_assists', 0)),
                    'offloads': self._safe_int(player.get('offloads', 0)),
                    'runs': self._safe_int(player.get('runs', 0)),
                    'metres': self._safe_int(player.get('metres', 0)),
                    'post_contact_metres': self._safe_int(player.get('post_contact_metres', 0)),

                    # Defensive stats
                    'tackles': self._safe_int(player.get('tackles', 0)),
                    'missed_tackles': self._safe_int(player.get('missed_tackles', 0)),
                    'tackle_efficiency': self._safe_float(player.get('tackle_efficiency', 0)),
                    'intercepts': self._safe_int(player.get('intercepts', 0)),

                    # Kicking stats
                    'kicks': self._safe_int(player.get('kicks', 0)),
                    'kick_metres': self._safe_int(player.get('kick_metres', 0)),
                    'forced_dropouts': self._safe_int(player.get('forced_dropouts', 0)),
                    'forty_twenties': self._safe_int(player.get('40_20s', 0)),

                    # Discipline
                    'errors': self._safe_int(player.get('errors', 0)),
                    'penalties': self._safe_int(player.get('penalties', 0)),
                    'sin_bins': self._safe_int(player.get('sin_bins', 0)),

                    # Form indicators
                    'last_3_avg': self._safe_float(player.get('last_3_avg', 0)),
                    'last_5_avg': self._safe_float(player.get('last_5_avg', 0)),
                    'home_avg': self._safe_float(player.get('home_avg', 0)),
                    'away_avg': self._safe_float(player.get('away_avg', 0)),

                    # Metadata
                    'data_source': player.get('source_url', ''),
                    'last_updated': datetime.now().isoformat()
                }
                transformed_data.append(transformed_player)

            # Store in batches to avoid request size limits
            batch_size = 50
            total_stored = 0

            for i in range(0, len(transformed_data), batch_size):
                batch = transformed_data[i:i + batch_size]

                # Use upsert to handle duplicates (based on name)
                result = self._make_request('POST', 'nrl_player_stats', batch)

                if result is not None:
                    total_stored += len(batch)
                    logger.info(f"Stored batch {i//batch_size + 1}: {len(batch)} player stats")
                else:
                    logger.error(f"Failed to store batch {i//batch_size + 1}")

            logger.info(f"✅ Total NRL player stats stored: {total_stored}")

            # Update metadata
            self.update_cache_metadata('last_nrl_stats_update', datetime.now().isoformat())
            self.update_cache_metadata('total_nrl_player_stats', str(total_stored))

            return total_stored > 0

        except Exception as e:
            logger.error(f"❌ Error storing NRL player stats: {e}")
            return False

    def _safe_int(self, value) -> int:
        """Safely convert value to int"""
        try:
            if isinstance(value, (int, float)):
                return int(value)
            elif isinstance(value, str):
                # Remove commas and other formatting
                clean_value = value.replace(',', '').replace('$', '').strip()
                return int(float(clean_value)) if clean_value else 0
            return 0
        except:
            return 0

    def _safe_float(self, value) -> float:
        """Safely convert value to float"""
        try:
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                # Remove commas and other formatting
                clean_value = value.replace(',', '').replace('$', '').replace('%', '').strip()
                return float(clean_value) if clean_value else 0.0
            return 0.0
        except:
            return 0.0

    def update_cache_metadata(self, key: str, value: str):
        """Update cache metadata"""
        try:
            # Use upsert (insert or update)
            data = {'key': key, 'value': value, 'updated_at': datetime.now().isoformat()}
            self._make_request('POST', 'cache_metadata', [data])
        except Exception as e:
            logger.error(f"Error updating cache metadata: {e}")

    def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        try:
            # Get player count
            players_result = self._make_request('GET', 'nrl_players?select=count')
            player_count = players_result[0]['count'] if players_result else 0
            
            # Get team count
            teams_result = self._make_request('GET', 'nrl_teams?select=count')
            team_count = teams_result[0]['count'] if teams_result else 0
            
            # Get last update
            metadata_result = self._make_request('GET', 'cache_metadata?key=eq.last_full_update')
            last_update = metadata_result[0]['value'] if metadata_result else None
            
            return {
                'players_count': player_count,
                'teams_count': team_count,
                'last_updated': last_update,
                'cache_fresh': self.is_cache_fresh(),
                'database': 'supabase'
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {'error': str(e)}

def main():
    """Test the Supabase client"""
    client = FantasyProSupabaseClient()
    
    # You need to set your Supabase service key here
    # service_key = "your_supabase_service_key_here"
    # client.set_service_key(service_key)
    
    print("Supabase client initialized")
    print("To use this client, you need to:")
    print("1. Get your Supabase service key from the project settings")
    print("2. Call client.set_service_key(your_key)")
    print("3. Then you can use client.update_from_sportradar(sportradar_key)")

if __name__ == "__main__":
    main()
