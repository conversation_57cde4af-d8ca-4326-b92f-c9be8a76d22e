#!/usr/bin/env python3
"""
Test SuperCoach site accessibility
"""

import requests
import time
from datetime import datetime

def test_supercoach_access():
    """Test different approaches to access SuperCoach"""
    
    print("🔍 Testing SuperCoach Site Access")
    print("=" * 50)
    
    # Different user agents to try
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ]
    
    urls_to_test = [
        'https://supercoach.com.au',
        'https://www.supercoach.com.au',
        'https://supercoach.com.au/nrl',
        'https://supercoach.com.au/login'
    ]
    
    for i, user_agent in enumerate(user_agents):
        print(f"\n🤖 Testing User Agent {i+1}:")
        print(f"   {user_agent[:60]}...")
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-AU,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'DNT': '1'
        })
        
        for url in urls_to_test:
            try:
                print(f"   Testing: {url}")
                response = session.get(url, timeout=10)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"   ✅ SUCCESS - Content length: {len(response.content)}")
                    
                    # Check for login form
                    if 'password' in response.text.lower():
                        print(f"   🔑 Login form detected")
                    
                    # Check for Cloudflare
                    if 'cloudflare' in response.text.lower():
                        print(f"   ☁️ Cloudflare detected")
                    
                    # Save successful response for analysis
                    if response.status_code == 200:
                        filename = f"supercoach_response_{i}_{url.split('/')[-1] or 'main'}.html"
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(response.text)
                        print(f"   💾 Saved response to {filename}")
                    
                elif response.status_code == 403:
                    print(f"   ❌ BLOCKED (403 Forbidden)")
                elif response.status_code == 429:
                    print(f"   ⏰ RATE LIMITED (429)")
                else:
                    print(f"   ⚠️ Other status: {response.status_code}")
                
                time.sleep(2)  # Rate limiting
                
            except Exception as e:
                print(f"   💥 ERROR: {e}")
        
        print()
        time.sleep(5)  # Longer delay between user agents
    
    print("\n🔍 Testing with different approaches...")
    
    # Test with session cookies
    print("\n🍪 Testing with session establishment:")
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': user_agents[0],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-AU,en-US;q=0.9,en;q=0.8',
        })
        
        # First visit main site
        main_response = session.get('https://supercoach.com.au', timeout=10)
        print(f"Main site: {main_response.status_code}")
        
        if main_response.status_code == 200:
            time.sleep(3)
            
            # Then try login page
            login_response = session.get('https://supercoach.com.au/login', timeout=10)
            print(f"Login page: {login_response.status_code}")
            
            if login_response.status_code == 200:
                print("✅ Successfully accessed login page with session!")
                
                # Save login page for analysis
                with open('supercoach_login_page.html', 'w', encoding='utf-8') as f:
                    f.write(login_response.text)
                print("💾 Saved login page to supercoach_login_page.html")
            
    except Exception as e:
        print(f"💥 Session test error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 RECOMMENDATIONS:")
    print("1. Check saved HTML files for site structure")
    print("2. If all requests are blocked, site uses strong anti-bot protection")
    print("3. Consider using Selenium for browser automation")
    print("4. May need to implement manual data entry as backup")
    print("5. Check if site has official API or RSS feeds")

if __name__ == "__main__":
    test_supercoach_access()
