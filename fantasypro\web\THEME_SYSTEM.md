# FantasyPro Theme System Documentation

## Overview

The FantasyPro theme system provides a seamless, maintainable dark/light mode experience that preserves the core design identity while offering a true inversion experience, not an afterthought.

## Key Features

### 🎨 **True Inversion Design**
- Light mode is a complete inversion of dark mode
- Maintains visual hierarchy and contrast ratios
- Preserves brand identity across both themes

### ⚡ **Performance Optimized**
- CSS custom properties for instant theme switching
- Hardware-accelerated transitions
- Minimal layout shifts during theme changes

### 🔧 **Developer Friendly**
- Semantic CSS custom properties
- Theme-aware utility classes
- TypeScript support with full type safety

### 🌟 **User Experience**
- Smooth transitions between themes
- System preference detection
- Persistent theme selection
- Multiple toggle variants

## Architecture

### Theme Context
```typescript
// Core theme management
const { theme, resolvedTheme, setTheme, toggleTheme } = useTheme();

// Available themes: 'dark' | 'light' | 'system'
// Resolved themes: 'dark' | 'light'
```

### CSS Custom Properties
```css
/* Core variables that adapt to theme */
--bg-primary: #0f172a;     /* Dark mode */
--bg-primary: #ffffff;     /* Light mode */

--text-primary: #ffffff;   /* Dark mode */
--text-primary: #0f172a;   /* Light mode */
```

### Theme-Aware Classes
```css
.themed-bg-primary { background-color: var(--bg-primary); }
.themed-text-primary { color: var(--text-primary); }
.themed-border { border-color: var(--border-primary); }
```

## Implementation Guide

### 1. Basic Setup

```tsx
// _app.tsx
import { ThemeProvider } from '../contexts/ThemeContext';

export default function App({ Component, pageProps }: AppProps) {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="fantasypro-theme">
      <Component {...pageProps} />
    </ThemeProvider>
  );
}
```

### 2. Using Theme Toggle

```tsx
import ThemeToggle from '../components/ThemeToggle';

// Button variant
<ThemeToggle variant="button" size="md" showLabel />

// Dropdown variant
<ThemeToggle variant="dropdown" size="lg" />

// Switch variant
<ThemeToggle variant="switch" size="sm" showLabel />
```

### 3. Theme-Aware Components

```tsx
// Use themed classes instead of hardcoded colors
<div className="themed-bg-secondary themed-text-primary themed-border">
  <h1 className="themed-text-primary">Title</h1>
  <p className="themed-text-tertiary">Description</p>
</div>
```

### 4. Custom Theme-Aware Styling

```css
.my-component {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  transition: var(--theme-transition);
}

.my-component:hover {
  box-shadow: var(--glow-primary);
}
```

## Color System

### Brand Colors (Consistent)
- **Primary**: `#22c55e` (Green) - Always consistent
- **Secondary**: `#3b82f6` (Blue) - Always consistent
- **Accent**: `#f59e0b` (Orange) - Always consistent
- **Danger**: `#ef4444` (Red) - Always consistent

### Adaptive Colors

#### Dark Theme
- **Background Primary**: `#0f172a` (Slate 900)
- **Background Secondary**: `#1e293b` (Slate 800)
- **Text Primary**: `#ffffff` (White)
- **Text Secondary**: `#e2e8f0` (Slate 200)

#### Light Theme
- **Background Primary**: `#ffffff` (White)
- **Background Secondary**: `#f8fafc` (Slate 50)
- **Text Primary**: `#0f172a` (Slate 900)
- **Text Secondary**: `#1e293b` (Slate 800)

## Component Examples

### Cards
```tsx
// Premium card with theme awareness
<div className="card-premium magnetic">
  <h3 className="themed-text-primary">Card Title</h3>
  <p className="themed-text-tertiary">Card content</p>
</div>
```

### Buttons
```tsx
// Theme-aware buttons maintain brand colors
<button className="btn-primary btn-ripple">
  Primary Action
</button>
```

### Status Indicators
```tsx
// Status colors adapt to theme background
<span className="themed-status-online">Online</span>
<span className="themed-status-warning">Warning</span>
<span className="themed-status-offline">Offline</span>
```

## Best Practices

### 1. Use Semantic Classes
```tsx
// ✅ Good - semantic and theme-aware
<div className="themed-bg-primary themed-text-secondary">

// ❌ Avoid - hardcoded colors
<div className="bg-slate-900 text-slate-400">
```

### 2. Maintain Contrast Ratios
- Ensure WCAG AA compliance in both themes
- Test with accessibility tools
- Use semantic color variables

### 3. Preserve Brand Identity
- Keep brand colors consistent across themes
- Maintain visual hierarchy
- Preserve component relationships

### 4. Optimize Performance
- Use CSS custom properties for instant switching
- Minimize layout shifts
- Leverage hardware acceleration

## Advanced Features

### System Preference Detection
```typescript
// Automatically detects and follows system theme
const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches 
  ? 'dark' 
  : 'light';
```

### Smooth Transitions
```css
/* Global theme transition */
.theme-transition * {
  transition: var(--theme-transition);
}

/* Custom transition timing */
--theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
```

### Theme-Aware Animations
```css
/* Glow effects adapt to theme */
.themed-glow-primary {
  box-shadow: var(--glow-primary);
}

/* Different intensities for light/dark */
:root.dark { --glow-intensity: 0.3; }
:root.light { --glow-intensity: 0.15; }
```

## Testing

### Visual Testing
1. Switch between themes rapidly
2. Check for layout shifts
3. Verify contrast ratios
4. Test all interactive states

### Accessibility Testing
1. Use screen readers with both themes
2. Check keyboard navigation
3. Verify focus indicators
4. Test with high contrast mode

### Performance Testing
1. Monitor theme switch performance
2. Check for memory leaks
3. Verify smooth animations
4. Test on low-end devices

## Troubleshooting

### Common Issues

1. **Flash of unstyled content**
   - Ensure theme is applied before render
   - Use `color-scheme` CSS property

2. **Inconsistent colors**
   - Use semantic variables consistently
   - Avoid hardcoded color values

3. **Poor performance**
   - Minimize CSS custom property usage
   - Use hardware acceleration
   - Optimize transition timing

### Debug Tools
```typescript
// Theme debugging hook
const { theme, resolvedTheme } = useTheme();
console.log('Current theme:', theme);
console.log('Resolved theme:', resolvedTheme);
```

## Migration Guide

### From Hardcoded Colors
```css
/* Before */
.component {
  background: #1e293b;
  color: #ffffff;
  border: 1px solid #334155;
}

/* After */
.component {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}
```

### From Tailwind Classes
```tsx
// Before
<div className="bg-slate-800 text-white border-slate-700">

// After
<div className="themed-bg-secondary themed-text-primary themed-border">
```

## Conclusion

The FantasyPro theme system provides a robust, maintainable solution for dark/light mode that preserves brand identity while offering users a seamless experience. The system is designed for performance, accessibility, and developer experience.
