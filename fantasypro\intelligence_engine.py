#!/usr/bin/env python3
"""
FantasyPro Intelligence Engine
Integrates all ML engines and AI agents to power specific FantasyPro features
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

from ml_engines import InjuryPredictionEngine, PerformancePredictionEngine, TradeOptimizationEngine, PlayerData
from injury_suspension_service import InjurySuspensionService
from supabase_client import FantasyProSupabaseClient

class FantasyProIntelligenceEngine:
    """Main intelligence engine that powers all FantasyPro features"""
    
    def __init__(self, supabase_key: Optional[str] = None):
        # Initialize core ML engines
        self.injury_engine = InjuryPredictionEngine()
        self.performance_engine = PerformancePredictionEngine()
        self.trade_engine = TradeOptimizationEngine(self.injury_engine, self.performance_engine)

        # Data services
        self.injury_service = InjurySuspensionService()

        if supabase_key:
            self.supabase_client = FantasyProSupabaseClient()
            self.supabase_client.set_service_key(supabase_key)
        else:
            self.supabase_client = None
    
    def get_captain_recommendations(self, squad_players: List[Dict], limit: int = 3) -> List[Dict]:
        """Generate AI-powered captain recommendations"""
        try:
            recommendations = []
            injury_data = self.injury_service.get_injury_suspension_data()
            
            for player in squad_players:
                # Convert to PlayerData for ML analysis
                player_data = self._convert_to_player_data(player)
                
                # Get injury risk assessment
                injury_risk = self.injury_engine.predict_injury_risk(player_data, injury_data.get('injuries', []))
                
                # Get performance prediction
                performance_pred = self.performance_engine.predict_performance(player_data, injury_risk)
                
                # Calculate captain score (higher is better)
                captain_score = self._calculate_captain_score(performance_pred, injury_risk, player)
                
                recommendations.append({
                    'player_id': player.get('id'),
                    'player_name': player.get('name'),
                    'team': player.get('team'),
                    'position': player.get('position'),
                    'predicted_score': performance_pred['predicted_score'],
                    'captain_score': captain_score,
                    'confidence': performance_pred['confidence'],
                    'injury_risk': injury_risk['risk_score'],
                    'form_rating': performance_pred['factors']['form_score'],
                    'reasoning': self._generate_captain_reasoning(performance_pred, injury_risk, player)
                })
            
            # Sort by captain score and return top recommendations
            recommendations.sort(key=lambda x: x['captain_score'], reverse=True)
            return recommendations[:limit]
            
        except Exception as e:
            logger.error(f"Error generating captain recommendations: {e}")
            return []
    
    def get_trade_recommendations(self, current_squad: List[Dict], budget: float = 0, limit: int = 5) -> List[Dict]:
        """Generate intelligent trade recommendations"""
        try:
            if not self.supabase_client:
                return []
            
            # Get all available players
            all_players = self.supabase_client.get_all_players(1000)
            injury_data = self.injury_service.get_injury_suspension_data()
            
            trade_recommendations = []
            
            for current_player in current_squad:
                # Find potential replacements in same position
                same_position = [
                    p for p in all_players 
                    if p.get('position') == current_player.get('position') and 
                       p.get('id') != current_player.get('id')
                ]
                
                for replacement in same_position[:20]:  # Limit for performance
                    # Convert to PlayerData
                    current_data = self._convert_to_player_data(current_player)
                    replacement_data = self._convert_to_player_data(replacement)
                    
                    # Analyze trade opportunity
                    trade_analysis = self.trade_engine.analyze_trade_opportunity(
                        current_data, replacement_data, injury_data.get('injuries', [])
                    )
                    
                    if trade_analysis['recommendation'] in ['Strong Buy', 'Buy']:
                        trade_recommendations.append({
                            'player_out': trade_analysis['player_out'],
                            'player_in': trade_analysis['player_in'],
                            'trade_metrics': trade_analysis['trade_metrics'],
                            'recommendation': trade_analysis['recommendation'],
                            'confidence': trade_analysis['confidence'],
                            'reasoning': self._generate_trade_reasoning(trade_analysis)
                        })
            
            # Sort by value and return top recommendations
            trade_recommendations.sort(
                key=lambda x: x['trade_metrics']['points_difference'] * x['confidence'], 
                reverse=True
            )
            return trade_recommendations[:limit]
            
        except Exception as e:
            logger.error(f"Error generating trade recommendations: {e}")
            return []
    
    def get_analytics_predictions(self, players: List[Dict], rounds_ahead: int = 3) -> Dict[str, Any]:
        """Generate comprehensive analytics and predictions"""
        try:
            injury_data = self.injury_service.get_injury_suspension_data()
            
            analytics = {
                'price_predictions': [],
                'performance_predictions': [],
                'injury_risks': [],
                'form_analysis': [],
                'summary_stats': {}
            }
            
            total_predicted_points = 0
            high_risk_players = 0
            rising_form_players = 0
            
            for player in players:
                player_data = self._convert_to_player_data(player)
                
                # Get comprehensive analysis
                injury_risk = self.injury_engine.predict_injury_risk(player_data, injury_data.get('injuries', []))
                performance_pred = self.performance_engine.predict_performance(player_data, injury_risk)
                
                # Price prediction (simplified)
                current_price = player.get('price', 500000)
                price_change = performance_pred['predicted_score'] - 50  # Simplified price change
                price_change = max(-50000, min(50000, price_change * 1000))  # Cap changes
                
                analytics['price_predictions'].append({
                    'player_name': player.get('name'),
                    'current_price': current_price,
                    'predicted_price': current_price + price_change,
                    'price_change': price_change,
                    'confidence': performance_pred['confidence']
                })
                
                analytics['performance_predictions'].append({
                    'player_name': player.get('name'),
                    'predicted_score': performance_pred['predicted_score'],
                    'confidence': performance_pred['confidence'],
                    'form_score': performance_pred['factors']['form_score']
                })
                
                analytics['injury_risks'].append({
                    'player_name': player.get('name'),
                    'risk_score': injury_risk['risk_score'],
                    'risk_category': injury_risk['risk_category'],
                    'recommendation': injury_risk['recommendation']
                })
                
                # Accumulate stats
                total_predicted_points += performance_pred['predicted_score']
                if injury_risk['risk_score'] > 0.4:
                    high_risk_players += 1
                if performance_pred['factors']['form_score'] > 60:
                    rising_form_players += 1
            
            analytics['summary_stats'] = {
                'total_predicted_points': round(total_predicted_points, 1),
                'average_predicted_score': round(total_predicted_points / len(players), 1) if players else 0,
                'high_risk_players': high_risk_players,
                'rising_form_players': rising_form_players,
                'total_players_analyzed': len(players)
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error generating analytics: {e}")
            return {'error': str(e)}
    
    def get_injury_intelligence(self, players: List[Dict]) -> Dict[str, Any]:
        """Get comprehensive injury intelligence for players"""
        try:
            injury_data = self.injury_service.get_injury_suspension_data()
            
            intelligence = {
                'current_injuries': injury_data.get('injuries', []),
                'current_suspensions': injury_data.get('suspensions', []),
                'risk_assessments': [],
                'recommendations': []
            }
            
            for player in players:
                player_data = self._convert_to_player_data(player)
                risk_assessment = self.injury_engine.predict_injury_risk(player_data, injury_data.get('injuries', []))
                
                intelligence['risk_assessments'].append({
                    'player_name': player.get('name'),
                    'team': player.get('team'),
                    'risk_score': risk_assessment['risk_score'],
                    'risk_category': risk_assessment['risk_category'],
                    'factors': risk_assessment['factors'],
                    'recommendation': risk_assessment['recommendation']
                })
            
            # Generate recommendations based on injury data
            for injury in injury_data.get('injuries', []):
                if injury.get('status') in ['Season Ending', 'Indefinite']:
                    intelligence['recommendations'].append({
                        'type': 'avoid',
                        'player': injury.get('player_name'),
                        'reason': f"Season ending injury: {injury.get('reason')}"
                    })
            
            return intelligence
            
        except Exception as e:
            logger.error(f"Error getting injury intelligence: {e}")
            return {'error': str(e)}
    
    def _convert_to_player_data(self, player_dict: Dict) -> PlayerData:
        """Convert player dictionary to PlayerData object"""
        return PlayerData(
            player_id=str(player_dict.get('id', '')),
            name=player_dict.get('name', ''),
            team=player_dict.get('team', ''),
            position=player_dict.get('position', 'Unknown'),
            price=float(player_dict.get('price', 500000)),
            recent_scores=player_dict.get('recent_scores', []),
            season_points=int(player_dict.get('season_points', 0)),
            games_played=int(player_dict.get('games_played', 15)),
            injury_history=player_dict.get('injury_history', []),
            ownership_percentage=float(player_dict.get('ownership_percentage', 20.0))
        )
    
    def _calculate_captain_score(self, performance_pred: Dict, injury_risk: Dict, player: Dict) -> float:
        """Calculate captain suitability score"""
        base_score = performance_pred['predicted_score']
        
        # Adjust for confidence
        confidence_bonus = performance_pred['confidence'] * 10
        
        # Penalty for injury risk
        injury_penalty = injury_risk['risk_score'] * 20
        
        # Bonus for consistent performers (simplified)
        consistency_bonus = 5 if performance_pred['factors']['form_score'] > 60 else 0
        
        captain_score = base_score + confidence_bonus - injury_penalty + consistency_bonus
        return round(captain_score, 1)
    
    def _generate_captain_reasoning(self, performance_pred: Dict, injury_risk: Dict, player: Dict) -> str:
        """Generate reasoning for captain recommendation"""
        reasons = []
        
        if performance_pred['predicted_score'] > 70:
            reasons.append("High predicted score")
        
        if performance_pred['confidence'] > 0.8:
            reasons.append("High prediction confidence")
        
        if injury_risk['risk_score'] < 0.2:
            reasons.append("Low injury risk")
        
        if performance_pred['factors']['form_score'] > 60:
            reasons.append("Good recent form")
        
        return ". ".join(reasons) if reasons else "Solid captain option"
    
    def _generate_trade_reasoning(self, trade_analysis: Dict) -> str:
        """Generate reasoning for trade recommendation"""
        metrics = trade_analysis['trade_metrics']
        
        reasons = []
        
        if metrics['points_difference'] > 5:
            reasons.append(f"+{metrics['points_difference']:.1f} points expected")
        
        if metrics['price_difference'] < 0:
            reasons.append(f"Save ${abs(metrics['price_difference']):,.0f}")
        
        if metrics['risk_difference'] < -0.1:
            reasons.append("Lower injury risk")
        
        return ". ".join(reasons) if reasons else "Strategic upgrade opportunity"

def main():
    """Test the intelligence engine"""
    print("🧠 Testing FantasyPro Intelligence Engine")
    
    # Sample player data
    sample_players = [
        {
            'id': 1,
            'name': 'James Tedesco',
            'team': 'Sydney Roosters',
            'position': 'Fullback',
            'price': 817700,
            'recent_scores': [85.4, 72.1, 91.2],
            'season_points': 1200,
            'games_played': 15
        },
        {
            'id': 2,
            'name': 'Nathan Cleary',
            'team': 'Penrith Panthers',
            'position': 'Halfback',
            'price': 795600,
            'recent_scores': [78.2, 82.5, 69.8],
            'season_points': 1150,
            'games_played': 14
        }
    ]
    
    # Initialize engine
    engine = FantasyProIntelligenceEngine()
    
    # Test captain recommendations
    captain_recs = engine.get_captain_recommendations(sample_players)
    print(f"Captain recommendations: {len(captain_recs)}")
    for rec in captain_recs:
        print(f"  - {rec['player_name']}: {rec['captain_score']} score")
    
    # Test analytics
    analytics = engine.get_analytics_predictions(sample_players)
    print(f"Analytics generated: {analytics.get('summary_stats', {})}")

if __name__ == "__main__":
    main()
