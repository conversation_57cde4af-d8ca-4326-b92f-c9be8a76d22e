<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FantasyPro API Test</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>FantasyPro API Connection Test</h1>
    
    <div id="status">Testing connection...</div>
    
    <h2>Health Check</h2>
    <div id="health-result">Loading...</div>
    
    <h2>Players Data</h2>
    <div id="players-result">Loading...</div>
    
    <h2>Recommendations</h2>
    <div id="recommendations-result">Loading...</div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        
        async function testAPI() {
            const statusDiv = document.getElementById('status');
            const healthDiv = document.getElementById('health-result');
            const playersDiv = document.getElementById('players-result');
            const recommendationsDiv = document.getElementById('recommendations-result');
            
            try {
                // Test health endpoint
                console.log('Testing health endpoint...');
                const healthResponse = await axios.get(`${API_BASE_URL}/health`);
                console.log('Health response:', healthResponse.data);
                healthDiv.innerHTML = `<pre>${JSON.stringify(healthResponse.data, null, 2)}</pre>`;
                
                // Test players endpoint
                console.log('Testing players endpoint...');
                const playersResponse = await axios.get(`${API_BASE_URL}/players`);
                console.log('Players response:', playersResponse.data);
                playersDiv.innerHTML = `<pre>${JSON.stringify(playersResponse.data, null, 2)}</pre>`;
                
                // Test recommendations endpoint
                console.log('Testing recommendations endpoint...');
                const recommendationsResponse = await axios.get(`${API_BASE_URL}/recommendations`);
                console.log('Recommendations response:', recommendationsResponse.data);
                recommendationsDiv.innerHTML = `<pre>${JSON.stringify(recommendationsResponse.data, null, 2)}</pre>`;
                
                statusDiv.innerHTML = '<span style="color: green;">✅ All API endpoints working!</span>';
                
            } catch (error) {
                console.error('API test failed:', error);
                statusDiv.innerHTML = `<span style="color: red;">❌ API test failed: ${error.message}</span>`;
                
                if (error.response) {
                    statusDiv.innerHTML += `<br>Status: ${error.response.status}`;
                    statusDiv.innerHTML += `<br>Data: ${JSON.stringify(error.response.data)}`;
                }
            }
        }
        
        // Run test when page loads
        document.addEventListener('DOMContentLoaded', testAPI);
    </script>
</body>
</html>
