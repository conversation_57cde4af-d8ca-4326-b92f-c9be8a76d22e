#!/usr/bin/env node
/**
 * FantasyPro Performance Optimization Script
 * Optimizes the web application for extreme speed and smooth interactions
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 FantasyPro Performance Optimization');
console.log('=' * 50);

// Next.js configuration optimizations
const nextConfigOptimizations = `
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Performance optimizations
  reactStrictMode: true,
  swcMinify: true,
  
  // Image optimization
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
  
  // Compression
  compress: true,
  
  // Bundle analyzer (development only)
  ...(process.env.ANALYZE === 'true' && {
    webpack: (config) => {
      config.plugins.push(
        new (require('@next/bundle-analyzer'))({
          enabled: true,
        })
      );
      return config;
    },
  }),
  
  // Experimental features for performance
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
    legacyBrowsers: false,
    browsersListForSwc: true,
  },
  
  // Headers for caching and security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
`;

// Package.json performance dependencies
const performanceDependencies = {
  "devDependencies": {
    "@next/bundle-analyzer": "^14.0.0",
    "webpack-bundle-analyzer": "^4.9.0"
  },
  "scripts": {
    "analyze": "ANALYZE=true npm run build",
    "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html",
    "perf": "npm run build && npm run analyze"
  }
};

// Performance monitoring component
const performanceMonitor = `
import { useEffect } from 'react';

export const usePerformanceMonitor = () => {
  useEffect(() => {
    // Monitor Core Web Vitals
    if (typeof window !== 'undefined' && 'web-vital' in window) {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(console.log);
        getFID(console.log);
        getFCP(console.log);
        getLCP(console.log);
        getTTFB(console.log);
      });
    }
    
    // Monitor memory usage
    if (performance.memory) {
      const memoryInfo = performance.memory;
      console.log('Memory Usage:', {
        used: Math.round(memoryInfo.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memoryInfo.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memoryInfo.jsHeapSizeLimit / 1048576) + ' MB'
      });
    }
    
    // Monitor frame rate
    let frameCount = 0;
    let lastTime = performance.now();
    
    function countFrames() {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        console.log('FPS:', frameCount);
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(countFrames);
    }
    
    requestAnimationFrame(countFrames);
  }, []);
};

export default usePerformanceMonitor;
`;

// Optimized CSS for better performance
const optimizedCSS = `
/* Performance optimizations */
* {
  /* Use hardware acceleration for transforms */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Optimize repaints */
.card-premium,
.card-glow,
.magnetic {
  will-change: transform, box-shadow;
}

.btn-primary,
.btn-secondary,
.btn-outline,
.btn-danger {
  will-change: transform, box-shadow, background-color;
}

/* Optimize scrolling */
.scrollable {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Optimize text rendering */
body {
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Optimize images */
img {
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* Critical CSS inlining */
.above-fold {
  contain: layout style paint;
}
`;

// Service Worker for caching
const serviceWorker = `
const CACHE_NAME = 'fantasypro-v1';
const urlsToCache = [
  '/',
  '/dashboard',
  '/my-squad',
  '/static/css/main.css',
  '/static/js/main.js',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
  );
});
`;

// Write optimization files
function writeOptimizations() {
  try {
    // Write Next.js config
    fs.writeFileSync(path.join(__dirname, 'next.config.js'), nextConfigOptimizations);
    console.log('✅ Next.js configuration optimized');
    
    // Update package.json
    const packagePath = path.join(__dirname, 'package.json');
    if (fs.existsSync(packagePath)) {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      packageJson.devDependencies = { ...packageJson.devDependencies, ...performanceDependencies.devDependencies };
      packageJson.scripts = { ...packageJson.scripts, ...performanceDependencies.scripts };
      fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
      console.log('✅ Package.json updated with performance tools');
    }
    
    // Write performance monitor
    const hooksDir = path.join(__dirname, 'src', 'hooks');
    if (!fs.existsSync(hooksDir)) {
      fs.mkdirSync(hooksDir, { recursive: true });
    }
    fs.writeFileSync(path.join(hooksDir, 'usePerformanceMonitor.ts'), performanceMonitor);
    console.log('✅ Performance monitoring hook created');
    
    // Append optimized CSS
    const cssPath = path.join(__dirname, 'src', 'styles', 'globals.css');
    if (fs.existsSync(cssPath)) {
      fs.appendFileSync(cssPath, '\\n\\n' + optimizedCSS);
      console.log('✅ Performance CSS optimizations added');
    }
    
    // Write service worker
    fs.writeFileSync(path.join(__dirname, 'public', 'sw.js'), serviceWorker);
    console.log('✅ Service worker created for caching');
    
    console.log('\\n🎉 Performance optimizations complete!');
    console.log('\\n📊 Next steps:');
    console.log('   1. Run: npm install (to install new dependencies)');
    console.log('   2. Run: npm run analyze (to analyze bundle size)');
    console.log('   3. Run: npm run lighthouse (to test performance)');
    console.log('   4. Monitor Core Web Vitals in browser console');
    
  } catch (error) {
    console.error('❌ Error applying optimizations:', error);
  }
}

// Performance checklist
function showPerformanceChecklist() {
  console.log('\\n📋 Performance Optimization Checklist:');
  console.log('=' * 40);
  console.log('✅ CSS animations use transform and opacity');
  console.log('✅ Hardware acceleration enabled');
  console.log('✅ Reduced motion support added');
  console.log('✅ Will-change properties optimized');
  console.log('✅ Image optimization configured');
  console.log('✅ Bundle analysis tools added');
  console.log('✅ Service worker for caching');
  console.log('✅ Core Web Vitals monitoring');
  console.log('✅ Memory usage tracking');
  console.log('✅ Frame rate monitoring');
  console.log('\\n🎯 Target Metrics:');
  console.log('   - First Contentful Paint: < 1.8s');
  console.log('   - Largest Contentful Paint: < 2.5s');
  console.log('   - First Input Delay: < 100ms');
  console.log('   - Cumulative Layout Shift: < 0.1');
  console.log('   - Frame Rate: 60 FPS');
}

// Run optimizations
if (require.main === module) {
  writeOptimizations();
  showPerformanceChecklist();
}

module.exports = {
  writeOptimizations,
  showPerformanceChecklist
};
