#!/usr/bin/env python3
"""
SuperCoach Ownership Mapper
Final step: Map exact selectors for ownership percentages in authenticated SuperCoach interface
"""

import time
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)

class SuperCoachOwnershipMapper:
    """Map exact ownership percentage selectors in SuperCoach interface"""
    
    def __init__(self, email: str = "<EMAIL>", password: str = "SuperKenny123!"):
        self.email = email
        self.password = password
        self.driver = None
        self.wait = None
        self.is_authenticated = False
        
        # Target URLs to systematically explore for ownership data
        self.target_urls = [
            'https://www.supercoach.com.au/nrl',
            'https://www.supercoach.com.au/nrl/classic',
            'https://www.supercoach.com.au/nrl/classic/team/select',
            'https://www.supercoach.com.au/nrl/classic/stats',
            'https://www.supercoach.com.au/nrl/classic/ladder',
            'https://www.supercoach.com.au/nrl/stats',
            'https://www.supercoach.com.au/nrl/players',
            'https://www.supercoach.com.au/nrl/my-team'
        ]
        
        self.ownership_data = {}
        self.cache_dir = Path("data/ownership_mapping")
        self.cache_dir.mkdir(exist_ok=True)
    
    def setup_driver(self) -> bool:
        """Setup Chrome driver for ownership mapping"""
        try:
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Run in visible mode to see the interface
            # options.add_argument('--headless')
            
            self.driver = webdriver.Chrome(
                service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
                options=options
            )
            
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 20)
            
            logger.info("✅ Chrome driver setup successful")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error setting up Chrome driver: {e}")
            return False
    
    def authenticate(self) -> bool:
        """Authenticate with SuperCoach using Auth0"""
        try:
            if not self.driver:
                if not self.setup_driver():
                    return False
            
            logger.info("🔐 Authenticating with SuperCoach...")
            
            # Navigate to Auth0 login directly
            auth0_url = "https://login.newscorpaustralia.com/login?state=hKFo2SBER3lEYktxVFNnN2RHSS1LLVlUQVNfOXlqclBtUng5VaFupWxvZ2luo3RpZNkgT2lsanBRUkhpY1lfM2JwOWJ6T19TNUFwZmx0TEhoY2SjY2lk2SBaWUNvdGxpaHFhR3VhcVNzU3Z1MEwydnhEZFFYQ3cxNg&client=ZYCotlihqaGuaqSsSvu0L2vxDdQXCw16&protocol=oauth2&response_type=token%20id_token&scope=openid%20profile&audience=newscorpaustralia&site=supercoach&redirect_uri=https%3A%2F%2Fwww.supercoach.com.au%2Fassets%2Fsites%2Fnews%2Fauth0%2Fcallback.html%3FredirectUri%3Dhttps%253A%252F%252Fwww.supercoach.com.au%252Fnrl&prevent_sign_up=&nonce=MwchNFYLpvljaSzA0FtG0kx5JhtggrZ5&auth0Client=eyJuYW1lIjoiYXV0aDAuanMiLCJ2ZXJzaW9uIjoiOS4yOC4wIn0%3D"
            self.driver.get(auth0_url)
            time.sleep(5)
            
            # Find and fill email field
            email_field = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'input[type="email"]')))
            email_field.clear()
            email_field.send_keys(self.email)
            time.sleep(1)
            
            # Find and fill password field
            password_field = self.driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
            password_field.clear()
            password_field.send_keys(self.password)
            time.sleep(1)
            
            # Submit form
            submit_button = self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            self.driver.execute_script("arguments[0].click();", submit_button)
            time.sleep(8)
            
            # Check if authentication was successful
            current_url = self.driver.current_url.lower()
            if 'supercoach.com.au' in current_url and 'login.newscorpaustralia.com' not in current_url:
                self.is_authenticated = True
                logger.info("✅ SuperCoach authentication successful")
                return True
            else:
                logger.error("❌ SuperCoach authentication failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error during authentication: {e}")
            return False
    
    def map_ownership_selectors(self) -> Dict[str, Any]:
        """Systematically map ownership percentage selectors"""
        try:
            if not self.authenticate():
                logger.error("Authentication failed")
                return {}
            
            mapping_results = {
                'mapping_timestamp': datetime.now().isoformat(),
                'urls_mapped': {},
                'ownership_selectors_found': [],
                'percentage_patterns': [],
                'successful_extractions': {}
            }
            
            for url in self.target_urls:
                try:
                    logger.info(f"🎯 Mapping ownership data at: {url}")
                    
                    self.driver.get(url)
                    time.sleep(8)  # Wait for SPA to load
                    
                    # Map this URL
                    url_mapping = self._map_single_url(url)
                    mapping_results['urls_mapped'][url] = url_mapping
                    
                    # If we found ownership data, add to successful extractions
                    if url_mapping.get('ownership_data_found'):
                        mapping_results['successful_extractions'][url] = url_mapping['ownership_data_found']
                        
                        # Collect unique selectors
                        for selector in url_mapping.get('working_selectors', []):
                            if selector not in mapping_results['ownership_selectors_found']:
                                mapping_results['ownership_selectors_found'].append(selector)
                    
                    # Collect percentage patterns
                    patterns = url_mapping.get('percentage_patterns', [])
                    for pattern in patterns:
                        if pattern not in mapping_results['percentage_patterns']:
                            mapping_results['percentage_patterns'].append(pattern)
                    
                    time.sleep(3)  # Rate limiting
                    
                except Exception as e:
                    logger.error(f"Error mapping {url}: {e}")
                    mapping_results['urls_mapped'][url] = {'error': str(e)}
                    continue
            
            # Save mapping results
            self._save_mapping_results(mapping_results)
            
            return mapping_results
            
        except Exception as e:
            logger.error(f"Error in ownership mapping: {e}")
            return {}
        finally:
            if self.driver:
                self.driver.quit()
    
    def _map_single_url(self, url: str) -> Dict[str, Any]:
        """Map ownership data for a single URL"""
        try:
            url_mapping = {
                'url': url,
                'current_url': self.driver.current_url,
                'page_title': self.driver.title,
                'ownership_data_found': {},
                'working_selectors': [],
                'percentage_patterns': [],
                'player_data_with_ownership': []
            }
            
            # Look for percentage patterns in page text
            page_text = self.driver.page_source
            import re
            percentage_matches = re.findall(r'\b\d{1,3}\.?\d*%', page_text)
            url_mapping['percentage_patterns'] = list(set(percentage_matches))[:20]  # Limit to 20
            
            # Try different ownership-related selectors
            ownership_selectors = [
                # Generic ownership selectors
                '[data-ownership]', '[data-picked]', '[data-percentage]',
                '.ownership', '.owned', '.picked', '.percentage',
                '.player-ownership', '.team-ownership', '.stats-ownership',
                
                # SuperCoach specific selectors
                '.sc-ownership', '.sc-picked', '.sc-percentage',
                '[data-testid*="ownership"]', '[data-testid*="picked"]',
                
                # Table-based selectors
                'td:contains("%")', 'th:contains("Owned")', 'th:contains("Picked")',
                'th:contains("Ownership")', 'th:contains("%")',
                
                # Text-based patterns
                '*:contains("% teams")', '*:contains("picked by")',
                '*:contains("ownership")', '*:contains("selected by")'
            ]
            
            for selector in ownership_selectors:
                try:
                    elements = self._find_elements_by_selector(selector)
                    if elements:
                        logger.info(f"Found {len(elements)} elements with selector: {selector}")
                        url_mapping['working_selectors'].append(selector)
                        
                        # Extract data from these elements
                        ownership_data = self._extract_ownership_from_elements(elements, selector)
                        if ownership_data:
                            url_mapping['ownership_data_found'][selector] = ownership_data
                
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            # Look for player tables with ownership data
            player_tables = self._find_player_tables_with_ownership()
            if player_tables:
                url_mapping['player_data_with_ownership'] = player_tables
            
            # Try JavaScript extraction
            js_ownership_data = self._extract_ownership_via_javascript()
            if js_ownership_data:
                url_mapping['javascript_ownership_data'] = js_ownership_data
            
            return url_mapping
            
        except Exception as e:
            logger.error(f"Error mapping single URL {url}: {e}")
            return {'url': url, 'error': str(e)}
    
    def _find_elements_by_selector(self, selector: str) -> List:
        """Find elements using various selector types"""
        try:
            if ':contains(' in selector:
                # Convert to XPath
                text_content = selector.split(':contains(')[1].split(')')[0].strip('"')
                xpath = f"//*[contains(text(), '{text_content}')]"
                return self.driver.find_elements(By.XPATH, xpath)
            else:
                return self.driver.find_elements(By.CSS_SELECTOR, selector)
        except:
            return []
    
    def _extract_ownership_from_elements(self, elements: List, selector: str) -> Dict[str, Any]:
        """Extract ownership data from found elements"""
        try:
            ownership_data = {
                'selector': selector,
                'element_count': len(elements),
                'sample_data': []
            }
            
            for i, element in enumerate(elements[:10]):  # Limit to first 10
                try:
                    element_data = {
                        'index': i,
                        'text': element.text.strip(),
                        'tag': element.tag_name,
                        'attributes': {}
                    }
                    
                    # Get relevant attributes
                    for attr in ['class', 'id', 'data-ownership', 'data-picked', 'data-percentage']:
                        try:
                            value = element.get_attribute(attr)
                            if value:
                                element_data['attributes'][attr] = value
                        except:
                            continue
                    
                    # Look for percentage in text
                    import re
                    percentage_match = re.search(r'\b(\d{1,3}\.?\d*)%', element_data['text'])
                    if percentage_match:
                        element_data['percentage_found'] = percentage_match.group(1)
                    
                    ownership_data['sample_data'].append(element_data)
                    
                except Exception as e:
                    logger.debug(f"Error extracting from element {i}: {e}")
                    continue
            
            return ownership_data
            
        except Exception as e:
            logger.error(f"Error extracting ownership from elements: {e}")
            return {}
    
    def _find_player_tables_with_ownership(self) -> List[Dict[str, Any]]:
        """Find tables that contain player data with ownership percentages"""
        try:
            tables = self.driver.find_elements(By.CSS_SELECTOR, 'table')
            player_tables = []
            
            for i, table in enumerate(tables):
                try:
                    # Get table headers
                    headers = []
                    header_elements = table.find_elements(By.CSS_SELECTOR, 'th, thead td')
                    for header in header_elements:
                        header_text = header.text.strip().lower()
                        headers.append(header_text)
                    
                    # Check if this looks like a player table with ownership
                    has_player_indicators = any(indicator in ' '.join(headers) for indicator in ['name', 'player', 'position'])
                    has_ownership_indicators = any(indicator in ' '.join(headers) for indicator in ['%', 'owned', 'picked', 'ownership'])
                    
                    if has_player_indicators and has_ownership_indicators:
                        # Extract sample rows
                        rows = table.find_elements(By.CSS_SELECTOR, 'tbody tr, tr')[:5]
                        sample_rows = []
                        
                        for row in rows:
                            cells = row.find_elements(By.CSS_SELECTOR, 'td, th')
                            cell_texts = [cell.text.strip() for cell in cells]
                            if cell_texts and any(cell for cell in cell_texts):
                                sample_rows.append(cell_texts)
                        
                        player_tables.append({
                            'table_index': i,
                            'headers': headers,
                            'sample_rows': sample_rows,
                            'row_count': len(rows)
                        })
                
                except Exception as e:
                    logger.debug(f"Error analyzing table {i}: {e}")
                    continue
            
            return player_tables
            
        except Exception as e:
            logger.error(f"Error finding player tables: {e}")
            return []
    
    def _extract_ownership_via_javascript(self) -> Dict[str, Any]:
        """Extract ownership data using JavaScript"""
        try:
            js_queries = [
                # Look for data in common JavaScript stores
                "return window.__INITIAL_STATE__ || null;",
                "return window.__REDUX_STATE__ || null;",
                "return window.APP_DATA || null;",
                
                # Look for elements containing percentages
                "return Array.from(document.querySelectorAll('*')).filter(el => el.textContent.match(/\\d+\\.?\\d*%/)).slice(0, 20).map(el => ({tag: el.tagName, text: el.textContent.trim(), class: el.className, id: el.id}));",
                
                # Look for ownership-related data attributes
                "return Array.from(document.querySelectorAll('[data-ownership], [data-picked], [data-percentage]')).map(el => ({tag: el.tagName, text: el.textContent.trim(), attributes: Array.from(el.attributes).reduce((acc, attr) => {acc[attr.name] = attr.value; return acc;}, {})}));",
                
                # Look for tables with percentage data
                "return Array.from(document.querySelectorAll('table')).map((table, i) => ({index: i, headers: Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim()), hasPercentages: table.textContent.includes('%')})).filter(t => t.hasPercentages);"
            ]
            
            js_results = {}
            for i, query in enumerate(js_queries):
                try:
                    result = self.driver.execute_script(query)
                    if result:
                        js_results[f'query_{i}'] = result
                except Exception as e:
                    logger.debug(f"JavaScript query {i} failed: {e}")
                    continue
            
            return js_results
            
        except Exception as e:
            logger.error(f"Error extracting ownership via JavaScript: {e}")
            return {}
    
    def _save_mapping_results(self, results: Dict[str, Any]):
        """Save ownership mapping results"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_file = self.cache_dir / f"ownership_mapping_{timestamp}.json"
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            # Also save as latest
            latest_file = self.cache_dir / "ownership_mapping_latest.json"
            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Saved ownership mapping results to {results_file}")
            
        except Exception as e:
            logger.error(f"Error saving mapping results: {e}")

def main():
    """Main ownership mapping function"""
    print("🎯 SuperCoach Ownership Percentage Mapper")
    print("=" * 50)
    print("🔍 Systematically mapping ownership % selectors in authenticated SuperCoach interface...")
    
    mapper = SuperCoachOwnershipMapper()
    results = mapper.map_ownership_selectors()
    
    if results:
        print("\n✅ Ownership mapping completed!")
        
        urls_mapped = len(results.get('urls_mapped', {}))
        selectors_found = len(results.get('ownership_selectors_found', []))
        successful_extractions = len(results.get('successful_extractions', {}))
        
        print(f"📊 Mapping Summary:")
        print(f"   URLs mapped: {urls_mapped}")
        print(f"   Working selectors found: {selectors_found}")
        print(f"   Successful extractions: {successful_extractions}")
        
        if successful_extractions > 0:
            print(f"\n🎉 Found ownership data sources:")
            for url, data in results.get('successful_extractions', {}).items():
                print(f"   - {url}")
                for selector, selector_data in data.items():
                    element_count = selector_data.get('element_count', 0)
                    print(f"     Selector: {selector} ({element_count} elements)")
        
        percentage_patterns = results.get('percentage_patterns', [])
        if percentage_patterns:
            print(f"\n📈 Percentage patterns found: {len(percentage_patterns)}")
            print(f"   Sample: {percentage_patterns[:10]}")
        
        print(f"\n🎯 FINAL STEP COMPLETE!")
        print(f"   Ownership percentage mapping data saved for integration")
        
    else:
        print("❌ Ownership mapping failed")

if __name__ == "__main__":
    main()
