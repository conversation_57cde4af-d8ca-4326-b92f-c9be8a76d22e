import React, { useState } from 'react';
import { motion } from 'framer-motion';
import SportRadarService from '../services/sportradar';

const SportRadarTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testAPI = async () => {
    setTesting(true);
    setResult(null);
    setError(null);

    try {
      console.log('🧪 Testing SportRadar API via proxy...');
      
      // Test the competitions endpoint
      const competitions = await SportRadarService.getCompetitions();
      
      console.log('✅ SportRadar API Success!', competitions);
      setResult(competitions);
      
    } catch (err) {
      console.error('❌ SportRadar API Error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setTesting(false);
    }
  };

  const testProxy = async () => {
    setTesting(true);
    setResult(null);
    setError(null);

    try {
      console.log('🔄 Testing proxy directly...');
      
      const response = await fetch('/api/sportradar-proxy?endpoint=competitions.json');
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Proxy Success!', data);
        setResult(data);
      } else {
        const errorData = await response.json();
        console.error('❌ Proxy Error:', errorData);
        setError(errorData.error || 'Proxy failed');
      }
      
    } catch (err) {
      console.error('💥 Proxy Request Error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setTesting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card-premium p-6"
    >
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold themed-text-primary">SportRadar API Test</h2>
          <p className="text-sm themed-text-tertiary">Test real API connectivity</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={testProxy}
            disabled={testing}
            className="btn-secondary btn-ripple"
          >
            {testing ? 'Testing...' : 'Test Proxy'}
          </button>
          <button
            onClick={testAPI}
            disabled={testing}
            className="btn-primary btn-ripple"
          >
            {testing ? 'Testing...' : 'Test API Service'}
          </button>
        </div>
      </div>

      {/* Loading State */}
      {testing && (
        <div className="flex items-center justify-center py-8">
          <div className="loading-spinner w-8 h-8"></div>
          <span className="ml-3 themed-text-secondary">Testing SportRadar API...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
          <h3 className="font-semibold text-red-400 mb-2">❌ API Test Failed</h3>
          <p className="text-sm text-red-300">{error}</p>
          
          <div className="mt-4 text-xs themed-text-tertiary">
            <p><strong>Possible solutions:</strong></p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Check if Next.js development server is running</li>
              <li>Verify API key is correct</li>
              <li>Check SportRadar endpoint URLs</li>
              <li>Ensure proxy route is working</li>
            </ul>
          </div>
        </div>
      )}

      {/* Success State */}
      {result && (
        <div className="space-y-4">
          <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
            <h3 className="font-semibold text-green-400 mb-2">✅ API Test Successful!</h3>
            <p className="text-sm text-green-300">SportRadar API is working through our proxy</p>
          </div>

          <div className="p-4 bg-slate-800 rounded-lg">
            <h4 className="font-semibold themed-text-primary mb-2">Response Data:</h4>
            
            {result.data ? (
              <div className="space-y-2">
                <div className="text-sm">
                  <strong>Data Keys:</strong> {Object.keys(result.data).join(', ')}
                </div>
                <div className="text-sm">
                  <strong>Endpoint:</strong> {result.endpoint}
                </div>
                <div className="text-sm">
                  <strong>Timestamp:</strong> {result.timestamp}
                </div>
                
                <details className="mt-3">
                  <summary className="cursor-pointer text-sm font-medium themed-text-secondary hover:themed-text-primary">
                    View Raw Data
                  </summary>
                  <pre className="mt-2 text-xs bg-slate-900 p-3 rounded overflow-auto max-h-64">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="text-sm">
                  <strong>Response Type:</strong> {Array.isArray(result) ? 'Array' : typeof result}
                </div>
                <div className="text-sm">
                  <strong>Length:</strong> {Array.isArray(result) ? result.length : 'N/A'}
                </div>
                
                <details className="mt-3">
                  <summary className="cursor-pointer text-sm font-medium themed-text-secondary hover:themed-text-primary">
                    View Raw Data
                  </summary>
                  <pre className="mt-2 text-xs bg-slate-900 p-3 rounded overflow-auto max-h-64">
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>

          {/* Next Steps */}
          <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <h4 className="font-semibold text-blue-400 mb-2">🚀 Next Steps</h4>
            <ul className="text-sm text-blue-300 space-y-1">
              <li>✅ API connectivity confirmed</li>
              <li>🔄 Update Players page to use real data</li>
              <li>🔄 Update Injuries page with real reports</li>
              <li>🔄 Replace all mock data with SportRadar data</li>
            </ul>
          </div>
        </div>
      )}

      {/* API Info */}
      <div className="mt-6 pt-6 border-t themed-border">
        <h4 className="font-semibold themed-text-primary mb-3">API Configuration</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong className="themed-text-secondary">API Key:</strong>
            <div className="font-mono text-xs themed-text-tertiary mt-1">
              aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN
            </div>
          </div>
          <div>
            <strong className="themed-text-secondary">Base URL:</strong>
            <div className="font-mono text-xs themed-text-tertiary mt-1">
              api.sportradar.com/rugby-league/trial/v3
            </div>
          </div>
          <div>
            <strong className="themed-text-secondary">Proxy Route:</strong>
            <div className="font-mono text-xs themed-text-tertiary mt-1">
              /api/sportradar-proxy
            </div>
          </div>
          <div>
            <strong className="themed-text-secondary">Status:</strong>
            <div className="text-xs mt-1">
              <span className="status-live">Trial Key Active</span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default SportRadarTest;
