# DO NOT EDIT THIS FILE!
#
# This file is generated from the CDP specification. If you need to make
# changes, edit the generator and regenerate all of the modules.
#
# CDP domain: Runtime
from __future__ import annotations
from .util import event_class, T_JSON_DICT
from dataclasses import dataclass
import enum
import typing

class ScriptId(str):
    '''
    Unique script identifier.
    '''
    def to_json(self) -> str:
        return self

    @classmethod
    def from_json(cls, json: str) -> ScriptId:
        return cls(json)

    def __repr__(self):
        return 'ScriptId({})'.format(super().__repr__())


@dataclass
class SerializationOptions:
    '''
    Represents options for serialization. Overrides ``generatePreview`` and ``returnByValue``.
    '''
    serialization: str

    #: Deep serialization depth. Default is full depth. Respected only in ``deep`` serialization mode.
    max_depth: typing.Optional[int] = None

    #: Embedder-specific parameters. For example if connected to V8 in Chrome these control DOM
    #: serialization via ``maxNodeDepth: integer`` and ``includeShadowTree: "none" `` "open" `` "all"``.
    #: Values can be only of type string or integer.
    additional_parameters: typing.Optional[dict] = None

    def to_json(self):
        json = dict()
        json['serialization'] = self.serialization
        if self.max_depth is not None:
            json['maxDepth'] = self.max_depth
        if self.additional_parameters is not None:
            json['additionalParameters'] = self.additional_parameters
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            serialization=str(json['serialization']),
            max_depth=int(json['maxDepth']) if 'maxDepth' in json else None,
            additional_parameters=dict(json['additionalParameters']) if 'additionalParameters' in json else None,
        )


@dataclass
class DeepSerializedValue:
    '''
    Represents deep serialized value.
    '''
    type_: str

    value: typing.Optional[typing.Any] = None

    object_id: typing.Optional[str] = None

    #: Set if value reference met more then once during serialization. In such
    #: case, value is provided only to one of the serialized values. Unique
    #: per value in the scope of one CDP call.
    weak_local_object_reference: typing.Optional[int] = None

    def to_json(self):
        json = dict()
        json['type'] = self.type_
        if self.value is not None:
            json['value'] = self.value
        if self.object_id is not None:
            json['objectId'] = self.object_id
        if self.weak_local_object_reference is not None:
            json['weakLocalObjectReference'] = self.weak_local_object_reference
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            type_=str(json['type']),
            value=json['value'] if 'value' in json else None,
            object_id=str(json['objectId']) if 'objectId' in json else None,
            weak_local_object_reference=int(json['weakLocalObjectReference']) if 'weakLocalObjectReference' in json else None,
        )


class RemoteObjectId(str):
    '''
    Unique object identifier.
    '''
    def to_json(self) -> str:
        return self

    @classmethod
    def from_json(cls, json: str) -> RemoteObjectId:
        return cls(json)

    def __repr__(self):
        return 'RemoteObjectId({})'.format(super().__repr__())


class UnserializableValue(str):
    '''
    Primitive value which cannot be JSON-stringified. Includes values ``-0``, ``NaN``, ``Infinity``,
    ``-Infinity``, and bigint literals.
    '''
    def to_json(self) -> str:
        return self

    @classmethod
    def from_json(cls, json: str) -> UnserializableValue:
        return cls(json)

    def __repr__(self):
        return 'UnserializableValue({})'.format(super().__repr__())


@dataclass
class RemoteObject:
    '''
    Mirror object referencing original JavaScript object.
    '''
    #: Object type.
    type_: str

    #: Object subtype hint. Specified for ``object`` type values only.
    #: NOTE: If you change anything here, make sure to also update
    #: ``subtype`` in ``ObjectPreview`` and ``PropertyPreview`` below.
    subtype: typing.Optional[str] = None

    #: Object class (constructor) name. Specified for ``object`` type values only.
    class_name: typing.Optional[str] = None

    #: Remote object value in case of primitive values or JSON values (if it was requested).
    value: typing.Optional[typing.Any] = None

    #: Primitive value which can not be JSON-stringified does not have ``value``, but gets this
    #: property.
    unserializable_value: typing.Optional[UnserializableValue] = None

    #: String representation of the object.
    description: typing.Optional[str] = None

    #: Deep serialized value.
    deep_serialized_value: typing.Optional[DeepSerializedValue] = None

    #: Unique object identifier (for non-primitive values).
    object_id: typing.Optional[RemoteObjectId] = None

    #: Preview containing abbreviated property values. Specified for ``object`` type values only.
    preview: typing.Optional[ObjectPreview] = None

    custom_preview: typing.Optional[CustomPreview] = None

    def to_json(self):
        json = dict()
        json['type'] = self.type_
        if self.subtype is not None:
            json['subtype'] = self.subtype
        if self.class_name is not None:
            json['className'] = self.class_name
        if self.value is not None:
            json['value'] = self.value
        if self.unserializable_value is not None:
            json['unserializableValue'] = self.unserializable_value.to_json()
        if self.description is not None:
            json['description'] = self.description
        if self.deep_serialized_value is not None:
            json['deepSerializedValue'] = self.deep_serialized_value.to_json()
        if self.object_id is not None:
            json['objectId'] = self.object_id.to_json()
        if self.preview is not None:
            json['preview'] = self.preview.to_json()
        if self.custom_preview is not None:
            json['customPreview'] = self.custom_preview.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            type_=str(json['type']),
            subtype=str(json['subtype']) if 'subtype' in json else None,
            class_name=str(json['className']) if 'className' in json else None,
            value=json['value'] if 'value' in json else None,
            unserializable_value=UnserializableValue.from_json(json['unserializableValue']) if 'unserializableValue' in json else None,
            description=str(json['description']) if 'description' in json else None,
            deep_serialized_value=DeepSerializedValue.from_json(json['deepSerializedValue']) if 'deepSerializedValue' in json else None,
            object_id=RemoteObjectId.from_json(json['objectId']) if 'objectId' in json else None,
            preview=ObjectPreview.from_json(json['preview']) if 'preview' in json else None,
            custom_preview=CustomPreview.from_json(json['customPreview']) if 'customPreview' in json else None,
        )


@dataclass
class CustomPreview:
    #: The JSON-stringified result of formatter.header(object, config) call.
    #: It contains json ML array that represents RemoteObject.
    header: str

    #: If formatter returns true as a result of formatter.hasBody call then bodyGetterId will
    #: contain RemoteObjectId for the function that returns result of formatter.body(object, config) call.
    #: The result value is json ML array.
    body_getter_id: typing.Optional[RemoteObjectId] = None

    def to_json(self):
        json = dict()
        json['header'] = self.header
        if self.body_getter_id is not None:
            json['bodyGetterId'] = self.body_getter_id.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            header=str(json['header']),
            body_getter_id=RemoteObjectId.from_json(json['bodyGetterId']) if 'bodyGetterId' in json else None,
        )


@dataclass
class ObjectPreview:
    '''
    Object containing abbreviated remote object value.
    '''
    #: Object type.
    type_: str

    #: True iff some of the properties or entries of the original object did not fit.
    overflow: bool

    #: List of the properties.
    properties: typing.List[PropertyPreview]

    #: Object subtype hint. Specified for ``object`` type values only.
    subtype: typing.Optional[str] = None

    #: String representation of the object.
    description: typing.Optional[str] = None

    #: List of the entries. Specified for ``map`` and ``set`` subtype values only.
    entries: typing.Optional[typing.List[EntryPreview]] = None

    def to_json(self):
        json = dict()
        json['type'] = self.type_
        json['overflow'] = self.overflow
        json['properties'] = [i.to_json() for i in self.properties]
        if self.subtype is not None:
            json['subtype'] = self.subtype
        if self.description is not None:
            json['description'] = self.description
        if self.entries is not None:
            json['entries'] = [i.to_json() for i in self.entries]
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            type_=str(json['type']),
            overflow=bool(json['overflow']),
            properties=[PropertyPreview.from_json(i) for i in json['properties']],
            subtype=str(json['subtype']) if 'subtype' in json else None,
            description=str(json['description']) if 'description' in json else None,
            entries=[EntryPreview.from_json(i) for i in json['entries']] if 'entries' in json else None,
        )


@dataclass
class PropertyPreview:
    #: Property name.
    name: str

    #: Object type. Accessor means that the property itself is an accessor property.
    type_: str

    #: User-friendly property value string.
    value: typing.Optional[str] = None

    #: Nested value preview.
    value_preview: typing.Optional[ObjectPreview] = None

    #: Object subtype hint. Specified for ``object`` type values only.
    subtype: typing.Optional[str] = None

    def to_json(self):
        json = dict()
        json['name'] = self.name
        json['type'] = self.type_
        if self.value is not None:
            json['value'] = self.value
        if self.value_preview is not None:
            json['valuePreview'] = self.value_preview.to_json()
        if self.subtype is not None:
            json['subtype'] = self.subtype
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            name=str(json['name']),
            type_=str(json['type']),
            value=str(json['value']) if 'value' in json else None,
            value_preview=ObjectPreview.from_json(json['valuePreview']) if 'valuePreview' in json else None,
            subtype=str(json['subtype']) if 'subtype' in json else None,
        )


@dataclass
class EntryPreview:
    #: Preview of the value.
    value: ObjectPreview

    #: Preview of the key. Specified for map-like collection entries.
    key: typing.Optional[ObjectPreview] = None

    def to_json(self):
        json = dict()
        json['value'] = self.value.to_json()
        if self.key is not None:
            json['key'] = self.key.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            value=ObjectPreview.from_json(json['value']),
            key=ObjectPreview.from_json(json['key']) if 'key' in json else None,
        )


@dataclass
class PropertyDescriptor:
    '''
    Object property descriptor.
    '''
    #: Property name or symbol description.
    name: str

    #: True if the type of this property descriptor may be changed and if the property may be
    #: deleted from the corresponding object.
    configurable: bool

    #: True if this property shows up during enumeration of the properties on the corresponding
    #: object.
    enumerable: bool

    #: The value associated with the property.
    value: typing.Optional[RemoteObject] = None

    #: True if the value associated with the property may be changed (data descriptors only).
    writable: typing.Optional[bool] = None

    #: A function which serves as a getter for the property, or ``undefined`` if there is no getter
    #: (accessor descriptors only).
    get: typing.Optional[RemoteObject] = None

    #: A function which serves as a setter for the property, or ``undefined`` if there is no setter
    #: (accessor descriptors only).
    set_: typing.Optional[RemoteObject] = None

    #: True if the result was thrown during the evaluation.
    was_thrown: typing.Optional[bool] = None

    #: True if the property is owned for the object.
    is_own: typing.Optional[bool] = None

    #: Property symbol object, if the property is of the ``symbol`` type.
    symbol: typing.Optional[RemoteObject] = None

    def to_json(self):
        json = dict()
        json['name'] = self.name
        json['configurable'] = self.configurable
        json['enumerable'] = self.enumerable
        if self.value is not None:
            json['value'] = self.value.to_json()
        if self.writable is not None:
            json['writable'] = self.writable
        if self.get is not None:
            json['get'] = self.get.to_json()
        if self.set_ is not None:
            json['set'] = self.set_.to_json()
        if self.was_thrown is not None:
            json['wasThrown'] = self.was_thrown
        if self.is_own is not None:
            json['isOwn'] = self.is_own
        if self.symbol is not None:
            json['symbol'] = self.symbol.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            name=str(json['name']),
            configurable=bool(json['configurable']),
            enumerable=bool(json['enumerable']),
            value=RemoteObject.from_json(json['value']) if 'value' in json else None,
            writable=bool(json['writable']) if 'writable' in json else None,
            get=RemoteObject.from_json(json['get']) if 'get' in json else None,
            set_=RemoteObject.from_json(json['set']) if 'set' in json else None,
            was_thrown=bool(json['wasThrown']) if 'wasThrown' in json else None,
            is_own=bool(json['isOwn']) if 'isOwn' in json else None,
            symbol=RemoteObject.from_json(json['symbol']) if 'symbol' in json else None,
        )


@dataclass
class InternalPropertyDescriptor:
    '''
    Object internal property descriptor. This property isn't normally visible in JavaScript code.
    '''
    #: Conventional property name.
    name: str

    #: The value associated with the property.
    value: typing.Optional[RemoteObject] = None

    def to_json(self):
        json = dict()
        json['name'] = self.name
        if self.value is not None:
            json['value'] = self.value.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            name=str(json['name']),
            value=RemoteObject.from_json(json['value']) if 'value' in json else None,
        )


@dataclass
class PrivatePropertyDescriptor:
    '''
    Object private field descriptor.
    '''
    #: Private property name.
    name: str

    #: The value associated with the private property.
    value: typing.Optional[RemoteObject] = None

    #: A function which serves as a getter for the private property,
    #: or ``undefined`` if there is no getter (accessor descriptors only).
    get: typing.Optional[RemoteObject] = None

    #: A function which serves as a setter for the private property,
    #: or ``undefined`` if there is no setter (accessor descriptors only).
    set_: typing.Optional[RemoteObject] = None

    def to_json(self):
        json = dict()
        json['name'] = self.name
        if self.value is not None:
            json['value'] = self.value.to_json()
        if self.get is not None:
            json['get'] = self.get.to_json()
        if self.set_ is not None:
            json['set'] = self.set_.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            name=str(json['name']),
            value=RemoteObject.from_json(json['value']) if 'value' in json else None,
            get=RemoteObject.from_json(json['get']) if 'get' in json else None,
            set_=RemoteObject.from_json(json['set']) if 'set' in json else None,
        )


@dataclass
class CallArgument:
    '''
    Represents function call argument. Either remote object id ``objectId``, primitive ``value``,
    unserializable primitive value or neither of (for undefined) them should be specified.
    '''
    #: Primitive value or serializable javascript object.
    value: typing.Optional[typing.Any] = None

    #: Primitive value which can not be JSON-stringified.
    unserializable_value: typing.Optional[UnserializableValue] = None

    #: Remote object handle.
    object_id: typing.Optional[RemoteObjectId] = None

    def to_json(self):
        json = dict()
        if self.value is not None:
            json['value'] = self.value
        if self.unserializable_value is not None:
            json['unserializableValue'] = self.unserializable_value.to_json()
        if self.object_id is not None:
            json['objectId'] = self.object_id.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            value=json['value'] if 'value' in json else None,
            unserializable_value=UnserializableValue.from_json(json['unserializableValue']) if 'unserializableValue' in json else None,
            object_id=RemoteObjectId.from_json(json['objectId']) if 'objectId' in json else None,
        )


class ExecutionContextId(int):
    '''
    Id of an execution context.
    '''
    def to_json(self) -> int:
        return self

    @classmethod
    def from_json(cls, json: int) -> ExecutionContextId:
        return cls(json)

    def __repr__(self):
        return 'ExecutionContextId({})'.format(super().__repr__())


@dataclass
class ExecutionContextDescription:
    '''
    Description of an isolated world.
    '''
    #: Unique id of the execution context. It can be used to specify in which execution context
    #: script evaluation should be performed.
    id_: ExecutionContextId

    #: Execution context origin.
    origin: str

    #: Human readable name describing given context.
    name: str

    #: A system-unique execution context identifier. Unlike the id, this is unique across
    #: multiple processes, so can be reliably used to identify specific context while backend
    #: performs a cross-process navigation.
    unique_id: str

    #: Embedder-specific auxiliary data likely matching {isDefault: boolean, type: 'default'``'isolated'``'worker', frameId: string}
    aux_data: typing.Optional[dict] = None

    def to_json(self):
        json = dict()
        json['id'] = self.id_.to_json()
        json['origin'] = self.origin
        json['name'] = self.name
        json['uniqueId'] = self.unique_id
        if self.aux_data is not None:
            json['auxData'] = self.aux_data
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            id_=ExecutionContextId.from_json(json['id']),
            origin=str(json['origin']),
            name=str(json['name']),
            unique_id=str(json['uniqueId']),
            aux_data=dict(json['auxData']) if 'auxData' in json else None,
        )


@dataclass
class ExceptionDetails:
    '''
    Detailed information about exception (or error) that was thrown during script compilation or
    execution.
    '''
    #: Exception id.
    exception_id: int

    #: Exception text, which should be used together with exception object when available.
    text: str

    #: Line number of the exception location (0-based).
    line_number: int

    #: Column number of the exception location (0-based).
    column_number: int

    #: Script ID of the exception location.
    script_id: typing.Optional[ScriptId] = None

    #: URL of the exception location, to be used when the script was not reported.
    url: typing.Optional[str] = None

    #: JavaScript stack trace if available.
    stack_trace: typing.Optional[StackTrace] = None

    #: Exception object if available.
    exception: typing.Optional[RemoteObject] = None

    #: Identifier of the context where exception happened.
    execution_context_id: typing.Optional[ExecutionContextId] = None

    #: Dictionary with entries of meta data that the client associated
    #: with this exception, such as information about associated network
    #: requests, etc.
    exception_meta_data: typing.Optional[dict] = None

    def to_json(self):
        json = dict()
        json['exceptionId'] = self.exception_id
        json['text'] = self.text
        json['lineNumber'] = self.line_number
        json['columnNumber'] = self.column_number
        if self.script_id is not None:
            json['scriptId'] = self.script_id.to_json()
        if self.url is not None:
            json['url'] = self.url
        if self.stack_trace is not None:
            json['stackTrace'] = self.stack_trace.to_json()
        if self.exception is not None:
            json['exception'] = self.exception.to_json()
        if self.execution_context_id is not None:
            json['executionContextId'] = self.execution_context_id.to_json()
        if self.exception_meta_data is not None:
            json['exceptionMetaData'] = self.exception_meta_data
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            exception_id=int(json['exceptionId']),
            text=str(json['text']),
            line_number=int(json['lineNumber']),
            column_number=int(json['columnNumber']),
            script_id=ScriptId.from_json(json['scriptId']) if 'scriptId' in json else None,
            url=str(json['url']) if 'url' in json else None,
            stack_trace=StackTrace.from_json(json['stackTrace']) if 'stackTrace' in json else None,
            exception=RemoteObject.from_json(json['exception']) if 'exception' in json else None,
            execution_context_id=ExecutionContextId.from_json(json['executionContextId']) if 'executionContextId' in json else None,
            exception_meta_data=dict(json['exceptionMetaData']) if 'exceptionMetaData' in json else None,
        )


class Timestamp(float):
    '''
    Number of milliseconds since epoch.
    '''
    def to_json(self) -> float:
        return self

    @classmethod
    def from_json(cls, json: float) -> Timestamp:
        return cls(json)

    def __repr__(self):
        return 'Timestamp({})'.format(super().__repr__())


class TimeDelta(float):
    '''
    Number of milliseconds.
    '''
    def to_json(self) -> float:
        return self

    @classmethod
    def from_json(cls, json: float) -> TimeDelta:
        return cls(json)

    def __repr__(self):
        return 'TimeDelta({})'.format(super().__repr__())


@dataclass
class CallFrame:
    '''
    Stack entry for runtime errors and assertions.
    '''
    #: JavaScript function name.
    function_name: str

    #: JavaScript script id.
    script_id: ScriptId

    #: JavaScript script name or url.
    url: str

    #: JavaScript script line number (0-based).
    line_number: int

    #: JavaScript script column number (0-based).
    column_number: int

    def to_json(self):
        json = dict()
        json['functionName'] = self.function_name
        json['scriptId'] = self.script_id.to_json()
        json['url'] = self.url
        json['lineNumber'] = self.line_number
        json['columnNumber'] = self.column_number
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            function_name=str(json['functionName']),
            script_id=ScriptId.from_json(json['scriptId']),
            url=str(json['url']),
            line_number=int(json['lineNumber']),
            column_number=int(json['columnNumber']),
        )


@dataclass
class StackTrace:
    '''
    Call frames for assertions or error messages.
    '''
    #: JavaScript function name.
    call_frames: typing.List[CallFrame]

    #: String label of this stack trace. For async traces this may be a name of the function that
    #: initiated the async call.
    description: typing.Optional[str] = None

    #: Asynchronous JavaScript stack trace that preceded this stack, if available.
    parent: typing.Optional[StackTrace] = None

    #: Asynchronous JavaScript stack trace that preceded this stack, if available.
    parent_id: typing.Optional[StackTraceId] = None

    def to_json(self):
        json = dict()
        json['callFrames'] = [i.to_json() for i in self.call_frames]
        if self.description is not None:
            json['description'] = self.description
        if self.parent is not None:
            json['parent'] = self.parent.to_json()
        if self.parent_id is not None:
            json['parentId'] = self.parent_id.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            call_frames=[CallFrame.from_json(i) for i in json['callFrames']],
            description=str(json['description']) if 'description' in json else None,
            parent=StackTrace.from_json(json['parent']) if 'parent' in json else None,
            parent_id=StackTraceId.from_json(json['parentId']) if 'parentId' in json else None,
        )


class UniqueDebuggerId(str):
    '''
    Unique identifier of current debugger.
    '''
    def to_json(self) -> str:
        return self

    @classmethod
    def from_json(cls, json: str) -> UniqueDebuggerId:
        return cls(json)

    def __repr__(self):
        return 'UniqueDebuggerId({})'.format(super().__repr__())


@dataclass
class StackTraceId:
    '''
    If ``debuggerId`` is set stack trace comes from another debugger and can be resolved there. This
    allows to track cross-debugger calls. See ``Runtime.StackTrace`` and ``Debugger.paused`` for usages.
    '''
    id_: str

    debugger_id: typing.Optional[UniqueDebuggerId] = None

    def to_json(self):
        json = dict()
        json['id'] = self.id_
        if self.debugger_id is not None:
            json['debuggerId'] = self.debugger_id.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            id_=str(json['id']),
            debugger_id=UniqueDebuggerId.from_json(json['debuggerId']) if 'debuggerId' in json else None,
        )


def await_promise(
        promise_object_id: RemoteObjectId,
        return_by_value: typing.Optional[bool] = None,
        generate_preview: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[RemoteObject, typing.Optional[ExceptionDetails]]]:
    '''
    Add handler to promise with given promise object id.

    :param promise_object_id: Identifier of the promise.
    :param return_by_value: *(Optional)* Whether the result is expected to be a JSON object that should be sent by value.
    :param generate_preview: *(Optional)* Whether preview should be generated for the result.
    :returns: A tuple with the following items:

        0. **result** - Promise result. Will contain rejected value if promise was rejected.
        1. **exceptionDetails** - *(Optional)* Exception details if stack strace is available.
    '''
    params: T_JSON_DICT = dict()
    params['promiseObjectId'] = promise_object_id.to_json()
    if return_by_value is not None:
        params['returnByValue'] = return_by_value
    if generate_preview is not None:
        params['generatePreview'] = generate_preview
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.awaitPromise',
        'params': params,
    }
    json = yield cmd_dict
    return (
        RemoteObject.from_json(json['result']),
        ExceptionDetails.from_json(json['exceptionDetails']) if 'exceptionDetails' in json else None
    )


def call_function_on(
        function_declaration: str,
        object_id: typing.Optional[RemoteObjectId] = None,
        arguments: typing.Optional[typing.List[CallArgument]] = None,
        silent: typing.Optional[bool] = None,
        return_by_value: typing.Optional[bool] = None,
        generate_preview: typing.Optional[bool] = None,
        user_gesture: typing.Optional[bool] = None,
        await_promise: typing.Optional[bool] = None,
        execution_context_id: typing.Optional[ExecutionContextId] = None,
        object_group: typing.Optional[str] = None,
        throw_on_side_effect: typing.Optional[bool] = None,
        unique_context_id: typing.Optional[str] = None,
        serialization_options: typing.Optional[SerializationOptions] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[RemoteObject, typing.Optional[ExceptionDetails]]]:
    '''
    Calls function with given declaration on the given object. Object group of the result is
    inherited from the target object.

    :param function_declaration: Declaration of the function to call.
    :param object_id: *(Optional)* Identifier of the object to call function on. Either objectId or executionContextId should be specified.
    :param arguments: *(Optional)* Call arguments. All call arguments must belong to the same JavaScript world as the target object.
    :param silent: *(Optional)* In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides ```setPauseOnException```` state.
    :param return_by_value: *(Optional)* Whether the result is expected to be a JSON object which should be sent by value. Can be overriden by ````serializationOptions````.
    :param generate_preview: **(EXPERIMENTAL)** *(Optional)* Whether preview should be generated for the result.
    :param user_gesture: *(Optional)* Whether execution should be treated as initiated by user in the UI.
    :param await_promise: *(Optional)* Whether execution should ````await```` for resulting value and return once awaited promise is resolved.
    :param execution_context_id: *(Optional)* Specifies execution context which global object will be used to call function on. Either executionContextId or objectId should be specified.
    :param object_group: *(Optional)* Symbolic group name that can be used to release multiple objects. If objectGroup is not specified and objectId is, objectGroup will be inherited from object.
    :param throw_on_side_effect: **(EXPERIMENTAL)** *(Optional)* Whether to throw an exception if side effect cannot be ruled out during evaluation.
    :param unique_context_id: **(EXPERIMENTAL)** *(Optional)* An alternative way to specify the execution context to call function on. Compared to contextId that may be reused across processes, this is guaranteed to be system-unique, so it can be used to prevent accidental function call in context different than intended (e.g. as a result of navigation across process boundaries). This is mutually exclusive with ````executionContextId````.
    :param serialization_options: **(EXPERIMENTAL)** *(Optional)* Specifies the result serialization. If provided, overrides ````generatePreview```` and ````returnByValue```.
    :returns: A tuple with the following items:

        0. **result** - Call result.
        1. **exceptionDetails** - *(Optional)* Exception details.
    '''
    params: T_JSON_DICT = dict()
    params['functionDeclaration'] = function_declaration
    if object_id is not None:
        params['objectId'] = object_id.to_json()
    if arguments is not None:
        params['arguments'] = [i.to_json() for i in arguments]
    if silent is not None:
        params['silent'] = silent
    if return_by_value is not None:
        params['returnByValue'] = return_by_value
    if generate_preview is not None:
        params['generatePreview'] = generate_preview
    if user_gesture is not None:
        params['userGesture'] = user_gesture
    if await_promise is not None:
        params['awaitPromise'] = await_promise
    if execution_context_id is not None:
        params['executionContextId'] = execution_context_id.to_json()
    if object_group is not None:
        params['objectGroup'] = object_group
    if throw_on_side_effect is not None:
        params['throwOnSideEffect'] = throw_on_side_effect
    if unique_context_id is not None:
        params['uniqueContextId'] = unique_context_id
    if serialization_options is not None:
        params['serializationOptions'] = serialization_options.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.callFunctionOn',
        'params': params,
    }
    json = yield cmd_dict
    return (
        RemoteObject.from_json(json['result']),
        ExceptionDetails.from_json(json['exceptionDetails']) if 'exceptionDetails' in json else None
    )


def compile_script(
        expression: str,
        source_url: str,
        persist_script: bool,
        execution_context_id: typing.Optional[ExecutionContextId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[typing.Optional[ScriptId], typing.Optional[ExceptionDetails]]]:
    '''
    Compiles expression.

    :param expression: Expression to compile.
    :param source_url: Source url to be set for the script.
    :param persist_script: Specifies whether the compiled script should be persisted.
    :param execution_context_id: *(Optional)* Specifies in which execution context to perform script run. If the parameter is omitted the evaluation will be performed in the context of the inspected page.
    :returns: A tuple with the following items:

        0. **scriptId** - *(Optional)* Id of the script.
        1. **exceptionDetails** - *(Optional)* Exception details.
    '''
    params: T_JSON_DICT = dict()
    params['expression'] = expression
    params['sourceURL'] = source_url
    params['persistScript'] = persist_script
    if execution_context_id is not None:
        params['executionContextId'] = execution_context_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.compileScript',
        'params': params,
    }
    json = yield cmd_dict
    return (
        ScriptId.from_json(json['scriptId']) if 'scriptId' in json else None,
        ExceptionDetails.from_json(json['exceptionDetails']) if 'exceptionDetails' in json else None
    )


def disable() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Disables reporting of execution contexts creation.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.disable',
    }
    json = yield cmd_dict


def discard_console_entries() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Discards collected exceptions and console API calls.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.discardConsoleEntries',
    }
    json = yield cmd_dict


def enable() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Enables reporting of execution contexts creation by means of ``executionContextCreated`` event.
    When the reporting gets enabled the event will be sent immediately for each existing execution
    context.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.enable',
    }
    json = yield cmd_dict


def evaluate(
        expression: str,
        object_group: typing.Optional[str] = None,
        include_command_line_api: typing.Optional[bool] = None,
        silent: typing.Optional[bool] = None,
        context_id: typing.Optional[ExecutionContextId] = None,
        return_by_value: typing.Optional[bool] = None,
        generate_preview: typing.Optional[bool] = None,
        user_gesture: typing.Optional[bool] = None,
        await_promise: typing.Optional[bool] = None,
        throw_on_side_effect: typing.Optional[bool] = None,
        timeout: typing.Optional[TimeDelta] = None,
        disable_breaks: typing.Optional[bool] = None,
        repl_mode: typing.Optional[bool] = None,
        allow_unsafe_eval_blocked_by_csp: typing.Optional[bool] = None,
        unique_context_id: typing.Optional[str] = None,
        serialization_options: typing.Optional[SerializationOptions] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[RemoteObject, typing.Optional[ExceptionDetails]]]:
    '''
    Evaluates expression on global object.

    :param expression: Expression to evaluate.
    :param object_group: *(Optional)* Symbolic group name that can be used to release multiple objects.
    :param include_command_line_api: *(Optional)* Determines whether Command Line API should be available during the evaluation.
    :param silent: *(Optional)* In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides ```setPauseOnException```` state.
    :param context_id: *(Optional)* Specifies in which execution context to perform evaluation. If the parameter is omitted the evaluation will be performed in the context of the inspected page. This is mutually exclusive with ````uniqueContextId````, which offers an alternative way to identify the execution context that is more reliable in a multi-process environment.
    :param return_by_value: *(Optional)* Whether the result is expected to be a JSON object that should be sent by value.
    :param generate_preview: **(EXPERIMENTAL)** *(Optional)* Whether preview should be generated for the result.
    :param user_gesture: *(Optional)* Whether execution should be treated as initiated by user in the UI.
    :param await_promise: *(Optional)* Whether execution should ````await```` for resulting value and return once awaited promise is resolved.
    :param throw_on_side_effect: **(EXPERIMENTAL)** *(Optional)* Whether to throw an exception if side effect cannot be ruled out during evaluation. This implies ````disableBreaks```` below.
    :param timeout: **(EXPERIMENTAL)** *(Optional)* Terminate execution after timing out (number of milliseconds).
    :param disable_breaks: **(EXPERIMENTAL)** *(Optional)* Disable breakpoints during execution.
    :param repl_mode: **(EXPERIMENTAL)** *(Optional)* Setting this flag to true enables ````let```` re-declaration and top-level ````await````. Note that ````let```` variables can only be re-declared if they originate from ````replMode```` themselves.
    :param allow_unsafe_eval_blocked_by_csp: **(EXPERIMENTAL)** *(Optional)* The Content Security Policy (CSP) for the target might block 'unsafe-eval' which includes eval(), Function(), setTimeout() and setInterval() when called with non-callable arguments. This flag bypasses CSP for this evaluation and allows unsafe-eval. Defaults to true.
    :param unique_context_id: **(EXPERIMENTAL)** *(Optional)* An alternative way to specify the execution context to evaluate in. Compared to contextId that may be reused across processes, this is guaranteed to be system-unique, so it can be used to prevent accidental evaluation of the expression in context different than intended (e.g. as a result of navigation across process boundaries). This is mutually exclusive with ````contextId````.
    :param serialization_options: **(EXPERIMENTAL)** *(Optional)* Specifies the result serialization. If provided, overrides ````generatePreview```` and ````returnByValue```.
    :returns: A tuple with the following items:

        0. **result** - Evaluation result.
        1. **exceptionDetails** - *(Optional)* Exception details.
    '''
    params: T_JSON_DICT = dict()
    params['expression'] = expression
    if object_group is not None:
        params['objectGroup'] = object_group
    if include_command_line_api is not None:
        params['includeCommandLineAPI'] = include_command_line_api
    if silent is not None:
        params['silent'] = silent
    if context_id is not None:
        params['contextId'] = context_id.to_json()
    if return_by_value is not None:
        params['returnByValue'] = return_by_value
    if generate_preview is not None:
        params['generatePreview'] = generate_preview
    if user_gesture is not None:
        params['userGesture'] = user_gesture
    if await_promise is not None:
        params['awaitPromise'] = await_promise
    if throw_on_side_effect is not None:
        params['throwOnSideEffect'] = throw_on_side_effect
    if timeout is not None:
        params['timeout'] = timeout.to_json()
    if disable_breaks is not None:
        params['disableBreaks'] = disable_breaks
    if repl_mode is not None:
        params['replMode'] = repl_mode
    if allow_unsafe_eval_blocked_by_csp is not None:
        params['allowUnsafeEvalBlockedByCSP'] = allow_unsafe_eval_blocked_by_csp
    if unique_context_id is not None:
        params['uniqueContextId'] = unique_context_id
    if serialization_options is not None:
        params['serializationOptions'] = serialization_options.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.evaluate',
        'params': params,
    }
    json = yield cmd_dict
    return (
        RemoteObject.from_json(json['result']),
        ExceptionDetails.from_json(json['exceptionDetails']) if 'exceptionDetails' in json else None
    )


def get_isolate_id() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,str]:
    '''
    Returns the isolate id.

    **EXPERIMENTAL**

    :returns: The isolate id.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.getIsolateId',
    }
    json = yield cmd_dict
    return str(json['id'])


def get_heap_usage() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[float, float, float, float]]:
    '''
    Returns the JavaScript heap usage.
    It is the total usage of the corresponding isolate not scoped to a particular Runtime.

    **EXPERIMENTAL**

    :returns: A tuple with the following items:

        0. **usedSize** - Used JavaScript heap size in bytes.
        1. **totalSize** - Allocated JavaScript heap size in bytes.
        2. **embedderHeapUsedSize** - Used size in bytes in the embedder's garbage-collected heap.
        3. **backingStorageSize** - Size in bytes of backing storage for array buffers and external strings.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.getHeapUsage',
    }
    json = yield cmd_dict
    return (
        float(json['usedSize']),
        float(json['totalSize']),
        float(json['embedderHeapUsedSize']),
        float(json['backingStorageSize'])
    )


def get_properties(
        object_id: RemoteObjectId,
        own_properties: typing.Optional[bool] = None,
        accessor_properties_only: typing.Optional[bool] = None,
        generate_preview: typing.Optional[bool] = None,
        non_indexed_properties_only: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[typing.List[PropertyDescriptor], typing.Optional[typing.List[InternalPropertyDescriptor]], typing.Optional[typing.List[PrivatePropertyDescriptor]], typing.Optional[ExceptionDetails]]]:
    '''
    Returns properties of a given object. Object group of the result is inherited from the target
    object.

    :param object_id: Identifier of the object to return properties for.
    :param own_properties: *(Optional)* If true, returns properties belonging only to the element itself, not to its prototype chain.
    :param accessor_properties_only: **(EXPERIMENTAL)** *(Optional)* If true, returns accessor properties (with getter/setter) only; internal properties are not returned either.
    :param generate_preview: **(EXPERIMENTAL)** *(Optional)* Whether preview should be generated for the results.
    :param non_indexed_properties_only: **(EXPERIMENTAL)** *(Optional)* If true, returns non-indexed properties only.
    :returns: A tuple with the following items:

        0. **result** - Object properties.
        1. **internalProperties** - *(Optional)* Internal object properties (only of the element itself).
        2. **privateProperties** - *(Optional)* Object private properties.
        3. **exceptionDetails** - *(Optional)* Exception details.
    '''
    params: T_JSON_DICT = dict()
    params['objectId'] = object_id.to_json()
    if own_properties is not None:
        params['ownProperties'] = own_properties
    if accessor_properties_only is not None:
        params['accessorPropertiesOnly'] = accessor_properties_only
    if generate_preview is not None:
        params['generatePreview'] = generate_preview
    if non_indexed_properties_only is not None:
        params['nonIndexedPropertiesOnly'] = non_indexed_properties_only
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.getProperties',
        'params': params,
    }
    json = yield cmd_dict
    return (
        [PropertyDescriptor.from_json(i) for i in json['result']],
        [InternalPropertyDescriptor.from_json(i) for i in json['internalProperties']] if 'internalProperties' in json else None,
        [PrivatePropertyDescriptor.from_json(i) for i in json['privateProperties']] if 'privateProperties' in json else None,
        ExceptionDetails.from_json(json['exceptionDetails']) if 'exceptionDetails' in json else None
    )


def global_lexical_scope_names(
        execution_context_id: typing.Optional[ExecutionContextId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[str]]:
    '''
    Returns all let, const and class variables from global scope.

    :param execution_context_id: *(Optional)* Specifies in which execution context to lookup global scope variables.
    :returns: 
    '''
    params: T_JSON_DICT = dict()
    if execution_context_id is not None:
        params['executionContextId'] = execution_context_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.globalLexicalScopeNames',
        'params': params,
    }
    json = yield cmd_dict
    return [str(i) for i in json['names']]


def query_objects(
        prototype_object_id: RemoteObjectId,
        object_group: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,RemoteObject]:
    '''
    :param prototype_object_id: Identifier of the prototype to return objects for.
    :param object_group: *(Optional)* Symbolic group name that can be used to release the results.
    :returns: Array with objects.
    '''
    params: T_JSON_DICT = dict()
    params['prototypeObjectId'] = prototype_object_id.to_json()
    if object_group is not None:
        params['objectGroup'] = object_group
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.queryObjects',
        'params': params,
    }
    json = yield cmd_dict
    return RemoteObject.from_json(json['objects'])


def release_object(
        object_id: RemoteObjectId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Releases remote object with given id.

    :param object_id: Identifier of the object to release.
    '''
    params: T_JSON_DICT = dict()
    params['objectId'] = object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.releaseObject',
        'params': params,
    }
    json = yield cmd_dict


def release_object_group(
        object_group: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Releases all remote objects that belong to a given group.

    :param object_group: Symbolic object group name.
    '''
    params: T_JSON_DICT = dict()
    params['objectGroup'] = object_group
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.releaseObjectGroup',
        'params': params,
    }
    json = yield cmd_dict


def run_if_waiting_for_debugger() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Tells inspected instance to run if it was waiting for debugger to attach.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.runIfWaitingForDebugger',
    }
    json = yield cmd_dict


def run_script(
        script_id: ScriptId,
        execution_context_id: typing.Optional[ExecutionContextId] = None,
        object_group: typing.Optional[str] = None,
        silent: typing.Optional[bool] = None,
        include_command_line_api: typing.Optional[bool] = None,
        return_by_value: typing.Optional[bool] = None,
        generate_preview: typing.Optional[bool] = None,
        await_promise: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[RemoteObject, typing.Optional[ExceptionDetails]]]:
    '''
    Runs script with given id in a given context.

    :param script_id: Id of the script to run.
    :param execution_context_id: *(Optional)* Specifies in which execution context to perform script run. If the parameter is omitted the evaluation will be performed in the context of the inspected page.
    :param object_group: *(Optional)* Symbolic group name that can be used to release multiple objects.
    :param silent: *(Optional)* In silent mode exceptions thrown during evaluation are not reported and do not pause execution. Overrides ```setPauseOnException```` state.
    :param include_command_line_api: *(Optional)* Determines whether Command Line API should be available during the evaluation.
    :param return_by_value: *(Optional)* Whether the result is expected to be a JSON object which should be sent by value.
    :param generate_preview: *(Optional)* Whether preview should be generated for the result.
    :param await_promise: *(Optional)* Whether execution should ````await``` for resulting value and return once awaited promise is resolved.
    :returns: A tuple with the following items:

        0. **result** - Run result.
        1. **exceptionDetails** - *(Optional)* Exception details.
    '''
    params: T_JSON_DICT = dict()
    params['scriptId'] = script_id.to_json()
    if execution_context_id is not None:
        params['executionContextId'] = execution_context_id.to_json()
    if object_group is not None:
        params['objectGroup'] = object_group
    if silent is not None:
        params['silent'] = silent
    if include_command_line_api is not None:
        params['includeCommandLineAPI'] = include_command_line_api
    if return_by_value is not None:
        params['returnByValue'] = return_by_value
    if generate_preview is not None:
        params['generatePreview'] = generate_preview
    if await_promise is not None:
        params['awaitPromise'] = await_promise
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.runScript',
        'params': params,
    }
    json = yield cmd_dict
    return (
        RemoteObject.from_json(json['result']),
        ExceptionDetails.from_json(json['exceptionDetails']) if 'exceptionDetails' in json else None
    )


def set_async_call_stack_depth(
        max_depth: int
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Enables or disables async call stacks tracking.

    :param max_depth: Maximum depth of async call stacks. Setting to ```0``` will effectively disable collecting async call stacks (default).
    '''
    params: T_JSON_DICT = dict()
    params['maxDepth'] = max_depth
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.setAsyncCallStackDepth',
        'params': params,
    }
    json = yield cmd_dict


def set_custom_object_formatter_enabled(
        enabled: bool
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''


    **EXPERIMENTAL**

    :param enabled:
    '''
    params: T_JSON_DICT = dict()
    params['enabled'] = enabled
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.setCustomObjectFormatterEnabled',
        'params': params,
    }
    json = yield cmd_dict


def set_max_call_stack_size_to_capture(
        size: int
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''


    **EXPERIMENTAL**

    :param size:
    '''
    params: T_JSON_DICT = dict()
    params['size'] = size
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.setMaxCallStackSizeToCapture',
        'params': params,
    }
    json = yield cmd_dict


def terminate_execution() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Terminate current or next JavaScript execution.
    Will cancel the termination when the outer-most script execution ends.

    **EXPERIMENTAL**
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.terminateExecution',
    }
    json = yield cmd_dict


def add_binding(
        name: str,
        execution_context_id: typing.Optional[ExecutionContextId] = None,
        execution_context_name: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    If executionContextId is empty, adds binding with the given name on the
    global objects of all inspected contexts, including those created later,
    bindings survive reloads.
    Binding function takes exactly one argument, this argument should be string,
    in case of any other input, function throws an exception.
    Each binding function call produces Runtime.bindingCalled notification.

    :param name:
    :param execution_context_id: **(EXPERIMENTAL)** *(Optional)* If specified, the binding would only be exposed to the specified execution context. If omitted and ```executionContextName```` is not set, the binding is exposed to all execution contexts of the target. This parameter is mutually exclusive with ````executionContextName````. Deprecated in favor of ````executionContextName```` due to an unclear use case and bugs in implementation (crbug.com/1169639). ````executionContextId```` will be removed in the future.
    :param execution_context_name: *(Optional)* If specified, the binding is exposed to the executionContext with matching name, even for contexts created after the binding is added. See also ````ExecutionContext.name```` and ````worldName```` parameter to ````Page.addScriptToEvaluateOnNewDocument````. This parameter is mutually exclusive with ````executionContextId```.
    '''
    params: T_JSON_DICT = dict()
    params['name'] = name
    if execution_context_id is not None:
        params['executionContextId'] = execution_context_id.to_json()
    if execution_context_name is not None:
        params['executionContextName'] = execution_context_name
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.addBinding',
        'params': params,
    }
    json = yield cmd_dict


def remove_binding(
        name: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    This method does not remove binding function from global object but
    unsubscribes current runtime agent from Runtime.bindingCalled notifications.

    :param name:
    '''
    params: T_JSON_DICT = dict()
    params['name'] = name
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.removeBinding',
        'params': params,
    }
    json = yield cmd_dict


def get_exception_details(
        error_object_id: RemoteObjectId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Optional[ExceptionDetails]]:
    '''
    This method tries to lookup and populate exception details for a
    JavaScript Error object.
    Note that the stackTrace portion of the resulting exceptionDetails will
    only be populated if the Runtime domain was enabled at the time when the
    Error was thrown.

    **EXPERIMENTAL**

    :param error_object_id: The error object for which to resolve the exception details.
    :returns: 
    '''
    params: T_JSON_DICT = dict()
    params['errorObjectId'] = error_object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'Runtime.getExceptionDetails',
        'params': params,
    }
    json = yield cmd_dict
    return ExceptionDetails.from_json(json['exceptionDetails']) if 'exceptionDetails' in json else None


@event_class('Runtime.bindingCalled')
@dataclass
class BindingCalled:
    '''
    **EXPERIMENTAL**

    Notification is issued every time when binding is called.
    '''
    name: str
    payload: str
    #: Identifier of the context where the call was made.
    execution_context_id: ExecutionContextId

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> BindingCalled:
        return cls(
            name=str(json['name']),
            payload=str(json['payload']),
            execution_context_id=ExecutionContextId.from_json(json['executionContextId'])
        )


@event_class('Runtime.consoleAPICalled')
@dataclass
class ConsoleAPICalled:
    '''
    Issued when console API was called.
    '''
    #: Type of the call.
    type_: str
    #: Call arguments.
    args: typing.List[RemoteObject]
    #: Identifier of the context where the call was made.
    execution_context_id: ExecutionContextId
    #: Call timestamp.
    timestamp: Timestamp
    #: Stack trace captured when the call was made. The async stack chain is automatically reported for
    #: the following call types: ``assert``, ``error``, ``trace``, ``warning``. For other types the async call
    #: chain can be retrieved using ``Debugger.getStackTrace`` and ``stackTrace.parentId`` field.
    stack_trace: typing.Optional[StackTrace]
    #: Console context descriptor for calls on non-default console context (not console.*):
    #: 'anonymous#unique-logger-id' for call on unnamed context, 'name#unique-logger-id' for call
    #: on named context.
    context: typing.Optional[str]

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ConsoleAPICalled:
        return cls(
            type_=str(json['type']),
            args=[RemoteObject.from_json(i) for i in json['args']],
            execution_context_id=ExecutionContextId.from_json(json['executionContextId']),
            timestamp=Timestamp.from_json(json['timestamp']),
            stack_trace=StackTrace.from_json(json['stackTrace']) if 'stackTrace' in json else None,
            context=str(json['context']) if 'context' in json else None
        )


@event_class('Runtime.exceptionRevoked')
@dataclass
class ExceptionRevoked:
    '''
    Issued when unhandled exception was revoked.
    '''
    #: Reason describing why exception was revoked.
    reason: str
    #: The id of revoked exception, as reported in ``exceptionThrown``.
    exception_id: int

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ExceptionRevoked:
        return cls(
            reason=str(json['reason']),
            exception_id=int(json['exceptionId'])
        )


@event_class('Runtime.exceptionThrown')
@dataclass
class ExceptionThrown:
    '''
    Issued when exception was thrown and unhandled.
    '''
    #: Timestamp of the exception.
    timestamp: Timestamp
    exception_details: ExceptionDetails

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ExceptionThrown:
        return cls(
            timestamp=Timestamp.from_json(json['timestamp']),
            exception_details=ExceptionDetails.from_json(json['exceptionDetails'])
        )


@event_class('Runtime.executionContextCreated')
@dataclass
class ExecutionContextCreated:
    '''
    Issued when new execution context is created.
    '''
    #: A newly created execution context.
    context: ExecutionContextDescription

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ExecutionContextCreated:
        return cls(
            context=ExecutionContextDescription.from_json(json['context'])
        )


@event_class('Runtime.executionContextDestroyed')
@dataclass
class ExecutionContextDestroyed:
    '''
    Issued when execution context is destroyed.
    '''
    #: Id of the destroyed context
    execution_context_id: ExecutionContextId
    #: Unique Id of the destroyed context
    execution_context_unique_id: str

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ExecutionContextDestroyed:
        return cls(
            execution_context_id=ExecutionContextId.from_json(json['executionContextId']),
            execution_context_unique_id=str(json['executionContextUniqueId'])
        )


@event_class('Runtime.executionContextsCleared')
@dataclass
class ExecutionContextsCleared:
    '''
    Issued when all executionContexts were cleared in browser
    '''


    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ExecutionContextsCleared:
        return cls(

        )


@event_class('Runtime.inspectRequested')
@dataclass
class InspectRequested:
    '''
    Issued when object should be inspected (for example, as a result of inspect() command line API
    call).
    '''
    object_: RemoteObject
    hints: dict
    #: Identifier of the context where the call was made.
    execution_context_id: typing.Optional[ExecutionContextId]

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> InspectRequested:
        return cls(
            object_=RemoteObject.from_json(json['object']),
            hints=dict(json['hints']),
            execution_context_id=ExecutionContextId.from_json(json['executionContextId']) if 'executionContextId' in json else None
        )
