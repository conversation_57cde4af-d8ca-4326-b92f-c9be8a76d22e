#!/usr/bin/env python3
"""
Simple Test Server for FantasyPro
Quick demo server to verify the setup works for client presentation.
"""

import os
import sys
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime

# Set environment variables for testing
os.environ["OPENAI_API_KEY"] = "not-needed"
os.environ["OPENAI_API_BASE"] = "http://localhost:1234/v1"

# Create FastAPI app
app = FastAPI(
    title="FantasyPro Demo API",
    description="AI-Integrated Fantasy Sports Platform - Demo Server",
    version="1.0.0-demo",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Sample data for demo
DEMO_PLAYERS = [
    {
        "id": 1,
        "name": "<PERSON>",
        "position": "Halfback",
        "team": "Penrith Panthers",
        "price": 750000,
        "points": 1250,
        "form": 8.5,
        "ownership": 45.2,
        "value_score": 9.1,
        "recommendation": "Strong Buy"
    },
    {
        "id": 2,
        "name": "<PERSON>co",
        "position": "Fullback",
        "team": "Sydney Roosters",
        "price": 820000,
        "points": 1380,
        "form": 9.2,
        "ownership": 52.8,
        "value_score": 8.8,
        "recommendation": "Hold"
    },
    {
        "id": 3,
        "name": "Daly Cherry-Evans",
        "position": "Halfback",
        "team": "Manly Sea Eagles",
        "price": 680000,
        "points": 1150,
        "form": 7.8,
        "ownership": 38.5,
        "value_score": 8.5,
        "recommendation": "Buy"
    }
]

@app.get("/")
async def root():
    """Root endpoint with project info."""
    return {
        "message": "FantasyPro Demo API",
        "status": "running",
        "version": "1.0.0-demo",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "health": "/health",
            "players": "/players",
            "docs": "/docs",
            "demo": "/demo"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0-demo",
        "mode": "demo"
    }

@app.get("/players")
async def get_players():
    """Get demo player data."""
    return {
        "players": DEMO_PLAYERS,
        "count": len(DEMO_PLAYERS),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/players/{player_id}")
async def get_player(player_id: int):
    """Get specific player by ID."""
    for player in DEMO_PLAYERS:
        if player["id"] == player_id:
            return player
    return {"error": "Player not found"}

@app.get("/demo")
async def demo_endpoint():
    """Demo endpoint showing AI integration."""
    return {
        "message": "FantasyPro AI Integration Demo",
        "features": [
            "Player Performance Analysis",
            "AI-Driven Recommendations",
            "Real-time Data Processing",
            "Multi-Agent Collaboration",
            "Advanced Analytics Dashboard"
        ],
        "tech_stack": [
            "Python FastAPI",
            "PraisonAI Agents",
            "React/Next.js Frontend",
            "PostgreSQL Database",
            "Redis Caching",
            "Docker Deployment"
        ],
        "status": "ready_for_demo",
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    print("🏈 FantasyPro Demo Server Starting...")
    print("📊 API Documentation: http://localhost:8000/docs")
    print("🏈 Demo Dashboard: http://localhost:8000/demo")
    print("👥 Players API: http://localhost:8000/players")
    print("🔍 Health Check: http://localhost:8000/health")
    print("=" * 60)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    ) 