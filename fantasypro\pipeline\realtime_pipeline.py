#!/usr/bin/env python3
"""
FantasyPro Real-time Data Pipeline

Real-time data ingestion, processing, and notification system for live game updates,
player changes, news, and other time-sensitive fantasy sports information.
"""

import os
import sys
import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
import websockets
from concurrent.futures import ThreadPoolExecutor

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logging_config import setup_logging

logger = setup_logging(__name__)

class EventType(Enum):
    """Types of real-time events."""
    PLAYER_SCORE_UPDATE = "player_score_update"
    INJURY_REPORT = "injury_report"
    TEAM_NEWS = "team_news"
    PRICE_CHANGE = "price_change"
    GAME_START = "game_start"
    GAME_END = "game_end"
    WEATHER_UPDATE = "weather_update"
    NEWS_ALERT = "news_alert"

@dataclass
class RealTimeEvent:
    """Real-time event data structure."""
    event_type: EventType
    timestamp: datetime
    data: Dict[str, Any]
    priority: str = "medium"  # low, medium, high, critical
    source: str = "unknown"
    event_id: Optional[str] = None

class DataSource:
    """Base class for real-time data sources."""
    
    def __init__(self, name: str, url: str, update_interval: int = 30):
        self.name = name
        self.url = url
        self.update_interval = update_interval
        self.is_active = False
        self.last_update = None
        self.error_count = 0
        self.max_errors = 5
        
    async def start(self):
        """Start the data source."""
        self.is_active = True
        logger.info(f"Started data source: {self.name}")
        
    async def stop(self):
        """Stop the data source."""
        self.is_active = False
        logger.info(f"Stopped data source: {self.name}")
        
    async def fetch_data(self) -> List[RealTimeEvent]:
        """Fetch data from the source. Override in subclasses."""
        raise NotImplementedError
        
    def handle_error(self, error: Exception):
        """Handle errors from data source."""
        self.error_count += 1
        logger.error(f"Error in {self.name}: {error}")
        
        if self.error_count >= self.max_errors:
            logger.warning(f"Max errors reached for {self.name}, deactivating")
            self.is_active = False

class NRLLiveDataSource(DataSource):
    """Real-time NRL game data source."""
    
    def __init__(self):
        super().__init__("NRL Live Data", "https://api.nrl.com/live", 15)
        
    async def fetch_data(self) -> List[RealTimeEvent]:
        """Fetch live NRL game data."""
        events = []
        
        try:
            # Mock live data for demonstration
            current_time = datetime.now()
            
            # Simulate live score updates
            if current_time.second % 30 == 0:  # Every 30 seconds
                events.append(RealTimeEvent(
                    event_type=EventType.PLAYER_SCORE_UPDATE,
                    timestamp=current_time,
                    data={
                        "player_id": 1,
                        "player_name": "Nathan Cleary",
                        "score_type": "try_assist",
                        "points": 4,
                        "game_time": "65:23",
                        "total_points": 45
                    },
                    priority="high",
                    source=self.name
                ))
            
            # Simulate injury reports
            if current_time.minute % 15 == 0 and current_time.second == 0:
                events.append(RealTimeEvent(
                    event_type=EventType.INJURY_REPORT,
                    timestamp=current_time,
                    data={
                        "player_id": 3,
                        "player_name": "Kalyn Ponga",
                        "injury_type": "head knock",
                        "severity": "minor",
                        "status": "HIA assessment",
                        "return_probability": 0.7
                    },
                    priority="critical",
                    source=self.name
                ))
            
            self.last_update = current_time
            self.error_count = 0
            
        except Exception as e:
            self.handle_error(e)
            
        return events

class NewsDataSource(DataSource):
    """Real-time sports news data source."""
    
    def __init__(self):
        super().__init__("Sports News", "https://api.newsapi.org/v2/sports", 300)
        
    async def fetch_data(self) -> List[RealTimeEvent]:
        """Fetch sports news updates."""
        events = []
        
        try:
            # Mock news data
            current_time = datetime.now()
            
            if current_time.minute % 10 == 0 and current_time.second == 0:
                events.append(RealTimeEvent(
                    event_type=EventType.NEWS_ALERT,
                    timestamp=current_time,
                    data={
                        "headline": "Star player ruled out for next match",
                        "summary": "Key fantasy player will miss upcoming game due to suspension",
                        "impact_players": [2, 5],
                        "sentiment": "negative",
                        "relevance_score": 0.85
                    },
                    priority="high",
                    source=self.name
                ))
            
            self.last_update = current_time
            
        except Exception as e:
            self.handle_error(e)
            
        return events

class PriceChangeDataSource(DataSource):
    """Real-time price change monitoring."""
    
    def __init__(self):
        super().__init__("Price Changes", "https://api.supercoach.com/prices", 3600)
        
    async def fetch_data(self) -> List[RealTimeEvent]:
        """Fetch price change data."""
        events = []
        
        try:
            # Mock price changes
            current_time = datetime.now()
            
            if current_time.hour in [2, 14] and current_time.minute == 0 and current_time.second == 0:
                events.append(RealTimeEvent(
                    event_type=EventType.PRICE_CHANGE,
                    timestamp=current_time,
                    data={
                        "player_id": 1,
                        "player_name": "Nathan Cleary",
                        "old_price": 750000,
                        "new_price": 760000,
                        "change": 10000,
                        "change_percentage": 1.33
                    },
                    priority="medium",
                    source=self.name
                ))
            
            self.last_update = current_time
            
        except Exception as e:
            self.handle_error(e)
            
        return events

class EventProcessor:
    """Processes real-time events and triggers appropriate actions."""
    
    def __init__(self):
        self.event_handlers: Dict[EventType, List[Callable]] = {}
        self.processed_events = []
        
    def register_handler(self, event_type: EventType, handler: Callable):
        """Register an event handler for a specific event type."""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        logger.info(f"Registered handler for {event_type.value}")
        
    async def process_event(self, event: RealTimeEvent):
        """Process a single event."""
        try:
            # Log the event
            logger.info(f"Processing {event.event_type.value} event from {event.source}")
            
            # Store event for history
            self.processed_events.append(event)
            
            # Keep only last 1000 events
            if len(self.processed_events) > 1000:
                self.processed_events = self.processed_events[-1000:]
            
            # Call registered handlers
            handlers = self.event_handlers.get(event.event_type, [])
            for handler in handlers:
                try:
                    await handler(event)
                except Exception as e:
                    logger.error(f"Error in event handler: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing event: {e}")

class NotificationManager:
    """Manages real-time notifications to users."""
    
    def __init__(self):
        self.subscribers: Dict[str, List[websockets.WebSocketServerProtocol]] = {}
        self.notification_rules = []
        
    def add_subscriber(self, user_id: str, websocket: websockets.WebSocketServerProtocol):
        """Add a WebSocket subscriber."""
        if user_id not in self.subscribers:
            self.subscribers[user_id] = []
        self.subscribers[user_id].append(websocket)
        logger.info(f"Added subscriber: {user_id}")
        
    def remove_subscriber(self, user_id: str, websocket: websockets.WebSocketServerProtocol):
        """Remove a WebSocket subscriber."""
        if user_id in self.subscribers:
            self.subscribers[user_id] = [
                ws for ws in self.subscribers[user_id] if ws != websocket
            ]
            if not self.subscribers[user_id]:
                del self.subscribers[user_id]
        logger.info(f"Removed subscriber: {user_id}")
        
    async def send_notification(self, user_id: str, notification: Dict[str, Any]):
        """Send notification to a specific user."""
        if user_id in self.subscribers:
            message = json.dumps(notification)
            disconnected = []
            
            for websocket in self.subscribers[user_id]:
                try:
                    await websocket.send(message)
                except websockets.exceptions.ConnectionClosed:
                    disconnected.append(websocket)
                except Exception as e:
                    logger.error(f"Error sending notification: {e}")
                    disconnected.append(websocket)
            
            # Remove disconnected websockets
            for ws in disconnected:
                self.remove_subscriber(user_id, ws)
                
    async def broadcast_notification(self, notification: Dict[str, Any]):
        """Broadcast notification to all subscribers."""
        for user_id in list(self.subscribers.keys()):
            await self.send_notification(user_id, notification)

class RealTimePipeline:
    """Main real-time data pipeline orchestrator."""
    
    def __init__(self):
        self.data_sources: List[DataSource] = []
        self.event_processor = EventProcessor()
        self.notification_manager = NotificationManager()
        self.is_running = False
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Initialize data sources
        self._initialize_data_sources()
        self._register_event_handlers()
        
    def _initialize_data_sources(self):
        """Initialize all data sources."""
        self.data_sources = [
            NRLLiveDataSource(),
            NewsDataSource(),
            PriceChangeDataSource()
        ]
        
    def _register_event_handlers(self):
        """Register event handlers for different event types."""
        
        async def handle_score_update(event: RealTimeEvent):
            """Handle player score updates."""
            notification = {
                "type": "score_update",
                "title": f"{event.data['player_name']} scores!",
                "message": f"+{event.data['points']} points ({event.data['score_type']})",
                "data": event.data,
                "timestamp": event.timestamp.isoformat()
            }
            await self.notification_manager.broadcast_notification(notification)
            
        async def handle_injury_report(event: RealTimeEvent):
            """Handle injury reports."""
            notification = {
                "type": "injury_alert",
                "title": f"Injury Alert: {event.data['player_name']}",
                "message": f"{event.data['injury_type']} - {event.data['status']}",
                "data": event.data,
                "timestamp": event.timestamp.isoformat(),
                "priority": "critical"
            }
            await self.notification_manager.broadcast_notification(notification)
            
        async def handle_price_change(event: RealTimeEvent):
            """Handle price changes."""
            change_text = "increased" if event.data['change'] > 0 else "decreased"
            notification = {
                "type": "price_change",
                "title": f"Price Change: {event.data['player_name']}",
                "message": f"Price {change_text} by ${abs(event.data['change']):,}",
                "data": event.data,
                "timestamp": event.timestamp.isoformat()
            }
            await self.notification_manager.broadcast_notification(notification)
            
        async def handle_news_alert(event: RealTimeEvent):
            """Handle news alerts."""
            notification = {
                "type": "news_alert",
                "title": "Breaking News",
                "message": event.data['headline'],
                "data": event.data,
                "timestamp": event.timestamp.isoformat()
            }
            await self.notification_manager.broadcast_notification(notification)
        
        # Register handlers
        self.event_processor.register_handler(EventType.PLAYER_SCORE_UPDATE, handle_score_update)
        self.event_processor.register_handler(EventType.INJURY_REPORT, handle_injury_report)
        self.event_processor.register_handler(EventType.PRICE_CHANGE, handle_price_change)
        self.event_processor.register_handler(EventType.NEWS_ALERT, handle_news_alert)
        
    async def start(self):
        """Start the real-time pipeline."""
        logger.info("Starting FantasyPro Real-time Data Pipeline...")
        self.is_running = True
        
        # Start all data sources
        for source in self.data_sources:
            await source.start()
        
        # Start main processing loop
        await self._run_pipeline()
        
    async def stop(self):
        """Stop the real-time pipeline."""
        logger.info("Stopping FantasyPro Real-time Data Pipeline...")
        self.is_running = False
        
        # Stop all data sources
        for source in self.data_sources:
            await source.stop()
            
    async def _run_pipeline(self):
        """Main pipeline processing loop."""
        while self.is_running:
            try:
                # Collect events from all active sources
                all_events = []
                
                for source in self.data_sources:
                    if source.is_active:
                        try:
                            events = await source.fetch_data()
                            all_events.extend(events)
                        except Exception as e:
                            logger.error(f"Error fetching from {source.name}: {e}")
                
                # Process all events
                for event in all_events:
                    await self.event_processor.process_event(event)
                
                # Wait before next cycle
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in pipeline loop: {e}")
                await asyncio.sleep(10)  # Wait longer on error
                
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status."""
        return {
            "is_running": self.is_running,
            "data_sources": [
                {
                    "name": source.name,
                    "is_active": source.is_active,
                    "last_update": source.last_update.isoformat() if source.last_update else None,
                    "error_count": source.error_count
                }
                for source in self.data_sources
            ],
            "events_processed": len(self.event_processor.processed_events),
            "active_subscribers": sum(len(subs) for subs in self.notification_manager.subscribers.values())
        }

async def main():
    """Demo function to test the real-time pipeline."""
    print("🚀 Starting FantasyPro Real-time Data Pipeline Demo...")
    print("=" * 60)
    
    pipeline = RealTimePipeline()
    
    try:
        # Start pipeline in background
        pipeline_task = asyncio.create_task(pipeline.start())
        
        # Run for 30 seconds
        await asyncio.sleep(30)
        
        # Show status
        status = pipeline.get_pipeline_status()
        print("\n📊 Pipeline Status:")
        print(f"   Running: {status['is_running']}")
        print(f"   Events Processed: {status['events_processed']}")
        print(f"   Active Sources: {sum(1 for ds in status['data_sources'] if ds['is_active'])}")
        
        # Stop pipeline
        await pipeline.stop()
        pipeline_task.cancel()
        
        print("\n✅ Real-time pipeline demo completed!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Pipeline stopped by user")
        await pipeline.stop()

if __name__ == "__main__":
    asyncio.run(main())
