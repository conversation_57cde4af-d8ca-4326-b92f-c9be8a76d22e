#!/usr/bin/env python3
"""
Test imports to verify all dependencies are available
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test all required imports"""
    print("🔍 Testing imports...")
    
    try:
        print("  📦 Testing basic dependencies...")
        import sqlalchemy
        print(f"    ✅ sqlalchemy: {sqlalchemy.__version__}")
        
        import fastapi
        print(f"    ✅ fastapi: {fastapi.__version__}")
        
        import pydantic
        print(f"    ✅ pydantic: {pydantic.__version__}")
        
        import aiohttp
        print(f"    ✅ aiohttp: {aiohttp.__version__}")
        
        import pandas
        print(f"    ✅ pandas: {pandas.__version__}")
        
        print("  🗄️ Testing database imports...")
        from database.supercoach_db import supercoach_db
        print("    ✅ supercoach_db imported")
        
        from database.supercoach_models import SuperCoachPlayer, NRLTeam
        print("    ✅ supercoach_models imported")
        
        print("  🔍 Testing scraper imports...")
        from scrapers.nrl_supercoach_scraper import NRLSuperCoachScraper
        print("    ✅ nrl_supercoach_scraper imported")
        
        print("  🧠 Testing analysis imports...")
        from analysis.supercoach_algorithms import supercoach_analyzer
        print("    ✅ supercoach_algorithms imported")
        
        print("  🤖 Testing agent imports...")
        from agents.supercoach_agent import supercoach_agent
        print("    ✅ supercoach_agent imported")
        
        print("  🌐 Testing API imports...")
        from api.supercoach_endpoints import router
        print("    ✅ supercoach_endpoints imported")
        
        print("🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_imports()
