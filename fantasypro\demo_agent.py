#!/usr/bin/env python3
"""
FantasyPro Demo Agent

A simple demonstration agent that shows how the FantasyPro AI system works.
This agent analyzes player data and generates recommendations.
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Try to import PraisonAI, fall back to mock if not available
try:
    from praisonaiagents import Agent, Task, PraisonAIAgents
    praisonai_available = True
except ImportError:
    print("PraisonAI not available, running in demo mode...")
    praisonai_available = False

class FantasyProDemoAgent:
    """Demo agent for FantasyPro platform."""
    
    def __init__(self):
        self.name = "FantasyPro Demo Agent"
        self.players_data = self._load_mock_data()
        
    def _load_mock_data(self) -> List[Dict[str, Any]]:
        """Load mock player data for analysis."""
        return [
            {
                "id": 1,
                "name": "<PERSON>",
                "position": "Halfback",
                "team": "Penrith Panthers",
                "price": 750000,
                "points": 1250,
                "recent_scores": [72, 58, 69, 71, 63],
                "ownership": 45.2,
                "fixtures": ["vs SYD (H)", "vs MEL (A)", "vs BRI (H)"],
                "injury_risk": 0.15
            },
            {
                "id": 2,
                "name": "James Tedesco",
                "position": "Fullback",
                "team": "Sydney Roosters",
                "price": 820000,
                "points": 1380,
                "recent_scores": [85, 92, 78, 88, 91],
                "ownership": 52.8,
                "fixtures": ["vs PEN (A)", "vs SOU (H)", "vs CAN (A)"],
                "injury_risk": 0.08
            },
            {
                "id": 3,
                "name": "Daly Cherry-Evans",
                "position": "Halfback",
                "team": "Manly Sea Eagles",
                "price": 680000,
                "points": 1150,
                "recent_scores": [65, 71, 58, 69, 74],
                "ownership": 38.5,
                "fixtures": ["vs TIT (H)", "vs WAR (A)", "vs NEW (H)"],
                "injury_risk": 0.12
            },
            {
                "id": 4,
                "name": "Kalyn Ponga",
                "position": "Fullback",
                "team": "Newcastle Knights",
                "price": 780000,
                "points": 1200,
                "recent_scores": [45, 89, 67, 92, 78],
                "ownership": 41.2,
                "fixtures": ["vs CRO (A)", "vs PAR (H)", "vs MAN (A)"],
                "injury_risk": 0.25
            },
            {
                "id": 5,
                "name": "Cameron Munster",
                "position": "Five-eighth",
                "team": "Melbourne Storm",
                "price": 720000,
                "points": 1180,
                "recent_scores": [78, 82, 71, 85, 79],
                "ownership": 39.8,
                "fixtures": ["vs PEN (H)", "vs BRI (A)", "vs GLD (H)"],
                "injury_risk": 0.10
            }
        ]
    
    def analyze_player_form(self, player: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a player's recent form and performance."""
        recent_scores = player["recent_scores"]
        
        # Calculate form metrics
        avg_score = sum(recent_scores) / len(recent_scores)
        consistency = 1 - (max(recent_scores) - min(recent_scores)) / max(recent_scores)
        trend = "improving" if recent_scores[-1] > recent_scores[0] else "declining"
        
        # Calculate form rating (0-10)
        form_rating = min(10, (avg_score / 100) * 10)
        
        return {
            "player_id": player["id"],
            "player_name": player["name"],
            "avg_score": round(avg_score, 1),
            "consistency": round(consistency, 2),
            "trend": trend,
            "form_rating": round(form_rating, 1),
            "recent_high": max(recent_scores),
            "recent_low": min(recent_scores)
        }
    
    def calculate_value_score(self, player: Dict[str, Any]) -> float:
        """Calculate player value score based on price vs performance."""
        points_per_dollar = player["points"] / player["price"]
        avg_recent = sum(player["recent_scores"]) / len(player["recent_scores"])
        
        # Normalize and combine metrics
        value_score = (points_per_dollar * 1000000) + (avg_recent / 10)
        return round(min(10, value_score), 1)
    
    def assess_injury_risk(self, player: Dict[str, Any]) -> str:
        """Assess injury risk level for a player."""
        risk = player["injury_risk"]
        if risk < 0.1:
            return "Low"
        elif risk < 0.2:
            return "Medium"
        else:
            return "High"
    
    def generate_recommendation(self, player: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI recommendation for a player."""
        form_analysis = self.analyze_player_form(player)
        value_score = self.calculate_value_score(player)
        injury_risk = self.assess_injury_risk(player)
        
        # Decision logic
        recommendation = "Hold"
        confidence = 0.5
        reasoning = []
        
        # Form-based recommendations
        if form_analysis["form_rating"] > 8.0:
            if value_score > 7.0:
                recommendation = "Strong Buy"
                confidence = 0.9
                reasoning.append(f"Excellent form ({form_analysis['form_rating']}/10)")
            else:
                recommendation = "Buy"
                confidence = 0.75
                reasoning.append(f"Good form but consider price")
        elif form_analysis["form_rating"] < 6.0:
            recommendation = "Sell"
            confidence = 0.8
            reasoning.append(f"Poor recent form ({form_analysis['form_rating']}/10)")
        
        # Injury risk factor
        if injury_risk == "High":
            if recommendation in ["Strong Buy", "Buy"]:
                recommendation = "Hold"
                confidence = max(0.3, confidence - 0.3)
            reasoning.append(f"High injury risk ({player['injury_risk']:.0%})")
        
        # Ownership considerations
        if player["ownership"] > 50:
            reasoning.append(f"High ownership ({player['ownership']:.1f}%)")
        elif player["ownership"] < 30:
            reasoning.append(f"Low ownership - differential pick")
        
        # Value considerations
        if value_score > 8.0:
            reasoning.append(f"Excellent value (score: {value_score}/10)")
        elif value_score < 5.0:
            reasoning.append(f"Poor value for money")
        
        return {
            "player_id": player["id"],
            "player_name": player["name"],
            "recommendation": recommendation,
            "confidence": round(confidence, 2),
            "reasoning": reasoning,
            "form_analysis": form_analysis,
            "value_score": value_score,
            "injury_risk": injury_risk,
            "generated_at": datetime.now().isoformat()
        }
    
    def run_analysis(self) -> Dict[str, Any]:
        """Run complete analysis on all players."""
        print(f"🤖 {self.name} starting analysis...")
        print("=" * 60)
        
        analysis_results = {
            "analysis_timestamp": datetime.now().isoformat(),
            "total_players": len(self.players_data),
            "recommendations": [],
            "summary": {
                "strong_buys": 0,
                "buys": 0,
                "holds": 0,
                "sells": 0
            }
        }
        
        for player in self.players_data:
            print(f"📊 Analyzing {player['name']} ({player['position']})...")
            
            recommendation = self.generate_recommendation(player)
            analysis_results["recommendations"].append(recommendation)
            
            # Update summary
            rec_type = recommendation["recommendation"].lower().replace(" ", "_")
            if rec_type in analysis_results["summary"]:
                analysis_results["summary"][rec_type] += 1
            
            # Print recommendation
            print(f"   💡 Recommendation: {recommendation['recommendation']}")
            print(f"   🎯 Confidence: {recommendation['confidence']:.0%}")
            print(f"   📈 Form Rating: {recommendation['form_analysis']['form_rating']}/10")
            print(f"   💰 Value Score: {recommendation['value_score']}/10")
            print(f"   🏥 Injury Risk: {recommendation['injury_risk']}")
            print(f"   📝 Reasoning: {', '.join(recommendation['reasoning'])}")
            print()
        
        return analysis_results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print analysis summary."""
        print("=" * 60)
        print("📋 ANALYSIS SUMMARY")
        print("=" * 60)
        
        summary = results["summary"]
        total = results["total_players"]
        
        print(f"Total Players Analyzed: {total}")
        print(f"Strong Buys: {summary.get('strong_buys', 0)}")
        print(f"Buys: {summary.get('buys', 0)}")
        print(f"Holds: {summary.get('holds', 0)}")
        print(f"Sells: {summary.get('sells', 0)}")
        print()
        
        # Top recommendations
        recommendations = results["recommendations"]
        strong_buys = [r for r in recommendations if r["recommendation"] == "Strong Buy"]
        
        if strong_buys:
            print("🌟 TOP RECOMMENDATIONS:")
            for rec in strong_buys:
                print(f"   • {rec['player_name']}: {rec['recommendation']} ({rec['confidence']:.0%} confidence)")
        
        print()
        print(f"Analysis completed at: {results['analysis_timestamp']}")
        print("=" * 60)

def main():
    """Main function to run the demo agent."""
    print("🚀 Starting FantasyPro Demo Agent...")
    print()
    
    # Initialize agent
    agent = FantasyProDemoAgent()
    
    # Run analysis
    results = agent.run_analysis()
    
    # Print summary
    agent.print_summary(results)
    
    # Save results to file
    output_file = f"analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"📄 Results saved to: {output_file}")
    print()
    print("✅ Demo agent completed successfully!")

if __name__ == "__main__":
    main()
