# FantasyPro API Documentation

The FantasyPro API provides comprehensive access to fantasy sports data, AI-powered insights, and platform functionality through a RESTful interface.

## Base URL

```
Production: https://api.fantasypro.ai/v1
Development: http://localhost:8000
```

## Authentication

FantasyPro uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your_jwt_token>
```

### Getting a Token

```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": 123,
    "email": "<EMAIL>",
    "username": "fantasy_pro"
  }
}
```

## Rate Limiting

API requests are rate limited based on your subscription tier:

| Tier | Requests/Hour | AI Analyses/Day | Data Exports/Day |
|------|---------------|-----------------|------------------|
| Free | 100 | 10 | 1 |
| Standard | 1,000 | 50 | 5 |
| Premium | 5,000 | 500 | 50 |
| Enterprise | 50,000 | Unlimited | Unlimited |

Rate limit headers are included in all responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Core Endpoints

### Health Check

Check API status and connectivity.

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "mode": "production",
  "database_status": "connected"
}
```

### Players

#### List Players

```http
GET /players?position={position}&team={team}&min_price={min}&max_price={max}&limit={limit}
```

**Parameters:**
- `position` (optional): Filter by player position
- `team` (optional): Filter by team name
- `min_price` (optional): Minimum player price
- `max_price` (optional): Maximum player price
- `limit` (optional): Number of results (default: 100, max: 500)

**Response:**
```json
[
  {
    "id": 1,
    "name": "Nathan Cleary",
    "position": "Halfback",
    "team": "Penrith Panthers",
    "price": 750000,
    "points": 1250,
    "form": 8.5,
    "ownership": 45.2,
    "value_score": 9.1,
    "recommendation": "Strong Buy",
    "last_updated": "2024-01-15T10:30:00Z"
  }
]
```

#### Get Player Details

```http
GET /players/{player_id}
```

**Response:**
```json
{
  "id": 1,
  "name": "Nathan Cleary",
  "position": "Halfback",
  "team": "Penrith Panthers",
  "price": 750000,
  "points": 1250,
  "form": 8.5,
  "ownership": 45.2,
  "value_score": 9.1,
  "recommendation": "Strong Buy",
  "recent_scores": [72, 58, 69, 71, 63],
  "season_stats": {
    "games_played": 20,
    "average_points": 62.5,
    "tries": 8,
    "try_assists": 15,
    "goals": 45
  },
  "injury_history": [],
  "upcoming_fixtures": [
    {
      "opponent": "Sydney Roosters",
      "venue": "ANZ Stadium",
      "date": "2024-01-20T19:30:00Z",
      "difficulty": 3.5
    }
  ],
  "ai_insights": {
    "predicted_score": 68.5,
    "confidence": 0.85,
    "key_factors": [
      "Strong recent form",
      "Favorable matchup",
      "Home ground advantage"
    ]
  }
}
```

### AI Recommendations

#### Get Recommendations

```http
GET /recommendations?type={type}&limit={limit}
```

**Parameters:**
- `type` (optional): Filter by recommendation type (transfer, captain, team)
- `limit` (optional): Number of results (default: 20)

**Response:**
```json
[
  {
    "id": 1,
    "type": "transfer",
    "title": "Consider transferring Nathan Cleary",
    "description": "Cleary has a tough fixture run coming up with low scoring potential.",
    "confidence": 0.85,
    "impact": "high",
    "player_in": {
      "id": 2,
      "name": "Daly Cherry-Evans",
      "price": 680000
    },
    "player_out": {
      "id": 1,
      "name": "Nathan Cleary",
      "price": 750000
    },
    "reasoning": [
      "Cleary faces 3 tough opponents in next 4 weeks",
      "DCE has easier fixture run",
      "Price difference allows upgrades elsewhere"
    ],
    "created_at": "2024-01-15T10:30:00Z"
  }
]
```

### Team Analysis

#### Optimize Team

```http
POST /team/optimize
Content-Type: application/json
Authorization: Bearer <token>

{
  "budget": 9500000,
  "strategy": "balanced",
  "constraints": {
    "max_players_per_team": 3,
    "required_positions": {
      "Halfback": 2,
      "Fullback": 2
    }
  }
}
```

**Response:**
```json
{
  "optimized_team": {
    "players": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17],
    "total_cost": 9480000,
    "projected_points": 1245.5,
    "risk_score": 0.25,
    "strategy": "balanced"
  },
  "alternatives": [
    {
      "strategy": "high_risk_high_reward",
      "projected_points": 1280.2,
      "risk_score": 0.65
    }
  ],
  "analysis": {
    "strengths": ["Strong attacking core", "Good value picks"],
    "weaknesses": ["Injury risk in forwards"],
    "recommendations": ["Consider captain options"]
  }
}
```

### Analytics

#### Dashboard Data

```http
GET /analytics/dashboard
Authorization: Bearer <token>
```

**Response:**
```json
{
  "user_stats": {
    "total_points": 15420,
    "rank": 1250,
    "weekly_rank": 890,
    "team_value": 9500000,
    "transfers_remaining": 2
  },
  "recent_performance": [
    {"week": 20, "points": 1250, "rank": 1200},
    {"week": 21, "points": 1180, "rank": 1350},
    {"week": 22, "points": 1320, "rank": 1100}
  ],
  "league_position": {
    "overall_rank": 1250,
    "total_players": 500000,
    "percentile": 99.75,
    "points_to_top_1000": 150
  },
  "upcoming_fixtures": [
    {
      "player": "Nathan Cleary",
      "opponent": "Sydney Roosters",
      "difficulty": 3.5,
      "date": "2024-01-20T19:30:00Z"
    }
  ]
}
```

## Real-time Features

### WebSocket Connection

Connect to real-time updates:

```javascript
const ws = new WebSocket('wss://api.fantasypro.ai/ws');

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Real-time update:', data);
};
```

### Event Types

- `score_update`: Live player scoring updates
- `injury_alert`: Injury reports and updates
- `price_change`: Player price changes
- `news_alert`: Breaking news affecting players
- `weather_update`: Weather conditions for games

## Error Handling

The API uses standard HTTP status codes:

- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Internal Server Error

**Error Response Format:**
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid player ID provided",
    "details": {
      "field": "player_id",
      "value": "invalid",
      "expected": "integer"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

## SDKs and Libraries

### Python SDK

```bash
pip install fantasypro-python
```

```python
from fantasypro import FantasyProClient

client = FantasyProClient(api_key='your_api_key')
players = client.players.list(position='Halfback')
recommendations = client.recommendations.get()
```

### JavaScript SDK

```bash
npm install fantasypro-js
```

```javascript
import FantasyPro from 'fantasypro-js';

const client = new FantasyPro({ apiKey: 'your_api_key' });
const players = await client.players.list({ position: 'Halfback' });
const recommendations = await client.recommendations.get();
```

## Webhooks

Configure webhooks to receive real-time notifications:

```http
POST /webhooks
Content-Type: application/json
Authorization: Bearer <token>

{
  "url": "https://your-app.com/webhooks/fantasypro",
  "events": ["score_update", "injury_alert", "price_change"],
  "secret": "your_webhook_secret"
}
```

## API Versioning

The API uses URL versioning. Current version is `v1`. When breaking changes are introduced, a new version will be released with a 6-month deprecation period for the previous version.

## Support

- **Documentation**: https://docs.fantasypro.ai
- **Status Page**: https://status.fantasypro.ai
- **Support Email**: <EMAIL>
- **Discord**: https://discord.gg/fantasypro-api
