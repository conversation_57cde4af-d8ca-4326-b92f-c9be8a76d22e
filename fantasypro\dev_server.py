#!/usr/bin/env python3
"""
FantasyPro Development Server

A simplified development server for FantasyPro that connects to real databases
but uses mock data for AI agents and external APIs.
"""

import os
import sys
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Try to import our modules, fall back to mock implementations if they fail
try:
    from config.settings import config
    from database.connection import get_db_session, get_redis_connection
    from services.auth_service import get_current_user, auth_service
    from services.player_service import PlayerService
    from services.team_service import TeamService
    from services.recommendation_service import RecommendationService
    database_available = True
except ImportError as e:
    print(f"Warning: Could not import database modules: {e}")
    print("Running in mock mode...")
    database_available = False

# Initialize FastAPI app
app = FastAPI(
    title="FantasyPro Development API",
    description="AI-Integrated Fantasy Sports Platform - Development Server",
    version="1.0.0-dev",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services if database is available
if database_available:
    player_service = PlayerService()
    team_service = TeamService()
    recommendation_service = RecommendationService()

# =============================================================================
# PYDANTIC MODELS
# =============================================================================

class PlayerResponse(BaseModel):
    id: int
    name: str
    position: str
    team: str
    price: float
    points: int
    form: float
    ownership: float
    value_score: float
    recommendation: str

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    mode: str
    database_status: str

# =============================================================================
# MOCK DATA
# =============================================================================

MOCK_PLAYERS = [
    {
        "id": 1,
        "name": "Nathan Cleary",
        "position": "Halfback",
        "team": "Penrith Panthers",
        "price": 750000,
        "points": 1250,
        "form": 8.5,
        "ownership": 45.2,
        "value_score": 9.1,
        "recommendation": "Strong Buy"
    },
    {
        "id": 2,
        "name": "James Tedesco",
        "position": "Fullback",
        "team": "Sydney Roosters",
        "price": 820000,
        "points": 1380,
        "form": 9.2,
        "ownership": 52.8,
        "value_score": 8.8,
        "recommendation": "Hold"
    },
    {
        "id": 3,
        "name": "Daly Cherry-Evans",
        "position": "Halfback",
        "team": "Manly Sea Eagles",
        "price": 680000,
        "points": 1150,
        "form": 7.8,
        "ownership": 38.5,
        "value_score": 8.5,
        "recommendation": "Buy"
    },
    {
        "id": 4,
        "name": "Kalyn Ponga",
        "position": "Fullback",
        "team": "Newcastle Knights",
        "price": 780000,
        "points": 1200,
        "form": 8.0,
        "ownership": 41.2,
        "value_score": 7.9,
        "recommendation": "Hold"
    },
    {
        "id": 5,
        "name": "Cameron Munster",
        "position": "Five-eighth",
        "team": "Melbourne Storm",
        "price": 720000,
        "points": 1180,
        "form": 8.3,
        "ownership": 39.8,
        "value_score": 8.7,
        "recommendation": "Buy"
    }
]

# =============================================================================
# HEALTH CHECK ENDPOINTS
# =============================================================================

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    
    # Test database connection if available
    db_status = "mock"
    if database_available:
        try:
            # Try to get a database session
            db = next(get_db_session())
            db.execute("SELECT 1")
            db.close()
            db_status = "connected"
        except Exception as e:
            db_status = f"error: {str(e)}"
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version="1.0.0-dev",
        mode="development",
        database_status=db_status
    )

@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with service status."""
    
    services = {
        "api": "healthy",
        "database": "mock",
        "redis": "mock",
        "agents": "mock"
    }
    
    if database_available:
        # Test PostgreSQL
        try:
            db = next(get_db_session())
            db.execute("SELECT 1")
            db.close()
            services["database"] = "connected"
        except Exception as e:
            services["database"] = f"error: {str(e)}"
        
        # Test Redis
        try:
            redis_client = get_redis_connection()
            redis_client.ping()
            services["redis"] = "connected"
        except Exception as e:
            services["redis"] = f"error: {str(e)}"
    
    return {
        "status": "healthy",
        "services": services,
        "timestamp": datetime.now().isoformat(),
        "environment": {
            "database_url": os.getenv("DATABASE_URL", "not_configured"),
            "redis_url": os.getenv("REDIS_URL", "not_configured"),
            "openai_configured": bool(os.getenv("OPENAI_API_KEY"))
        }
    }

# =============================================================================
# AUTHENTICATION ENDPOINTS
# =============================================================================

@app.post("/auth/login")
async def login(credentials: dict):
    """User login endpoint."""
    email = credentials.get("email")
    password = credentials.get("password")
    
    # Mock authentication for development
    if email == "<EMAIL>" and password == "demo123":
        if database_available:
            try:
                token = await auth_service.authenticate_user(email, password)
                return {
                    "access_token": token,
                    "token_type": "bearer",
                    "user": {
                        "id": 1,
                        "email": email,
                        "username": "demo_user"
                    }
                }
            except Exception:
                pass
        
        # Fallback to mock token
        return {
            "access_token": "mock_jwt_token_12345",
            "token_type": "bearer",
            "user": {
                "id": 1,
                "email": email,
                "username": "demo_user"
            }
        }
    else:
        raise HTTPException(status_code=401, detail="Invalid credentials")

# =============================================================================
# PLAYER ENDPOINTS
# =============================================================================

@app.get("/players", response_model=List[PlayerResponse])
async def get_players(
    position: Optional[str] = None,
    team: Optional[str] = None,
    min_price: Optional[float] = None,
    max_price: Optional[float] = None,
    limit: int = 100
):
    """Get players with optional filtering."""
    
    if database_available:
        try:
            db = next(get_db_session())
            players = await player_service.get_players(
                db=db, position=position, team=team, 
                min_price=min_price, max_price=max_price, limit=limit
            )
            db.close()
            return players
        except Exception as e:
            print(f"Database error, falling back to mock data: {e}")
    
    # Use mock data
    players = MOCK_PLAYERS.copy()
    
    # Apply filters
    if position:
        players = [p for p in players if p["position"] == position]
    if team:
        players = [p for p in players if p["team"] == team]
    if min_price:
        players = [p for p in players if p["price"] >= min_price]
    if max_price:
        players = [p for p in players if p["price"] <= max_price]
    
    return players[:limit]

@app.get("/players/{player_id}", response_model=PlayerResponse)
async def get_player(player_id: int):
    """Get detailed player information."""
    
    if database_available:
        try:
            db = next(get_db_session())
            player = await player_service.get_player_by_id(db, player_id)
            db.close()
            if player:
                return player
        except Exception as e:
            print(f"Database error, falling back to mock data: {e}")
    
    # Use mock data
    player = next((p for p in MOCK_PLAYERS if p["id"] == player_id), None)
    if not player:
        raise HTTPException(status_code=404, detail="Player not found")
    return player

# =============================================================================
# ANALYTICS ENDPOINTS
# =============================================================================

@app.get("/analytics/dashboard")
async def get_dashboard_data():
    """Get dashboard analytics data."""
    return {
        "user_stats": {
            "total_points": 15420,
            "rank": 1250,
            "weekly_rank": 890,
            "team_value": 9500000,
            "transfers_remaining": 2
        },
        "recent_performance": [
            {"week": 20, "points": 1250, "rank": 1200},
            {"week": 21, "points": 1180, "rank": 1350},
            {"week": 22, "points": 1320, "rank": 1100}
        ],
        "league_position": {
            "overall_rank": 1250,
            "total_players": 500000,
            "percentile": 99.75,
            "points_to_top_1000": 150
        },
        "upcoming_fixtures": [
            {
                "player": "Nathan Cleary",
                "opponent": "Sydney Roosters",
                "difficulty": 3.5,
                "date": "2024-08-15"
            }
        ]
    }

@app.get("/recommendations")
async def get_recommendations(type: Optional[str] = None, limit: int = 20):
    """Get personalized recommendations."""
    recommendations = [
        {
            "id": 1,
            "type": "transfer",
            "title": "Consider transferring Nathan Cleary",
            "description": "Cleary has a tough fixture run coming up.",
            "confidence": 0.85,
            "impact": "high",
            "created_at": datetime.now().isoformat()
        },
        {
            "id": 2,
            "type": "captain",
            "title": "Captain James Tedesco this week",
            "description": "Tedesco has an excellent matchup.",
            "confidence": 0.92,
            "impact": "medium",
            "created_at": datetime.now().isoformat()
        }
    ]
    
    if type:
        recommendations = [r for r in recommendations if r["type"] == type]
    
    return recommendations[:limit]

# =============================================================================
# MAIN
# =============================================================================

if __name__ == "__main__":
    print("🚀 Starting FantasyPro Development Server...")
    print("=" * 60)
    print(f"📊 API Documentation: http://localhost:8000/docs")
    print(f"🔍 Health Check: http://localhost:8000/health")
    print(f"👤 Demo Login: <EMAIL> / demo123")
    print(f"🗄️  Database Status: {'Connected' if database_available else 'Mock Mode'}")
    print("=" * 60)
    
    uvicorn.run(
        "dev_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
