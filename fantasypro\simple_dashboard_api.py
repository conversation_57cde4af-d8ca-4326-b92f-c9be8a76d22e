#!/usr/bin/env python3
"""
Simple Dashboard API

A working version of the dashboard endpoint for testing.
"""

import sys
import os
from pathlib import Path
from datetime import datetime
from typing import List, Optional
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
import uvicorn

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.supercoach_db import supercoach_db
from database.supercoach_models import SuperCoachPlayer

# SportRadar integration (optional - only if API key is provided)
sportradar_client = None
try:
    from sportradar_client import SportRadarNRLClient
    SPORTRADAR_API_KEY = os.getenv('SPORTRADAR_API_KEY')
    if SPORTRADAR_API_KEY:
        sportradar_client = SportRadarNRLClient(SPORTRADAR_API_KEY)
        print("✅ SportRadar client initialized")
    else:
        print("⚠️  SportRadar API key not found - using database only")
except ImportError:
    print("⚠️  SportRadar client not available - using database only")

# Simple response models
class SimplePlayerResponse(BaseModel):
    id: int
    name: str
    team: str
    position: str
    current_price: Optional[float] = None
    current_breakeven: Optional[int] = None
    season_points: Optional[int] = None

class SimpleDashboardResponse(BaseModel):
    total_players: int
    active_players: int
    total_teams: int
    last_updated: datetime
    sample_players: List[SimplePlayerResponse]

# Create FastAPI app
app = FastAPI(
    title="FantasyPro Simple Dashboard API",
    description="Simple working dashboard with real NRL SuperCoach data",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def serve_dashboard():
    """Serve the HTML dashboard"""
    return FileResponse("dashboard.html")

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "mode": "simple_dashboard",
        "supercoach_data": "real"
    }

@app.get("/dashboard", response_model=SimpleDashboardResponse)
async def get_simple_dashboard():
    """Get simple dashboard data with real NRL SuperCoach players"""
    try:
        with supercoach_db.get_session() as session:
            # Get all players
            all_players = session.query(SuperCoachPlayer).all()
            active_players = [p for p in all_players if p.is_active]
            
            # Convert first 10 players to response format
            sample_players = []
            for player in all_players[:10]:
                sample_players.append(SimplePlayerResponse(
                    id=player.id,
                    name=player.name,
                    team=player.team.name if player.team else "Unknown",
                    position=player.position or "Unknown",
                    current_price=player.current_price,
                    current_breakeven=player.current_breakeven,
                    season_points=player.season_points
                ))
            
            return SimpleDashboardResponse(
                total_players=len(all_players),
                active_players=len(active_players),
                total_teams=17,
                last_updated=datetime.utcnow(),
                sample_players=sample_players
            )
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error retrieving dashboard data: {str(e)}")

@app.get("/players")
async def get_simple_players(limit: int = 20):
    """Get simple players list"""
    try:
        with supercoach_db.get_session() as session:
            players = session.query(SuperCoachPlayer).limit(limit).all()
            
            player_responses = []
            for player in players:
                player_responses.append(SimplePlayerResponse(
                    id=player.id,
                    name=player.name,
                    team=player.team.name if player.team else "Unknown",
                    position=player.position or "Unknown",
                    current_price=player.current_price,
                    current_breakeven=player.current_breakeven,
                    season_points=player.season_points
                ))
            
            return player_responses
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error retrieving players: {str(e)}")

@app.get("/teams")
async def get_teams():
    """Get all teams with player counts"""
    try:
        with supercoach_db.get_session() as session:
            from database.supercoach_models import NRLTeam
            teams = session.query(NRLTeam).all()
            
            team_data = []
            for team in teams:
                team_players = session.query(SuperCoachPlayer).filter_by(team_id=team.id).all()
                team_data.append({
                    "id": team.id,
                    "name": team.name,
                    "abbreviation": team.abbreviation,
                    "city": team.city,
                    "player_count": len(team_players),
                    "sample_players": [p.name for p in team_players[:3]]
                })
            
            return team_data
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error retrieving teams: {str(e)}")

@app.get("/sportradar/players")
async def get_sportradar_players(limit: int = 50):
    """Get players from SportRadar API (if available)"""
    if not sportradar_client:
        raise HTTPException(status_code=503, detail="SportRadar API not available")

    try:
        # Get all players from SportRadar
        players = sportradar_client.get_all_players()

        # Limit results
        limited_players = players[:limit] if players else []

        return {
            "source": "sportradar",
            "count": len(limited_players),
            "players": limited_players
        }

    except Exception as e:
        print(f"SportRadar Error: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving SportRadar data: {str(e)}")

@app.get("/sportradar/teams")
async def get_sportradar_teams():
    """Get teams from SportRadar API (if available)"""
    if not sportradar_client:
        raise HTTPException(status_code=503, detail="SportRadar API not available")

    try:
        teams = sportradar_client.get_team_list()
        return {
            "source": "sportradar",
            "count": len(teams),
            "teams": teams
        }

    except Exception as e:
        print(f"SportRadar Error: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving SportRadar teams: {str(e)}")

@app.post("/sportradar/update")
async def update_sportradar_data():
    """Update player database with SportRadar data"""
    if not sportradar_client:
        raise HTTPException(status_code=503, detail="SportRadar API not available")

    try:
        result = sportradar_client.update_player_database()
        return {
            "status": "success",
            "message": "SportRadar data updated successfully",
            "result": result
        }

    except Exception as e:
        print(f"SportRadar Update Error: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating SportRadar data: {str(e)}")

if __name__ == "__main__":
    print("🏈 Starting Simple Dashboard API with Real NRL SuperCoach Data")
    print("📊 Dashboard: http://localhost:8001/dashboard")
    print("👥 Players: http://localhost:8001/players")
    print("🏈 Teams: http://localhost:8001/teams")
    print("🔍 Health: http://localhost:8001/health")
    print("📖 Docs: http://localhost:8001/docs")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,  # Different port to avoid conflicts
        reload=False,
        log_level="info"
    )
