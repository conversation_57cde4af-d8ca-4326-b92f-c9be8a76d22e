<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FantasyPro - Theme System Demo</title>
    <meta name="theme-color" content="#0f172a">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        /* Theme System CSS */
        :root {
            --theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --glow-intensity: 0.3;
        }

        /* Dark Theme (Default) */
        :root, :root.dark {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-tertiary: #94a3b8;
            --border-primary: #334155;
            --brand-primary: #22c55e;
            --glow-primary: 0 0 20px rgba(34, 197, 94, 0.3);
        }

        /* Light Theme */
        :root.light {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --text-primary: #0f172a;
            --text-secondary: #1e293b;
            --text-tertiary: #475569;
            --border-primary: #e2e8f0;
            --glow-intensity: 0.15;
            --glow-primary: 0 0 15px rgba(34, 197, 94, 0.15);
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
            transition: var(--theme-transition);
        }

        .themed-bg-primary { background-color: var(--bg-primary); }
        .themed-bg-secondary { background-color: var(--bg-secondary); }
        .themed-text-primary { color: var(--text-primary); }
        .themed-text-secondary { color: var(--text-secondary); }
        .themed-text-tertiary { color: var(--text-tertiary); }
        .themed-border { border-color: var(--border-primary); }
        .themed-glow-primary { box-shadow: var(--glow-primary); }

        .card-premium {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 1px solid var(--border-primary);
            transition: var(--theme-transition);
        }

        .card-premium:hover {
            box-shadow: var(--glow-primary);
            border-color: var(--brand-primary);
            transform: translateY(-2px);
        }

        .btn-primary {
            background-color: var(--brand-primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: var(--theme-transition);
            border: none;
            cursor: pointer;
        }

        .btn-primary:hover {
            background-color: #16a34a;
            box-shadow: var(--glow-primary);
            transform: translateY(-1px);
        }

        .theme-toggle {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: var(--theme-transition);
        }

        .theme-toggle:hover {
            background: var(--bg-tertiary);
            box-shadow: var(--glow-primary);
        }

        .status-live {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            color: var(--brand-primary);
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .glow-pulse {
            animation: glowPulse 2s ease-in-out infinite alternate;
        }

        @keyframes glowPulse {
            0% { box-shadow: 0 0 5px rgba(34, 197, 94, 0.2); }
            100% { box-shadow: 0 0 20px rgba(34, 197, 94, 0.4); }
        }

        .float {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Smooth transitions for all themed elements */
        [class*="themed-"], .card-premium, .btn-primary, .theme-toggle {
            transition: var(--theme-transition);
        }
    </style>
</head>
<body>
    <div class="min-h-screen themed-bg-primary">
        <!-- Header -->
        <header class="themed-bg-secondary themed-border border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center themed-glow-primary glow-pulse">
                            <span class="text-white font-bold">FP</span>
                        </div>
                        <h1 class="text-xl font-bold themed-text-primary">FantasyPro</h1>
                        <span class="status-live">Live</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="theme-toggle" onclick="toggleTheme()">
                            <span id="theme-icon">🌙</span>
                            <span id="theme-text">Dark Mode</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Hero Section -->
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold themed-text-primary mb-4">
                    FantasyPro Theme System
                </h2>
                <p class="text-lg themed-text-tertiary max-w-2xl mx-auto">
                    Experience seamless dark and light modes that preserve the core FantasyPro identity 
                    while providing a true inversion experience, not an afterthought.
                </p>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                <div class="card-premium p-6 rounded-lg float">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-2xl">🏆</span>
                        </div>
                        <span class="text-sm text-green-400">+7%</span>
                    </div>
                    <div class="text-2xl font-bold themed-text-primary mb-1">2,847</div>
                    <div class="text-sm themed-text-tertiary">Total Points</div>
                </div>

                <div class="card-premium p-6 rounded-lg float" style="animation-delay: 0.2s;">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-2xl">📊</span>
                        </div>
                        <span class="text-sm text-blue-400">Live</span>
                    </div>
                    <div class="text-2xl font-bold themed-text-primary mb-1">114</div>
                    <div class="text-sm themed-text-tertiary">Players Tracked</div>
                </div>

                <div class="card-premium p-6 rounded-lg float" style="animation-delay: 0.4s;">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-2xl">🔄</span>
                        </div>
                        <span class="text-sm text-orange-400">AI</span>
                    </div>
                    <div class="text-2xl font-bold themed-text-primary mb-1">3</div>
                    <div class="text-sm themed-text-tertiary">Trade Recommendations</div>
                </div>

                <div class="card-premium p-6 rounded-lg float" style="animation-delay: 0.6s;">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-2xl">⚡</span>
                        </div>
                        <span class="text-sm text-purple-400">Real-time</span>
                    </div>
                    <div class="text-2xl font-bold themed-text-primary mb-1">A+</div>
                    <div class="text-sm themed-text-tertiary">System Grade</div>
                </div>
            </div>

            <!-- Feature Showcase -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                <!-- Theme Features -->
                <div class="card-premium p-8 rounded-lg">
                    <h3 class="text-xl font-semibold themed-text-primary mb-6">Theme System Features</h3>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span class="themed-text-secondary">Seamless dark/light mode switching</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                            <span class="themed-text-secondary">True inversion design (not afterthought)</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                            <span class="themed-text-secondary">Preserved brand identity</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-orange-400 rounded-full"></div>
                            <span class="themed-text-secondary">Hardware-accelerated transitions</span>
                        </div>
                    </div>
                </div>

                <!-- Interactive Demo -->
                <div class="card-premium p-8 rounded-lg">
                    <h3 class="text-xl font-semibold themed-text-primary mb-6">Interactive Elements</h3>
                    <div class="space-y-4">
                        <button class="btn-primary w-full">Primary Action Button</button>
                        <div class="flex items-center justify-between p-3 themed-bg-secondary rounded-lg themed-border">
                            <span class="themed-text-secondary">System Status</span>
                            <span class="status-live">Operational</span>
                        </div>
                        <input type="text" placeholder="Search players..." 
                               class="w-full p-3 themed-bg-secondary themed-border rounded-lg themed-text-primary"
                               style="background: var(--bg-secondary); border: 1px solid var(--border-primary); color: var(--text-primary);">
                    </div>
                </div>
            </div>

            <!-- API Status -->
            <div class="card-premium p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold themed-text-primary mb-4">System Status</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="flex items-center justify-center space-x-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full glow-pulse"></div>
                        <span class="themed-text-secondary">Backend API</span>
                    </div>
                    <div class="flex items-center justify-center space-x-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full glow-pulse"></div>
                        <span class="themed-text-secondary">Data Pipeline</span>
                    </div>
                    <div class="flex items-center justify-center space-x-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full glow-pulse"></div>
                        <span class="themed-text-secondary">Theme System</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');
            const metaThemeColor = document.querySelector('meta[name="theme-color"]');
            
            if (html.classList.contains('dark')) {
                html.classList.remove('dark');
                html.classList.add('light');
                themeIcon.textContent = '☀️';
                themeText.textContent = 'Light Mode';
                metaThemeColor.setAttribute('content', '#ffffff');
            } else {
                html.classList.remove('light');
                html.classList.add('dark');
                themeIcon.textContent = '🌙';
                themeText.textContent = 'Dark Mode';
                metaThemeColor.setAttribute('content', '#0f172a');
            }
        }

        // Test API connection
        fetch('http://localhost:8004/health')
            .then(response => response.json())
            .then(data => {
                console.log('✅ Backend API connected:', data);
            })
            .catch(error => {
                console.log('⚠️ Backend API not available:', error);
            });
    </script>
</body>
</html>
