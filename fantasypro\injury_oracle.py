#!/usr/bin/env python3
"""
FantasyPro Injury Oracle
Real-time injury intelligence system combining SportRadar data with ML predictions
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json

from sportradar_client import SportRadarNRLClient
from supabase_client import FantasyProSupabaseClient
from ml_engines import InjuryPredictionEngine, PlayerData

logger = logging.getLogger(__name__)

@dataclass
class InjuryAlert:
    """Injury alert with ML-enhanced insights"""
    player_id: str
    player_name: str
    team: str
    injury_type: str
    injury_status: str
    severity: str
    expected_return: Optional[str]
    risk_score: float
    impact_rating: str
    ownership_percentage: float
    replacement_suggestions: List[Dict]
    last_updated: str

class InjuryOracle:
    """Main injury intelligence system"""
    
    def __init__(self, sportradar_key: str, supabase_key: str):
        self.sportradar_client = SportRadarNRLClient(sportradar_key)
        self.supabase_client = FantasyProSupabaseClient()
        self.supabase_client.set_service_key(supabase_key)
        self.injury_engine = InjuryPredictionEngine()
        
        self.cache_duration = timedelta(hours=1)  # Refresh every hour
        self.last_update = None
        self.cached_alerts = []
    
    async def get_live_injury_intel(self) -> List[InjuryAlert]:
        """Get real-time injury intelligence with ML insights"""
        
        # Check if cache is fresh
        if (self.last_update and 
            datetime.now() - self.last_update < self.cache_duration and 
            self.cached_alerts):
            logger.info("Returning cached injury alerts")
            return self.cached_alerts
        
        try:
            # Get fresh injury data from SportRadar
            logger.info("Fetching fresh injury data from SportRadar...")
            injury_reports = self.sportradar_client.get_injury_reports()
            
            # Get player data for ML analysis
            all_players = self.supabase_client.get_all_players(1000)
            
            # Store injury reports in Supabase
            if injury_reports:
                self.supabase_client.store_injuries(injury_reports)
                logger.info(f"Stored {len(injury_reports)} injury reports")
            
            # Generate enhanced injury alerts
            alerts = await self._generate_injury_alerts(injury_reports, all_players)
            
            # Cache results
            self.cached_alerts = alerts
            self.last_update = datetime.now()
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error getting injury intel: {e}")
            # Return cached data if available
            return self.cached_alerts if self.cached_alerts else []
    
    async def _generate_injury_alerts(self, injury_reports: List[Dict], 
                                    all_players: List[Dict]) -> List[InjuryAlert]:
        """Generate enhanced injury alerts with ML insights"""
        
        alerts = []
        
        # Create player lookup
        player_lookup = {p['name']: p for p in all_players}
        
        for injury in injury_reports:
            player_name = injury.get('player_name', '')
            player_data = player_lookup.get(player_name)
            
            if not player_data:
                continue
            
            try:
                # Convert to PlayerData for ML analysis
                ml_player = self._convert_to_player_data(player_data)
                
                # Get ML injury risk assessment
                risk_assessment = self.injury_engine.predict_injury_risk(ml_player, injury_reports)
                
                # Calculate impact rating
                impact_rating = self._calculate_impact_rating(
                    player_data, injury.get('injury_status', ''), risk_assessment['risk_score']
                )
                
                # Get replacement suggestions
                replacements = self._get_replacement_suggestions(
                    player_data, all_players, injury_reports
                )
                
                # Create enhanced alert
                alert = InjuryAlert(
                    player_id=player_data.get('id', ''),
                    player_name=player_name,
                    team=injury.get('team', ''),
                    injury_type=injury.get('injury_type', 'Unknown'),
                    injury_status=injury.get('injury_status', ''),
                    severity=injury.get('severity', 'Unknown'),
                    expected_return=injury.get('expected_return'),
                    risk_score=risk_assessment['risk_score'],
                    impact_rating=impact_rating,
                    ownership_percentage=self._estimate_ownership(player_data),
                    replacement_suggestions=replacements,
                    last_updated=datetime.now().isoformat()
                )
                
                alerts.append(alert)
                
            except Exception as e:
                logger.error(f"Error processing injury for {player_name}: {e}")
                continue
        
        # Sort by impact (high impact first)
        impact_order = {'Critical': 0, 'High': 1, 'Medium': 2, 'Low': 3}
        alerts.sort(key=lambda x: impact_order.get(x.impact_rating, 4))
        
        return alerts
    
    def _convert_to_player_data(self, player_dict: Dict) -> PlayerData:
        """Convert player dictionary to PlayerData object"""
        return PlayerData(
            player_id=str(player_dict.get('id', '')),
            name=player_dict.get('name', ''),
            team=player_dict.get('team', ''),
            position=player_dict.get('position', 'Unknown'),
            price=float(player_dict.get('price', 500000)),  # Default price
            recent_scores=[],  # Would need historical data
            season_points=int(player_dict.get('season_points', 0)),
            games_played=int(player_dict.get('games_played', 15)),
            injury_history=[],  # Would need historical injury data
            ownership_percentage=self._estimate_ownership(player_dict)
        )
    
    def _estimate_ownership(self, player_data: Dict) -> float:
        """Estimate ownership percentage based on player attributes"""
        # Simple estimation based on position and team
        base_ownership = 20.0
        
        # Popular teams get higher ownership
        popular_teams = ['Sydney Roosters', 'Melbourne Storm', 'Penrith Panthers']
        if player_data.get('team') in popular_teams:
            base_ownership += 15.0
        
        # Key positions get higher ownership
        key_positions = ['Fullback', 'Halfback', 'Five-eighth']
        if player_data.get('position') in key_positions:
            base_ownership += 10.0
        
        return min(80.0, base_ownership)
    
    def _calculate_impact_rating(self, player_data: Dict, injury_status: str, 
                               risk_score: float) -> str:
        """Calculate the fantasy impact rating of an injury"""
        
        # Base impact from injury status
        status_impact = {
            'out': 4,
            'injured': 4,
            'doubtful': 3,
            'questionable': 2,
            'probable': 1
        }
        
        base_impact = 0
        for status, impact in status_impact.items():
            if status in injury_status.lower():
                base_impact = impact
                break
        
        # Adjust for player importance (estimated by position)
        position_multiplier = {
            'Fullback': 1.3,
            'Halfback': 1.3,
            'Five-eighth': 1.2,
            'Hooker': 1.2,
            'Centre': 1.1,
            'Winger': 0.9,
            'Prop': 1.0,
            'Second-row': 1.0,
            'Lock': 1.1
        }
        
        multiplier = position_multiplier.get(player_data.get('position', ''), 1.0)
        adjusted_impact = base_impact * multiplier
        
        # Factor in ML risk score
        if risk_score > 0.6:
            adjusted_impact += 1
        elif risk_score > 0.4:
            adjusted_impact += 0.5
        
        # Convert to rating
        if adjusted_impact >= 4.5:
            return "Critical"
        elif adjusted_impact >= 3.0:
            return "High"
        elif adjusted_impact >= 1.5:
            return "Medium"
        else:
            return "Low"
    
    def _get_replacement_suggestions(self, injured_player: Dict, 
                                   all_players: List[Dict], 
                                   injury_reports: List[Dict]) -> List[Dict]:
        """Get replacement player suggestions"""
        
        replacements = []
        injured_position = injured_player.get('position', '')
        injured_team = injured_player.get('team', '')
        
        # Find players in same position
        same_position = [
            p for p in all_players 
            if (p.get('position') == injured_position and 
                p.get('name') != injured_player.get('name'))
        ]
        
        # Filter out currently injured players
        injured_names = {inj.get('player_name', '').lower() for inj in injury_reports}
        healthy_players = [
            p for p in same_position 
            if p.get('name', '').lower() not in injured_names
        ]
        
        # Sort by estimated value (simplified)
        for player in healthy_players[:5]:  # Top 5 replacements
            replacements.append({
                'name': player.get('name'),
                'team': player.get('team'),
                'position': player.get('position'),
                'estimated_ownership': self._estimate_ownership(player),
                'reason': f"Same position replacement for {injured_player.get('name')}"
            })
        
        return replacements
    
    def get_injury_summary(self) -> Dict[str, Any]:
        """Get summary of current injury situation"""
        if not self.cached_alerts:
            return {
                'total_injuries': 0,
                'critical_injuries': 0,
                'high_impact_injuries': 0,
                'last_updated': None
            }
        
        critical_count = sum(1 for alert in self.cached_alerts if alert.impact_rating == 'Critical')
        high_count = sum(1 for alert in self.cached_alerts if alert.impact_rating == 'High')
        
        return {
            'total_injuries': len(self.cached_alerts),
            'critical_injuries': critical_count,
            'high_impact_injuries': high_count,
            'last_updated': self.last_update.isoformat() if self.last_update else None,
            'top_concerns': [
                {
                    'player': alert.player_name,
                    'team': alert.team,
                    'status': alert.injury_status,
                    'impact': alert.impact_rating
                }
                for alert in self.cached_alerts[:5]
            ]
        }

async def main():
    """Test the Injury Oracle"""
    print("🏥 Testing FantasyPro Injury Oracle")
    
    # Initialize with API keys
    sportradar_key = "aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN"
    supabase_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg"
    
    oracle = InjuryOracle(sportradar_key, supabase_key)
    
    # Get injury intelligence
    alerts = await oracle.get_live_injury_intel()
    
    print(f"Found {len(alerts)} injury alerts")
    for alert in alerts[:3]:
        print(f"- {alert.player_name} ({alert.team}): {alert.injury_status} - {alert.impact_rating} impact")
    
    # Get summary
    summary = oracle.get_injury_summary()
    print(f"Injury Summary: {summary}")

if __name__ == "__main__":
    asyncio.run(main())
