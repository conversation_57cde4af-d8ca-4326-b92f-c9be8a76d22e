#!/usr/bin/env python3
"""
SuperCoach AI Agent

Enhanced AI agent that integrates scraped NRL SuperCoach data with 
existing decision engines for intelligent fantasy sports management.
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import logging
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.supercoach_db import supercoach_db
from analysis.supercoach_algorithms import supercoach_analyzer, FormTrend, RiskLevel
from scrapers.nrl_supercoach_scraper import NRLSuperCoachScraper
from utils.logging_config import setup_logging

logger = setup_logging(__name__)

@dataclass
class AgentRecommendation:
    """AI Agent recommendation structure"""
    type: str  # 'trade', 'captain', 'hold', 'sell'
    title: str
    description: str
    confidence: float
    priority: int
    data: Dict[str, Any]
    reasoning: List[str]
    created_at: datetime

class SuperCoachAIAgent:
    """Enhanced AI agent for SuperCoach decision making"""
    
    def __init__(self):
        self.db = supercoach_db
        self.analyzer = supercoach_analyzer
        self.last_update = None
        
    async def update_data(self) -> bool:
        """Update data from NRL SuperCoach Stats"""
        try:
            logger.info("Starting data update from NRL SuperCoach Stats...")
            
            async with NRLSuperCoachScraper() as scraper:
                scraped_data = await scraper.scrape_all_data()
                
                if scraped_data:
                    success = self.db.store_scraped_data(scraped_data)
                    if success:
                        self.last_update = datetime.utcnow()
                        logger.info(f"Successfully updated data for {len(scraped_data.get('combined_players', []))} players")
                        return True
                    else:
                        logger.error("Failed to store scraped data")
                        return False
                else:
                    logger.error("No data scraped")
                    return False
                    
        except Exception as e:
            logger.error(f"Error updating data: {e}")
            return False
    
    def generate_weekly_recommendations(self, user_team: Optional[List[int]] = None) -> List[AgentRecommendation]:
        """Generate comprehensive weekly recommendations"""
        recommendations = []
        
        try:
            # 1. Trade Recommendations
            trade_recs = self._generate_trade_recommendations(user_team)
            recommendations.extend(trade_recs)
            
            # 2. Captain Recommendations
            captain_recs = self._generate_captain_recommendations()
            recommendations.extend(captain_recs)
            
            # 3. Price Watch Alerts
            price_alerts = self._generate_price_alerts()
            recommendations.extend(price_alerts)
            
            # 4. Injury Alerts
            injury_alerts = self._generate_injury_alerts()
            recommendations.extend(injury_alerts)
            
            # 5. Form Alerts
            form_alerts = self._generate_form_alerts()
            recommendations.extend(form_alerts)
            
            # Sort by priority and confidence
            recommendations.sort(key=lambda x: (x.priority, -x.confidence))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating weekly recommendations: {e}")
            return []
    
    def _generate_trade_recommendations(self, user_team: Optional[List[int]] = None) -> List[AgentRecommendation]:
        """Generate AI-powered trade recommendations"""
        recommendations = []
        
        try:
            # Get trade recommendations from analyzer
            trade_recs = self.analyzer.generate_trade_recommendations(limit=5)
            
            for i, trade in enumerate(trade_recs):
                # Get player details
                with self.db.get_session() as session:
                    player_out = session.query(self.db.SuperCoachPlayer).filter_by(id=trade.player_out_id).first()
                    player_in = session.query(self.db.SuperCoachPlayer).filter_by(id=trade.player_in_id).first()
                    
                    if not player_out or not player_in:
                        continue
                
                # Build reasoning
                reasoning = [
                    f"Expected points gain: +{trade.expected_points_gain:.1f} per round",
                    f"Price difference: ${trade.price_difference:,.0f}",
                    f"Risk level: {trade.risk_score:.1%}",
                    trade.reasoning
                ]
                
                # Determine priority based on user team
                priority = i + 1
                if user_team and trade.player_out_id in user_team:
                    priority = max(1, priority - 2)  # Higher priority for owned players
                
                recommendation = AgentRecommendation(
                    type="trade",
                    title=f"Trade {player_out.name} → {player_in.name}",
                    description=f"Upgrade {player_out.name} ({player_out.team.name}) to {player_in.name} ({player_in.team.name}) for improved scoring potential",
                    confidence=trade.confidence,
                    priority=priority,
                    data={
                        "player_out": {
                            "id": player_out.id,
                            "name": player_out.name,
                            "team": player_out.team.name,
                            "position": player_out.position,
                            "price": player_out.current_price
                        },
                        "player_in": {
                            "id": player_in.id,
                            "name": player_in.name,
                            "team": player_in.team.name,
                            "position": player_in.position,
                            "price": player_in.current_price
                        },
                        "expected_gain": trade.expected_points_gain,
                        "price_difference": trade.price_difference,
                        "risk_score": trade.risk_score
                    },
                    reasoning=reasoning,
                    created_at=datetime.utcnow()
                )
                
                recommendations.append(recommendation)
                
        except Exception as e:
            logger.error(f"Error generating trade recommendations: {e}")
        
        return recommendations
    
    def _generate_captain_recommendations(self) -> List[AgentRecommendation]:
        """Generate captain recommendations based on form and fixtures"""
        recommendations = []
        
        try:
            # Get top form players
            top_players = self.db.get_top_players_by_metric('form', limit=10)
            
            captain_candidates = []
            for player in top_players:
                form_analysis = self.analyzer.analyze_player_form(player.id)
                if form_analysis and form_analysis.momentum_score > 0.6:
                    captain_candidates.append((player, form_analysis))
            
            # Sort by momentum and form
            captain_candidates.sort(key=lambda x: x[1].momentum_score, reverse=True)
            
            for i, (player, form) in enumerate(captain_candidates[:3]):
                reasoning = [
                    f"Current form rating: {form.current_form:.1f}/10",
                    f"Recent average: {form.recent_average:.1f} points",
                    f"Form trend: {form.trend.value}",
                    f"Momentum score: {form.momentum_score:.1%}"
                ]
                
                confidence = form.momentum_score * form.consistency
                
                recommendation = AgentRecommendation(
                    type="captain",
                    title=f"Captain {player.name} this round",
                    description=f"{player.name} ({player.team.name}) is in excellent form with strong momentum",
                    confidence=confidence,
                    priority=i + 1,
                    data={
                        "player_id": player.id,
                        "player_name": player.name,
                        "team": player.team.name,
                        "position": player.position,
                        "form_rating": form.current_form,
                        "momentum": form.momentum_score,
                        "recent_average": form.recent_average
                    },
                    reasoning=reasoning,
                    created_at=datetime.utcnow()
                )
                
                recommendations.append(recommendation)
                
        except Exception as e:
            logger.error(f"Error generating captain recommendations: {e}")
        
        return recommendations
    
    def _generate_price_alerts(self) -> List[AgentRecommendation]:
        """Generate price change alerts"""
        recommendations = []
        
        try:
            # Get price predictions
            price_predictions = self.analyzer.predict_price_changes()
            
            # Alert for significant price rises
            for pred in price_predictions[:5]:
                if pred.price_change > 20000 and pred.confidence > 0.6:  # $20k+ rise
                    with self.db.get_session() as session:
                        player = session.query(self.db.SuperCoachPlayer).filter_by(id=pred.player_id).first()
                        if not player:
                            continue
                    
                    reasoning = [
                        f"Predicted price rise: ${pred.price_change:,.0f}",
                        f"Current breakeven: {pred.breakeven_required}",
                        f"Confidence: {pred.confidence:.1%}",
                        f"Probability of increase: {pred.probability_increase:.1%}"
                    ]
                    
                    recommendation = AgentRecommendation(
                        type="price_alert",
                        title=f"Price Rise Alert: {player.name}",
                        description=f"{player.name} is likely to rise ${pred.price_change:,.0f} next round",
                        confidence=pred.confidence,
                        priority=3,
                        data={
                            "player_id": player.id,
                            "player_name": player.name,
                            "current_price": pred.current_price,
                            "predicted_price": pred.predicted_price,
                            "price_change": pred.price_change,
                            "breakeven": pred.breakeven_required
                        },
                        reasoning=reasoning,
                        created_at=datetime.utcnow()
                    )
                    
                    recommendations.append(recommendation)
                    
        except Exception as e:
            logger.error(f"Error generating price alerts: {e}")
        
        return recommendations
    
    def _generate_injury_alerts(self) -> List[AgentRecommendation]:
        """Generate injury-related alerts"""
        recommendations = []
        
        try:
            # Get players with injury concerns
            all_players = self.db.get_all_players()
            injured_players = [p for p in all_players if p.injury_status and p.injury_status != 'Fit']
            
            for player in injured_players[:5]:  # Top 5 injury concerns
                reasoning = [
                    f"Injury status: {player.injury_status}",
                    f"Ownership: {player.ownership_percentage:.1f}%" if player.ownership_percentage else "Ownership: Unknown",
                    "Monitor team news for updates"
                ]
                
                # Higher priority for high-ownership players
                priority = 2 if (player.ownership_percentage or 0) > 30 else 4
                
                recommendation = AgentRecommendation(
                    type="injury_alert",
                    title=f"Injury Watch: {player.name}",
                    description=f"{player.name} ({player.team.name}) has injury concerns - {player.injury_status}",
                    confidence=0.8,
                    priority=priority,
                    data={
                        "player_id": player.id,
                        "player_name": player.name,
                        "team": player.team.name,
                        "injury_status": player.injury_status,
                        "ownership": player.ownership_percentage
                    },
                    reasoning=reasoning,
                    created_at=datetime.utcnow()
                )
                
                recommendations.append(recommendation)
                
        except Exception as e:
            logger.error(f"Error generating injury alerts: {e}")
        
        return recommendations
    
    def _generate_form_alerts(self) -> List[AgentRecommendation]:
        """Generate form-based alerts"""
        recommendations = []
        
        try:
            # Get players with significant form changes
            all_players = self.db.get_all_players()
            
            for player in all_players:
                if not player.form_rating:
                    continue
                
                form_analysis = self.analyzer.analyze_player_form(player.id)
                if not form_analysis:
                    continue
                
                # Alert for players in declining form with high ownership
                if (form_analysis.trend == FormTrend.DECLINING and 
                    (player.ownership_percentage or 0) > 25 and
                    form_analysis.current_form < 6.0):
                    
                    reasoning = [
                        f"Form trend: {form_analysis.trend.value}",
                        f"Current form: {form_analysis.current_form:.1f}/10",
                        f"Recent average: {form_analysis.recent_average:.1f}",
                        f"Season average: {form_analysis.season_average:.1f}",
                        f"High ownership: {player.ownership_percentage:.1f}%"
                    ]
                    
                    recommendation = AgentRecommendation(
                        type="form_alert",
                        title=f"Form Concern: {player.name}",
                        description=f"{player.name} is in declining form despite high ownership",
                        confidence=0.7,
                        priority=3,
                        data={
                            "player_id": player.id,
                            "player_name": player.name,
                            "team": player.team.name,
                            "form_rating": form_analysis.current_form,
                            "trend": form_analysis.trend.value,
                            "ownership": player.ownership_percentage
                        },
                        reasoning=reasoning,
                        created_at=datetime.utcnow()
                    )
                    
                    recommendations.append(recommendation)
                    
        except Exception as e:
            logger.error(f"Error generating form alerts: {e}")
        
        return recommendations
    
    def get_player_insights(self, player_id: int) -> Dict[str, Any]:
        """Get comprehensive insights for a specific player"""
        try:
            with self.db.get_session() as session:
                player = session.query(self.db.SuperCoachPlayer).filter_by(id=player_id).first()
                if not player:
                    return {}
            
            # Get form analysis
            form_analysis = self.analyzer.analyze_player_form(player_id)
            
            # Get price prediction
            price_predictions = self.analyzer.predict_price_changes()
            price_pred = next((p for p in price_predictions if p.player_id == player_id), None)
            
            insights = {
                "player": {
                    "id": player.id,
                    "name": player.name,
                    "team": player.team.name,
                    "position": player.position,
                    "price": player.current_price,
                    "breakeven": player.current_breakeven,
                    "ownership": player.ownership_percentage
                },
                "form_analysis": asdict(form_analysis) if form_analysis else None,
                "price_prediction": asdict(price_pred) if price_pred else None,
                "recommendations": []
            }
            
            # Generate specific recommendations for this player
            if form_analysis:
                if form_analysis.trend == FormTrend.RISING and form_analysis.momentum_score > 0.7:
                    insights["recommendations"].append({
                        "type": "buy",
                        "message": "Strong buy - excellent form and momentum",
                        "confidence": form_analysis.momentum_score
                    })
                elif form_analysis.trend == FormTrend.DECLINING and form_analysis.risk_level == RiskLevel.HIGH:
                    insights["recommendations"].append({
                        "type": "sell",
                        "message": "Consider selling - declining form and high risk",
                        "confidence": 0.8
                    })
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting player insights: {e}")
            return {}


# Global agent instance
supercoach_agent = SuperCoachAIAgent()
