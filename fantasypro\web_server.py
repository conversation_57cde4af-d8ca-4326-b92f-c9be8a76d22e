#!/usr/bin/env python3
"""
Simple HTTP server for serving the FantasyPro web interface
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(Path(__file__).parent / "web"), **kwargs)
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()

def main():
    PORT = 3000
    
    print(f"🌐 Starting FantasyPro Web Server on port {PORT}...")
    print(f"📱 Access the web interface at: http://localhost:{PORT}")
    print(f"🔗 API server should be running at: http://localhost:8000")
    print("=" * 60)
    
    with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Web server stopped")

if __name__ == "__main__":
    main()
