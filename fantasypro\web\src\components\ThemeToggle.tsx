import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';
import {
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown' | 'switch';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'button',
  size = 'md',
  showLabel = false,
  className = ''
}) => {
  const { theme, resolvedTheme, setTheme, toggleTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const themes = [
    { value: 'light', label: 'Light', icon: SunIcon },
    { value: 'dark', label: 'Dark', icon: MoonIcon },
    { value: 'system', label: 'System', icon: ComputerDesktopIcon },
  ] as const;

  const currentTheme = themes.find(t => t.value === theme) || themes[1];
  const CurrentIcon = currentTheme.icon;

  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  if (variant === 'switch') {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        {showLabel && (
          <span className="text-sm font-medium themed-text-secondary">
            {resolvedTheme === 'dark' ? 'Dark' : 'Light'} Mode
          </span>
        )}
        <button
          onClick={toggleTheme}
          className={`
            relative inline-flex items-center justify-center
            ${sizeClasses[size]} rounded-full
            themed-bg-tertiary themed-border
            hover:themed-bg-elevated
            transition-all duration-300 ease-in-out
            focus:outline-none focus:ring-2 focus:ring-offset-2 
            focus:ring-offset-var(--bg-primary) focus:ring-var(--brand-primary)
            group overflow-hidden
          `}
          aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode`}
        >
          {/* Background glow effect */}
          <div className="absolute inset-0 themed-glow-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          {/* Icon container with rotation animation */}
          <motion.div
            key={resolvedTheme}
            initial={{ rotate: -180, opacity: 0 }}
            animate={{ rotate: 0, opacity: 1 }}
            exit={{ rotate: 180, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="relative z-10"
          >
            <CurrentIcon className={`${iconSizes[size]} themed-text-primary`} />
          </motion.div>
        </button>
      </div>
    );
  }

  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`
            flex items-center space-x-2 px-3 py-2 rounded-lg
            themed-bg-secondary themed-border
            hover:themed-bg-tertiary
            transition-all duration-200
            focus:outline-none focus:ring-2 focus:ring-var(--brand-primary)
          `}
          aria-label="Theme selector"
          aria-expanded={isOpen}
        >
          <CurrentIcon className={iconSizes[size]} />
          {showLabel && (
            <span className="text-sm font-medium themed-text-primary">
              {currentTheme.label}
            </span>
          )}
          <motion.svg
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
            className="w-4 h-4 themed-text-tertiary"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </motion.svg>
        </button>

        <AnimatePresence>
          {isOpen && (
            <>
              {/* Backdrop */}
              <div
                className="fixed inset-0 z-10"
                onClick={() => setIsOpen(false)}
              />
              
              {/* Dropdown menu */}
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="
                  absolute right-0 mt-2 w-48 z-20
                  themed-bg-secondary themed-border rounded-lg
                  themed-shadow-lg overflow-hidden
                "
              >
                {themes.map((themeOption) => {
                  const Icon = themeOption.icon;
                  const isSelected = theme === themeOption.value;
                  
                  return (
                    <button
                      key={themeOption.value}
                      onClick={() => {
                        setTheme(themeOption.value);
                        setIsOpen(false);
                      }}
                      className={`
                        w-full flex items-center justify-between px-4 py-3
                        hover:themed-bg-tertiary
                        transition-colors duration-150
                        ${isSelected ? 'themed-bg-tertiary' : ''}
                      `}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className="w-5 h-5 themed-text-secondary" />
                        <span className="text-sm font-medium themed-text-primary">
                          {themeOption.label}
                        </span>
                      </div>
                      {isSelected && (
                        <CheckIcon className="w-4 h-4 text-var(--brand-primary)" />
                      )}
                    </button>
                  );
                })}
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Default button variant
  return (
    <button
      onClick={toggleTheme}
      className={`
        relative inline-flex items-center justify-center
        ${sizeClasses[size]} rounded-lg
        themed-bg-secondary themed-border
        hover:themed-bg-tertiary hover:themed-glow-primary
        transition-all duration-300 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-offset-2 
        focus:ring-offset-var(--bg-primary) focus:ring-var(--brand-primary)
        group overflow-hidden
        ${className}
      `}
      aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode`}
    >
      {/* Shimmer effect */}
      <div className="absolute inset-0 shimmer opacity-0 group-hover:opacity-100" />
      
      {/* Icon with smooth transition */}
      <AnimatePresence mode="wait">
        <motion.div
          key={resolvedTheme}
          initial={{ rotate: -90, opacity: 0 }}
          animate={{ rotate: 0, opacity: 1 }}
          exit={{ rotate: 90, opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="relative z-10"
        >
          <CurrentIcon className={`${iconSizes[size]} themed-text-primary`} />
        </motion.div>
      </AnimatePresence>
      
      {showLabel && (
        <span className="ml-2 text-sm font-medium themed-text-primary">
          {currentTheme.label}
        </span>
      )}
    </button>
  );
};

export default ThemeToggle;
