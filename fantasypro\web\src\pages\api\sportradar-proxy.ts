/**
 * SportRadar API Proxy
 * Server-side proxy to bypass CORS restrictions
 */

import { NextApiRequest, NextApiResponse } from 'next';

const SPORTRADAR_API_KEY = 'aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN';
const SPORTRADAR_BASE_URL = 'https://api.sportradar.com/rugby-league/trial/v3';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { endpoint } = req.query;

  if (!endpoint || typeof endpoint !== 'string') {
    return res.status(400).json({ error: 'Endpoint parameter required' });
  }

  try {
    // Construct the full URL
    const url = `${SPORTRADAR_BASE_URL}/${endpoint}?api_key=${SPORTRADAR_API_KEY}`;
    
    console.log('🔄 Proxying request to:', url);

    // Make the request to SportRadar
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'FantasyPro/1.0',
      },
    });

    console.log('📊 SportRadar response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ SportRadar error:', errorText);
      
      return res.status(response.status).json({
        error: `SportRadar API error: ${response.status} ${response.statusText}`,
        details: errorText
      });
    }

    const data = await response.json();
    console.log('✅ SportRadar success, data keys:', Object.keys(data));

    // Return the data with CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    return res.status(200).json({
      success: true,
      data,
      endpoint: url,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Proxy error:', error);
    
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// Test endpoints that we can try
export const TEST_ENDPOINTS = [
  'competitions.json',
  'seasons.json',
  'teams.json',
  'players.json',
  'schedules.json',
  'fixtures.json',
  'injuries.json',
];
