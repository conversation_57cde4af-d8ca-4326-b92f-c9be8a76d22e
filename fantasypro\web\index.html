<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FantasyPro - AI-Powered Fantasy Sports</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                        <span class="text-purple-600 font-bold text-xl">FP</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">FantasyPro</h1>
                        <p class="text-purple-100 text-sm">AI-Powered Fantasy Sports Platform</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="status-indicator" class="px-3 py-1 bg-green-500 text-white rounded-full text-sm">
                        🟢 Connected
                    </span>
                    <button id="login-btn" class="bg-white text-purple-600 px-4 py-2 rounded-lg font-semibold hover:bg-gray-100 transition">
                        Login
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Dashboard Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">Total Points</p>
                        <p class="text-2xl font-bold text-gray-800">15,420</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <span class="text-blue-600 text-xl">📊</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">Overall Rank</p>
                        <p class="text-2xl font-bold text-gray-800">1,250</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-green-600 text-xl">🏆</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">Team Value</p>
                        <p class="text-2xl font-bold text-gray-800">$9.5M</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <span class="text-yellow-600 text-xl">💰</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">Transfers Left</p>
                        <p class="text-2xl font-bold text-gray-800">2</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <span class="text-purple-600 text-xl">🔄</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Players Section -->
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-gray-800">Top Players</h2>
                    <button id="refresh-players" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition">
                        Refresh Data
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="players-loading" class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                    <p class="text-gray-500 mt-2">Loading players...</p>
                </div>
                <div id="players-container" class="hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Player</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Position</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Team</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Price</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Points</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Form</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Recommendation</th>
                                </tr>
                            </thead>
                            <tbody id="players-table-body">
                                <!-- Players will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations Section -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">AI Recommendations</h2>
            </div>
            <div class="p-6">
                <div id="recommendations-container">
                    <!-- Recommendations will be loaded here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Login Modal -->
    <div id="login-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h3 class="text-xl font-bold mb-4">Login to FantasyPro</h3>
            <form id="login-form">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                    <input type="email" id="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-purple-500">
                </div>
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                    <input type="password" id="password" value="demo123" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-purple-500">
                </div>
                <div class="flex items-center justify-between">
                    <button type="submit" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition">
                        Login
                    </button>
                    <button type="button" id="close-modal" class="text-gray-500 hover:text-gray-700">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // API Configuration
        const API_BASE_URL = 'http://localhost:8000';
        let authToken = null;

        // DOM Elements
        const loginBtn = document.getElementById('login-btn');
        const loginModal = document.getElementById('login-modal');
        const loginForm = document.getElementById('login-form');
        const closeModal = document.getElementById('close-modal');
        const refreshPlayersBtn = document.getElementById('refresh-players');
        const statusIndicator = document.getElementById('status-indicator');

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            checkAPIHealth();
            loadPlayers();
            loadRecommendations();
        });

        // Event Listeners
        loginBtn.addEventListener('click', () => {
            loginModal.classList.remove('hidden');
            loginModal.classList.add('flex');
        });

        closeModal.addEventListener('click', () => {
            loginModal.classList.add('hidden');
            loginModal.classList.remove('flex');
        });

        loginForm.addEventListener('submit', handleLogin);
        refreshPlayersBtn.addEventListener('click', loadPlayers);

        // API Functions
        async function checkAPIHealth() {
            try {
                console.log('Checking API health...');
                const response = await axios.get(`${API_BASE_URL}/health`);
                console.log('API health response:', response.data);
                statusIndicator.innerHTML = '🟢 Connected';
                statusIndicator.className = 'px-3 py-1 bg-green-500 text-white rounded-full text-sm';
            } catch (error) {
                console.error('API health check failed:', error);
                statusIndicator.innerHTML = '🔴 Disconnected';
                statusIndicator.className = 'px-3 py-1 bg-red-500 text-white rounded-full text-sm';
            }
        }

        async function handleLogin(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await axios.post(`${API_BASE_URL}/auth/login`, {
                    email: email,
                    password: password
                });

                authToken = response.data.access_token;
                loginBtn.textContent = `Welcome, ${response.data.user.username}`;
                loginModal.classList.add('hidden');
                loginModal.classList.remove('flex');
                
                // Show success message
                showNotification('Login successful!', 'success');
            } catch (error) {
                showNotification('Login failed. Please check your credentials.', 'error');
            }
        }

        async function loadPlayers() {
            const loadingEl = document.getElementById('players-loading');
            const containerEl = document.getElementById('players-container');
            const tableBodyEl = document.getElementById('players-table-body');

            loadingEl.classList.remove('hidden');
            containerEl.classList.add('hidden');

            try {
                console.log('Loading players...');
                const response = await axios.get(`${API_BASE_URL}/players`);
                console.log('Players response:', response.data);
                const players = response.data;

                tableBodyEl.innerHTML = '';
                players.forEach(player => {
                    const row = document.createElement('tr');
                    row.className = 'border-b border-gray-100 hover:bg-gray-50';
                    row.innerHTML = `
                        <td class="py-3 px-4 font-medium text-gray-800">${player.name}</td>
                        <td class="py-3 px-4 text-gray-600">${player.position}</td>
                        <td class="py-3 px-4 text-gray-600">${player.team}</td>
                        <td class="py-3 px-4 text-gray-600">$${(player.price / 1000).toFixed(0)}k</td>
                        <td class="py-3 px-4 text-gray-600">${player.points}</td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                                ${player.form}/10
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 ${getRecommendationColor(player.recommendation)} rounded-full text-sm">
                                ${player.recommendation}
                            </span>
                        </td>
                    `;
                    tableBodyEl.appendChild(row);
                });

                loadingEl.classList.add('hidden');
                containerEl.classList.remove('hidden');
            } catch (error) {
                console.error('Failed to load players:', error);
                loadingEl.innerHTML = `<p class="text-red-500">Failed to load players: ${error.message}</p>`;
            }
        }

        async function loadRecommendations() {
            const containerEl = document.getElementById('recommendations-container');

            try {
                const response = await axios.get(`${API_BASE_URL}/recommendations`);
                const recommendations = response.data;

                containerEl.innerHTML = '';
                recommendations.forEach(rec => {
                    const recEl = document.createElement('div');
                    recEl.className = 'mb-4 p-4 border border-gray-200 rounded-lg';
                    recEl.innerHTML = `
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-800">${rec.title}</h4>
                                <p class="text-gray-600 mt-1">${rec.description}</p>
                                <div class="flex items-center mt-2 space-x-4">
                                    <span class="text-sm text-gray-500">Confidence: ${(rec.confidence * 100).toFixed(0)}%</span>
                                    <span class="px-2 py-1 ${getImpactColor(rec.impact)} rounded-full text-xs">
                                        ${rec.impact.toUpperCase()} IMPACT
                                    </span>
                                </div>
                            </div>
                            <span class="text-2xl">${getRecommendationIcon(rec.type)}</span>
                        </div>
                    `;
                    containerEl.appendChild(recEl);
                });
            } catch (error) {
                containerEl.innerHTML = '<p class="text-red-500">Failed to load recommendations</p>';
            }
        }

        // Utility Functions
        function getRecommendationColor(recommendation) {
            switch (recommendation.toLowerCase()) {
                case 'strong buy': return 'bg-green-100 text-green-800';
                case 'buy': return 'bg-blue-100 text-blue-800';
                case 'hold': return 'bg-yellow-100 text-yellow-800';
                case 'sell': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getImpactColor(impact) {
            switch (impact.toLowerCase()) {
                case 'high': return 'bg-red-100 text-red-800';
                case 'medium': return 'bg-yellow-100 text-yellow-800';
                case 'low': return 'bg-green-100 text-green-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getRecommendationIcon(type) {
            switch (type.toLowerCase()) {
                case 'transfer': return '🔄';
                case 'captain': return '👑';
                case 'buy': return '📈';
                case 'sell': return '📉';
                default: return '💡';
            }
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${
                type === 'success' ? 'bg-green-500' : 'bg-red-500'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html>
