/**
 * SportRadar API Service Layer
 * Integrates with SportRadar NRL API for real-time rugby league data
 */

// SportRadar API Configuration
const SPORTRADAR_API_KEY = 'aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN';
const SPORTRADAR_BASE_URL = 'https://api.sportradar.com/rugby-league/trial/v3';

// Rate limiting configuration
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
let lastRequestTime = 0;

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const cache = new Map<string, { data: any; timestamp: number }>();

// SportRadar API Types
export interface SportRadarPlayer {
  id: string;
  name: string;
  position: string;
  jersey_number?: number;
  date_of_birth?: string;
  height?: number;
  weight?: number;
  nationality?: string;
}

export interface SportRadarTeam {
  id: string;
  name: string;
  abbreviation: string;
  venue?: {
    id: string;
    name: string;
    capacity?: number;
    city?: string;
  };
  players?: SportRadarPlayer[];
}

export interface SportRadarCompetition {
  id: string;
  name: string;
  season: {
    id: string;
    name: string;
    start_date: string;
    end_date: string;
    year: number;
  };
  teams: SportRadarTeam[];
}

export interface SportRadarInjury {
  player: SportRadarPlayer;
  injury_type: string;
  status: 'injured' | 'doubtful' | 'out' | 'questionable';
  expected_return?: string;
  description?: string;
  updated: string;
}

export interface SportRadarFixture {
  id: string;
  start_time: string;
  status: string;
  home_team: SportRadarTeam;
  away_team: SportRadarTeam;
  venue?: {
    id: string;
    name: string;
  };
  round?: {
    number: number;
    name: string;
  };
}

// Rate limiting helper - now uses server-side proxy
async function rateLimitedRequest(endpoint: string): Promise<Response> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY - timeSinceLastRequest));
  }

  lastRequestTime = Date.now();

  // Use our server-side proxy to bypass CORS
  const response = await fetch(`/api/sportradar-proxy?endpoint=${encodeURIComponent(endpoint)}`, {
    headers: {
      'Accept': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(`SportRadar API error: ${response.status} - ${errorData.error || response.statusText}`);
  }

  // Extract the actual data from our proxy response
  const proxyResponse = await response.json();

  // Return a mock Response object with the data
  return {
    ok: true,
    status: 200,
    json: async () => proxyResponse.data
  } as Response;
}

// Cache helper
function getCachedData<T>(key: string): T | null {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data as T;
  }
  return null;
}

function setCachedData<T>(key: string, data: T): void {
  cache.set(key, { data, timestamp: Date.now() });
}

/**
 * SportRadar API Service Class
 */
export class SportRadarService {
  
  /**
   * Get all competitions (leagues)
   */
  static async getCompetitions(): Promise<SportRadarCompetition[]> {
    const cacheKey = 'competitions';
    const cached = getCachedData<SportRadarCompetition[]>(cacheKey);
    if (cached) return cached;

    try {
      const response = await rateLimitedRequest('competitions.json');
      const data = await response.json();

      const competitions = data.competitions || data || [];
      setCachedData(cacheKey, competitions);
      return competitions;
    } catch (error) {
      console.error('Error fetching competitions:', error);
      throw error;
    }
  }

  /**
   * Get NRL competition details with teams
   */
  static async getNRLCompetition(): Promise<SportRadarCompetition | null> {
    try {
      const competitions = await this.getCompetitions();
      // Find NRL competition (adjust name as needed based on API response)
      const nrlCompetition = competitions.find(comp => 
        comp.name.toLowerCase().includes('nrl') || 
        comp.name.toLowerCase().includes('national rugby league')
      );
      
      if (nrlCompetition) {
        // Get detailed competition info with teams
        return await this.getCompetitionDetails(nrlCompetition.id);
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching NRL competition:', error);
      throw error;
    }
  }

  /**
   * Get competition details including teams and players
   */
  static async getCompetitionDetails(competitionId: string): Promise<SportRadarCompetition> {
    const cacheKey = `competition_${competitionId}`;
    const cached = getCachedData<SportRadarCompetition>(cacheKey);
    if (cached) return cached;

    try {
      const response = await rateLimitedRequest(`competitions/${competitionId}/info.json`);
      const data = await response.json();

      setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching competition details:', error);
      throw error;
    }
  }

  /**
   * Get all teams in NRL
   */
  static async getNRLTeams(): Promise<SportRadarTeam[]> {
    try {
      const nrlCompetition = await this.getNRLCompetition();
      return nrlCompetition?.teams || [];
    } catch (error) {
      console.error('Error fetching NRL teams:', error);
      return [];
    }
  }

  /**
   * Get team details including players
   */
  static async getTeamDetails(teamId: string): Promise<SportRadarTeam> {
    const cacheKey = `team_${teamId}`;
    const cached = getCachedData<SportRadarTeam>(cacheKey);
    if (cached) return cached;

    try {
      const url = `${SPORTRADAR_BASE_URL}/teams/${teamId}/profile.json?api_key=${SPORTRADAR_API_KEY}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching team details:', error);
      throw error;
    }
  }

  /**
   * Get all NRL players
   */
  static async getAllNRLPlayers(): Promise<SportRadarPlayer[]> {
    const cacheKey = 'all_nrl_players';
    const cached = getCachedData<SportRadarPlayer[]>(cacheKey);
    if (cached) return cached;

    try {
      const teams = await this.getNRLTeams();
      const allPlayers: SportRadarPlayer[] = [];
      
      // Get players from each team
      for (const team of teams) {
        try {
          const teamDetails = await this.getTeamDetails(team.id);
          if (teamDetails.players) {
            // Add team info to each player
            const playersWithTeam = teamDetails.players.map(player => ({
              ...player,
              team_id: team.id,
              team_name: team.name,
              team_abbreviation: team.abbreviation
            }));
            allPlayers.push(...playersWithTeam);
          }
        } catch (error) {
          console.error(`Error fetching players for team ${team.name}:`, error);
          // Continue with other teams
        }
      }
      
      setCachedData(cacheKey, allPlayers);
      return allPlayers;
    } catch (error) {
      console.error('Error fetching all NRL players:', error);
      return [];
    }
  }

  /**
   * Get player details
   */
  static async getPlayerDetails(playerId: string): Promise<SportRadarPlayer> {
    const cacheKey = `player_${playerId}`;
    const cached = getCachedData<SportRadarPlayer>(cacheKey);
    if (cached) return cached;

    try {
      const url = `${SPORTRADAR_BASE_URL}/players/${playerId}/profile.json?api_key=${SPORTRADAR_API_KEY}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching player details:', error);
      throw error;
    }
  }

  /**
   * Get injury reports
   */
  static async getInjuryReports(): Promise<SportRadarInjury[]> {
    const cacheKey = 'injury_reports';
    const cached = getCachedData<SportRadarInjury[]>(cacheKey);
    if (cached) return cached;

    try {
      // Note: Adjust endpoint based on actual SportRadar API structure
      const url = `${SPORTRADAR_BASE_URL}/injuries.json?api_key=${SPORTRADAR_API_KEY}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      const injuries = data.injuries || [];
      setCachedData(cacheKey, injuries);
      return injuries;
    } catch (error) {
      console.error('Error fetching injury reports:', error);
      // Return empty array if endpoint doesn't exist yet
      return [];
    }
  }

  /**
   * Get upcoming fixtures
   */
  static async getUpcomingFixtures(limit: number = 10): Promise<SportRadarFixture[]> {
    const cacheKey = `fixtures_upcoming_${limit}`;
    const cached = getCachedData<SportRadarFixture[]>(cacheKey);
    if (cached) return cached;

    try {
      const nrlCompetition = await this.getNRLCompetition();
      if (!nrlCompetition) return [];

      const url = `${SPORTRADAR_BASE_URL}/competitions/${nrlCompetition.id}/schedules.json?api_key=${SPORTRADAR_API_KEY}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      // Filter for upcoming fixtures
      const now = new Date();
      const upcomingFixtures = (data.fixtures || [])
        .filter((fixture: SportRadarFixture) => new Date(fixture.start_time) > now)
        .slice(0, limit);
      
      setCachedData(cacheKey, upcomingFixtures);
      return upcomingFixtures;
    } catch (error) {
      console.error('Error fetching upcoming fixtures:', error);
      return [];
    }
  }

  /**
   * Search players by name
   */
  static async searchPlayers(query: string, limit: number = 10): Promise<SportRadarPlayer[]> {
    try {
      const allPlayers = await this.getAllNRLPlayers();
      const searchTerm = query.toLowerCase();
      
      return allPlayers
        .filter(player => 
          player.name.toLowerCase().includes(searchTerm) ||
          (player as any).team_name?.toLowerCase().includes(searchTerm)
        )
        .slice(0, limit);
    } catch (error) {
      console.error('Error searching players:', error);
      return [];
    }
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  static clearCache(): void {
    cache.clear();
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): { size: number; keys: string[] } {
    return {
      size: cache.size,
      keys: Array.from(cache.keys())
    };
  }
}

export default SportRadarService;
