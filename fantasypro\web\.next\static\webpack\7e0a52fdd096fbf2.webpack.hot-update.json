{"c": ["pages/dashboard", "pages/my-team", "webpack"], "r": ["pages/index", "pages/my-team", "pages/error-test", "src_services_supabase_ts"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cjonah%5CDesktop%5CORIGVMI%5CGithub%5CPlayground%5COCTOAGENT%5Cfantasypro%5Cweb%5Csrc%5Cpages%5Cindex.tsx&page=%2F!", "./src/pages/index.tsx", "./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js", "./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cjonah%5CDesktop%5CORIGVMI%5CGithub%5CPlayground%5COCTOAGENT%5Cfantasypro%5Cweb%5Csrc%5Cpages%5Cmy-team.tsx&page=%2Fmy-team!", "./src/pages/my-team.tsx", "__barrel_optimize__?names=ArrowTrendingUpIcon,ChartBarIcon,CurrencyDollarIcon,FireIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cjonah%5CDesktop%5CORIGVMI%5CGithub%5CPlayground%5COCTOAGENT%5Cfantasypro%5Cweb%5Csrc%5Cpages%5Cerror-test.tsx&page=%2Ferror-test!", "./src/pages/error-test.tsx"]}