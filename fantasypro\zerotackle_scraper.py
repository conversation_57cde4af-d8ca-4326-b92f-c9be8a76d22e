#!/usr/bin/env python3
"""
ZeroTackle Injury & Suspension Scraper
Scrapes real injury and suspension data from zerotackle.com
"""

import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
import time

logger = logging.getLogger(__name__)

class ZeroTackleScraper:
    def __init__(self):
        self.base_url = "https://www.zerotackle.com"
        self.injuries_url = f"{self.base_url}/nrl/injuries-suspensions/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def scrape_injuries_and_suspensions(self) -> Dict[str, List[Dict]]:
        """Scrape injury and suspension data from ZeroTackle"""
        try:
            print("Scraping injury data from ZeroTackle...")
            response = self.session.get(self.injuries_url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            injuries = []
            suspensions = []

            # Look for the specific structure from the HTML
            # Each team has a section with team flag image and player rows

            # Find all team sections by looking for team flag images
            team_images = soup.find_all('img', src=re.compile(r'team-flags'))
            print(f"Found {len(team_images)} team flag images")

            for img in team_images:
                team_name = self._extract_team_from_image(img)
                if team_name:
                    print(f"Processing team: {team_name}")

                    # Find the parent container and look for player data
                    team_container = img.find_parent(['div', 'section', 'article'])
                    if team_container:
                        players = self._extract_players_from_container(team_container, team_name)

                        for player in players:
                            if 'suspension' in player['reason'].lower():
                                suspensions.append(player)
                            else:
                                injuries.append(player)

            # Alternative approach: look for player links and data
            if not injuries and not suspensions:
                print("Trying alternative approach...")
                player_links = soup.find_all('a', href=re.compile(r'/rugby-league/players/'))
                print(f"Found {len(player_links)} player links")

                for link in player_links[:20]:  # Limit to first 20 for testing
                    player_data = self._extract_player_from_link(link)
                    if player_data:
                        if 'suspension' in player_data['reason'].lower():
                            suspensions.append(player_data)
                        else:
                            injuries.append(player_data)

            print(f"Found {len(injuries)} injuries and {len(suspensions)} suspensions")

            return {
                'injuries': injuries,
                'suspensions': suspensions,
                'last_updated': datetime.now().isoformat(),
                'source': 'zerotackle'
            }

        except Exception as e:
            print(f"Error scraping ZeroTackle: {e}")
            return {
                'injuries': [],
                'suspensions': [],
                'last_updated': datetime.now().isoformat(),
                'source': 'zerotackle',
                'error': str(e)
            }
    
    def _find_team_sections_alternative(self, soup: BeautifulSoup) -> List:
        """Alternative method to find team sections"""
        sections = []
        
        # Look for team headers (h3, h4 with team names)
        team_headers = soup.find_all(['h3', 'h4', 'h2'], string=re.compile(r'(Broncos|Raiders|Bulldogs|Sharks|Dolphins|Titans|Eagles|Storm|Warriors|Knights|Cowboys|Eels|Panthers|Rabbitohs|Dragons|Roosters|Tigers)', re.I))
        
        for header in team_headers:
            # Find the next table or list after this header
            next_element = header.find_next_sibling(['table', 'ul', 'div'])
            if next_element:
                sections.append({
                    'header': header,
                    'content': next_element
                })
        
        return sections
    
    def _parse_team_section(self, section) -> Optional[Dict]:
        """Parse a team section for injury/suspension data"""
        try:
            # Extract team name
            team_name = self._extract_team_name(section)
            if not team_name:
                return None
            
            players = []
            
            # Look for player rows in tables
            rows = section.find_all('tr') if hasattr(section, 'find_all') else []
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 3:  # Player, Reason, Expected Return
                    player_data = self._parse_player_row(cells, team_name)
                    if player_data:
                        players.append(player_data)
            
            # If no table rows, look for list items
            if not players:
                list_items = section.find_all('li') if hasattr(section, 'find_all') else []
                for item in list_items:
                    player_data = self._parse_player_list_item(item, team_name)
                    if player_data:
                        players.append(player_data)
            
            return {
                'team': team_name,
                'players': players
            }
            
        except Exception as e:
            logger.error(f"Error parsing team section: {e}")
            return None
    
    def _extract_team_name(self, section) -> Optional[str]:
        """Extract team name from section"""
        try:
            # Look for team name in various places
            team_patterns = [
                r'(Brisbane Broncos|Canberra Raiders|Canterbury-Bankstown Bulldogs|Cronulla Sharks|Dolphins|Gold Coast Titans|Manly Sea Eagles|Melbourne Storm|New Zealand Warriors|Newcastle Knights|North Queensland Cowboys|Parramatta Eels|Penrith Panthers|South Sydney Rabbitohs|St George Illawarra Dragons|Sydney Roosters|Wests Tigers)',
                r'(Broncos|Raiders|Bulldogs|Sharks|Dolphins|Titans|Eagles|Storm|Warriors|Knights|Cowboys|Eels|Panthers|Rabbitohs|Dragons|Roosters|Tigers)'
            ]
            
            # Check section text
            section_text = section.get_text() if hasattr(section, 'get_text') else str(section)
            
            for pattern in team_patterns:
                match = re.search(pattern, section_text, re.I)
                if match:
                    return self._normalize_team_name(match.group(1))
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting team name: {e}")
            return None
    
    def _normalize_team_name(self, team_name: str) -> str:
        """Normalize team name to standard format"""
        team_mapping = {
            'broncos': 'Brisbane Broncos',
            'raiders': 'Canberra Raiders',
            'bulldogs': 'Canterbury-Bankstown Bulldogs',
            'sharks': 'Cronulla Sharks',
            'dolphins': 'Dolphins',
            'titans': 'Gold Coast Titans',
            'eagles': 'Manly Sea Eagles',
            'storm': 'Melbourne Storm',
            'warriors': 'New Zealand Warriors',
            'knights': 'Newcastle Knights',
            'cowboys': 'North Queensland Cowboys',
            'eels': 'Parramatta Eels',
            'panthers': 'Penrith Panthers',
            'rabbitohs': 'South Sydney Rabbitohs',
            'dragons': 'St George Illawarra Dragons',
            'roosters': 'Sydney Roosters',
            'tigers': 'Wests Tigers'
        }
        
        team_lower = team_name.lower()
        for key, value in team_mapping.items():
            if key in team_lower:
                return value
        
        return team_name
    
    def _parse_player_row(self, cells, team_name: str) -> Optional[Dict]:
        """Parse a table row for player data"""
        try:
            if len(cells) < 3:
                return None
            
            # Extract player name (remove extra formatting)
            player_name = cells[0].get_text(strip=True)
            player_name = re.sub(r'\s+', ' ', player_name)
            
            # Extract reason
            reason = cells[1].get_text(strip=True)
            
            # Extract expected return
            expected_return = cells[2].get_text(strip=True)
            
            if not player_name or player_name.lower() in ['player', 'name']:
                return None
            
            return {
                'player_name': player_name,
                'team': team_name,
                'reason': reason,
                'expected_return': expected_return,
                'injury_type': self._categorize_injury(reason),
                'status': self._determine_status(reason, expected_return),
                'date_reported': datetime.now().isoformat(),
                'source': 'zerotackle'
            }
            
        except Exception as e:
            logger.error(f"Error parsing player row: {e}")
            return None
    
    def _parse_player_list_item(self, item, team_name: str) -> Optional[Dict]:
        """Parse a list item for player data"""
        try:
            text = item.get_text(strip=True)
            
            # Try to extract player name, reason, and return date
            # Format might be: "Player Name - Reason - Expected Return"
            parts = text.split(' - ')
            
            if len(parts) >= 2:
                player_name = parts[0].strip()
                reason = parts[1].strip()
                expected_return = parts[2].strip() if len(parts) > 2 else 'TBC'
                
                return {
                    'player_name': player_name,
                    'team': team_name,
                    'reason': reason,
                    'expected_return': expected_return,
                    'injury_type': self._categorize_injury(reason),
                    'status': self._determine_status(reason, expected_return),
                    'date_reported': datetime.now().isoformat(),
                    'source': 'zerotackle'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing list item: {e}")
            return None
    
    def _parse_full_page(self, soup: BeautifulSoup) -> tuple:
        """Parse the entire page for injury data as fallback"""
        injuries = []
        suspensions = []
        
        try:
            # Look for any text that mentions injuries or suspensions
            text_content = soup.get_text()
            
            # This is a basic fallback - in practice, you'd need to analyze
            # the actual HTML structure of the page
            
            # For now, return empty lists and log that manual parsing is needed
            logger.warning("Full page parsing not implemented - manual HTML analysis needed")
            
        except Exception as e:
            logger.error(f"Error in full page parsing: {e}")
        
        return injuries, suspensions

    def _extract_team_from_image(self, img) -> Optional[str]:
        """Extract team name from team flag image"""
        try:
            src = img.get('src', '')

            # Extract team name from image path
            team_patterns = {
                'brisbane-broncos': 'Brisbane Broncos',
                'canberra-raiders': 'Canberra Raiders',
                'canterbury-bankstown-bulldogs': 'Canterbury-Bankstown Bulldogs',
                'cronulla-sharks': 'Cronulla Sharks',
                'dolphins': 'Dolphins',
                'gold-coast-titans': 'Gold Coast Titans',
                'manly-sea-eagles': 'Manly Sea Eagles',
                'melbourne-storm': 'Melbourne Storm',
                'new-zealand-warriors': 'New Zealand Warriors',
                'newcastle-knights': 'Newcastle Knights',
                'north-queensland-cowboys': 'North Queensland Cowboys',
                'parramatta-eels': 'Parramatta Eels',
                'penrith-panthers': 'Penrith Panthers',
                'south-sydney-rabbitohs': 'South Sydney Rabbitohs',
                'st-george-illawarra-dragons': 'St George Illawarra Dragons',
                'sydney-roosters': 'Sydney Roosters',
                'wests-tigers': 'Wests Tigers'
            }

            for pattern, team_name in team_patterns.items():
                if pattern in src:
                    return team_name

            return None

        except Exception as e:
            print(f"Error extracting team from image: {e}")
            return None

    def _extract_players_from_container(self, container, team_name: str) -> List[Dict]:
        """Extract player data from team container"""
        players = []

        try:
            # Look for player links and associated data
            player_links = container.find_all('a', href=re.compile(r'/rugby-league/players/'))

            for link in player_links:
                # Get player name from link text
                player_name = link.get_text(strip=True)

                # Look for injury/suspension info near the link
                parent_row = link.find_parent(['tr', 'div', 'li'])
                if parent_row:
                    row_text = parent_row.get_text()

                    # Try to extract reason and return date
                    # This is a simplified approach - would need refinement based on actual HTML
                    text_parts = row_text.split()

                    # Look for common injury/suspension terms
                    reason = 'Unknown'
                    expected_return = 'TBC'

                    if any(word in row_text.lower() for word in ['suspension', 'suspended']):
                        reason = 'Suspension'
                    elif any(word in row_text.lower() for word in ['acl', 'knee', 'hamstring', 'calf', 'shoulder', 'concussion']):
                        # Extract the injury type
                        for word in text_parts:
                            if word.lower() in ['acl', 'knee', 'hamstring', 'calf', 'shoulder', 'concussion', 'ankle', 'wrist']:
                                reason = word.title()
                                break

                    # Look for return dates
                    if 'round' in row_text.lower():
                        round_match = re.search(r'round\s+(\d+)', row_text, re.I)
                        if round_match:
                            expected_return = f"Round {round_match.group(1)}"
                    elif 'next season' in row_text.lower():
                        expected_return = 'Next Season'

                    if player_name and reason != 'Unknown':
                        players.append({
                            'player_name': player_name,
                            'team': team_name,
                            'reason': reason,
                            'expected_return': expected_return,
                            'injury_type': self._categorize_injury(reason),
                            'status': self._determine_status(reason, expected_return),
                            'date_reported': datetime.now().isoformat(),
                            'source': 'zerotackle'
                        })

        except Exception as e:
            print(f"Error extracting players from container: {e}")

        return players

    def _extract_player_from_link(self, link) -> Optional[Dict]:
        """Extract player data from a player link"""
        try:
            player_name = link.get_text(strip=True)

            # Find the parent row/container
            parent = link.find_parent(['tr', 'div', 'li', 'td'])
            if not parent:
                return None

            # Get all text from the parent
            parent_text = parent.get_text()

            # Try to extract team, reason, and return date
            # This is a basic implementation - would need refinement

            # Default values
            team = 'Unknown'
            reason = 'Unknown'
            expected_return = 'TBC'

            # Look for team names in the text
            team_names = ['Broncos', 'Raiders', 'Bulldogs', 'Sharks', 'Dolphins', 'Titans', 'Eagles', 'Storm', 'Warriors', 'Knights', 'Cowboys', 'Eels', 'Panthers', 'Rabbitohs', 'Dragons', 'Roosters', 'Tigers']
            for team_name in team_names:
                if team_name.lower() in parent_text.lower():
                    team = self._normalize_team_name(team_name)
                    break

            # Look for injury/suspension terms
            if 'suspension' in parent_text.lower():
                reason = 'Suspension'
            elif any(word in parent_text.lower() for word in ['acl', 'knee', 'hamstring', 'calf', 'shoulder', 'concussion', 'ankle']):
                for word in parent_text.split():
                    if word.lower() in ['acl', 'knee', 'hamstring', 'calf', 'shoulder', 'concussion', 'ankle']:
                        reason = word.title()
                        break

            # Look for return dates
            if 'round' in parent_text.lower():
                round_match = re.search(r'round\s+(\d+)', parent_text, re.I)
                if round_match:
                    expected_return = f"Round {round_match.group(1)}"
            elif 'next season' in parent_text.lower():
                expected_return = 'Next Season'

            if reason != 'Unknown':
                return {
                    'player_name': player_name,
                    'team': team,
                    'reason': reason,
                    'expected_return': expected_return,
                    'injury_type': self._categorize_injury(reason),
                    'status': self._determine_status(reason, expected_return),
                    'date_reported': datetime.now().isoformat(),
                    'source': 'zerotackle'
                }

            return None

        except Exception as e:
            print(f"Error extracting player from link: {e}")
            return None

    def _categorize_injury(self, reason: str) -> str:
        """Categorize the type of injury"""
        reason_lower = reason.lower()
        
        if 'suspension' in reason_lower:
            return 'Suspension'
        elif any(word in reason_lower for word in ['acl', 'knee']):
            return 'Knee'
        elif any(word in reason_lower for word in ['hamstring', 'calf', 'quad']):
            return 'Leg'
        elif any(word in reason_lower for word in ['shoulder', 'arm', 'wrist']):
            return 'Upper Body'
        elif any(word in reason_lower for word in ['head', 'concussion']):
            return 'Head'
        elif any(word in reason_lower for word in ['back', 'spine']):
            return 'Back'
        else:
            return 'Other'
    
    def _determine_status(self, reason: str, expected_return: str) -> str:
        """Determine player status"""
        reason_lower = reason.lower()
        return_lower = expected_return.lower()
        
        if 'suspension' in reason_lower:
            return 'Suspended'
        elif any(word in return_lower for word in ['tbc', 'unknown', 'indefinite']):
            return 'Indefinite'
        elif 'next season' in return_lower:
            return 'Season Ending'
        elif any(word in return_lower for word in ['round', 'week']):
            return 'Short Term'
        else:
            return 'Injured'

def main():
    """Test the scraper"""
    scraper = ZeroTackleScraper()
    data = scraper.scrape_injuries_and_suspensions()
    
    print(f"Injuries: {len(data['injuries'])}")
    print(f"Suspensions: {len(data['suspensions'])}")
    
    # Show first few entries
    for injury in data['injuries'][:3]:
        print(f"- {injury['player_name']} ({injury['team']}): {injury['reason']}")
    
    for suspension in data['suspensions'][:3]:
        print(f"- {suspension['player_name']} ({suspension['team']}): {suspension['reason']}")

if __name__ == "__main__":
    main()
