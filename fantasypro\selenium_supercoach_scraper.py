#!/usr/bin/env python3
"""
Selenium-based SuperCoach Scraper
Comprehensive data extraction for ML enhancement
"""

import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import base64
import re

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

logger = logging.getLogger(__name__)

class SeleniumSuperCoachScraper:
    """Advanced SuperCoach scraper using Selenium for comprehensive data extraction"""
    
    def __init__(self, email: Optional[str] = None, password: Optional[str] = None, headless: bool = True):
        self.email = email or self._get_encrypted_email()
        self.password = password or self._get_encrypted_password()
        self.headless = headless
        
        self.cache_dir = Path("data/supercoach_selenium_cache")
        self.cache_dir.mkdir(exist_ok=True)
        
        self.driver = None
        self.wait = None
        self.is_authenticated = False
        
        # Data extraction targets - Focus on coach/ownership data
        self.data_targets = {
            'ownership': '/nrl/classic/stats/ownership',
            'ladder': '/nrl/classic/ladder',
            'my_team': '/nrl/classic/team/select',
            'stats': '/nrl/classic/stats',
            'rankings': '/nrl/classic/rankings'
        }
    
    def _get_encrypted_email(self) -> str:
        """Get encrypted email"""
        encoded = "************************"
        return base64.b64decode(encoded).decode('utf-8')
    
    def _get_encrypted_password(self) -> str:
        """Get encrypted password"""
        encoded = "U3VwZXJLZW5ueTEyMyE="
        return base64.b64decode(encoded).decode('utf-8')
    
    def setup_driver(self):
        """Setup Chrome driver with optimal settings"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # Anti-detection settings
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Performance settings
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')
            
            # User agent
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Use webdriver-manager to automatically handle ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.wait = WebDriverWait(self.driver, 20)
            
            logger.info("✅ Chrome driver setup successful")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error setting up Chrome driver: {e}")
            return False
    
    def authenticate(self) -> bool:
        """Authenticate with SuperCoach using Auth0"""
        try:
            if not self.driver:
                if not self.setup_driver():
                    return False

            logger.info("🔐 Starting SuperCoach Auth0 authentication")

            # Navigate to main site first
            self.driver.get('https://www.supercoach.com.au')
            time.sleep(3)

            # Look for login button/link that redirects to Auth0
            login_selectors = [
                'a[href*="login"]',
                'button[data-testid="login"]',
                '.login-button',
                '#login-btn',
                '.auth-login',
                '[data-auth="login"]'
            ]

            login_element = None
            for selector in login_selectors:
                try:
                    login_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if login_element:
                        logger.info(f"Found login element with selector: {selector}")
                        break
                except NoSuchElementException:
                    continue

            # Also try XPath for text-based search
            if not login_element:
                try:
                    login_element = self.driver.find_element(By.XPATH, "//a[contains(text(), 'Login') or contains(text(), 'Sign In') or contains(text(), 'Log In')]")
                    logger.info("Found login element with XPath text search")
                except NoSuchElementException:
                    pass

            if login_element:
                # Click login button to redirect to Auth0
                logger.info("Clicking login button to redirect to Auth0")
                self.driver.execute_script("arguments[0].click();", login_element)
                time.sleep(5)
            else:
                # Try direct navigation to Auth0 login
                logger.info("No login button found, trying direct Auth0 URL")
                auth0_url = "https://login.newscorpaustralia.com/login?state=hKFo2SBER3lEYktxVFNnN2RHSS1LLVlUQVNfOXlqclBtUng5VaFupWxvZ2luo3RpZNkgT2lsanBRUkhpY1lfM2JwOWJ6T19TNUFwZmx0TEhoY2SjY2lk2SBaWUNvdGxpaHFhR3VhcVNzU3Z1MEwydnhEZFFYQ3cxNg&client=ZYCotlihqaGuaqSsSvu0L2vxDdQXCw16&protocol=oauth2&response_type=token%20id_token&scope=openid%20profile&audience=newscorpaustralia&site=supercoach&redirect_uri=https%3A%2F%2Fwww.supercoach.com.au%2Fassets%2Fsites%2Fnews%2Fauth0%2Fcallback.html%3FredirectUri%3Dhttps%253A%252F%252Fwww.supercoach.com.au%252Fnrl&prevent_sign_up=&nonce=MwchNFYLpvljaSzA0FtG0kx5JhtggrZ5&auth0Client=eyJuYW1lIjoiYXV0aDAuanMiLCJ2ZXJzaW9uIjoiOS4yOC4wIn0%3D"
                self.driver.get(auth0_url)
                time.sleep(5)

            # Wait for Auth0 login page to load
            logger.info("Waiting for Auth0 login page to load")

            # Auth0 specific selectors
            email_selectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[name="username"]',
                '#email',
                '#username',
                'input[placeholder*="email"]',
                'input[placeholder*="Email"]'
            ]

            email_field = None
            for selector in email_selectors:
                try:
                    email_field = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    logger.info(f"Found email field with selector: {selector}")
                    break
                except TimeoutException:
                    continue

            if not email_field:
                logger.error("❌ Could not find email field on Auth0 page")
                # Save page for debugging
                with open('auth0_debug.html', 'w', encoding='utf-8') as f:
                    f.write(self.driver.page_source)
                return False

            # Find password field
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                '#password'
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    logger.info(f"Found password field with selector: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not password_field:
                logger.error("❌ Could not find password field on Auth0 page")
                return False

            # Clear and enter credentials
            logger.info("Entering credentials")
            email_field.clear()
            email_field.send_keys(self.email)
            time.sleep(1)

            password_field.clear()
            password_field.send_keys(self.password)
            time.sleep(1)

            # Find and click submit button
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button[name="action"]',
                '.auth0-lock-submit',
                '[data-action-button-primary]',
                'button:contains("Continue")',
                'button:contains("Log In")',
                'button:contains("Sign In")'
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    if 'contains' in selector:
                        text_content = selector.split('contains(')[1].split(')')[0].strip('"')
                        xpath = f"//button[contains(text(), '{text_content}')]"
                        submit_button = self.driver.find_element(By.XPATH, xpath)
                    else:
                        submit_button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if submit_button:
                        logger.info(f"Found submit button with selector: {selector}")
                        break
                except NoSuchElementException:
                    continue

            if submit_button:
                logger.info("Clicking submit button")
                self.driver.execute_script("arguments[0].click();", submit_button)
                time.sleep(8)  # Wait longer for Auth0 redirect
            else:
                # Try pressing Enter
                logger.info("No submit button found, trying Enter key")
                password_field.send_keys('\n')
                time.sleep(8)

            # Wait for redirect back to SuperCoach
            logger.info("Waiting for redirect back to SuperCoach")

            # Check if authentication was successful
            if self._check_authentication():
                self.is_authenticated = True
                logger.info("✅ SuperCoach Auth0 authentication successful")

                # Navigate directly to team selection page for data access
                logger.info("🎯 Navigating to team selection page for data access")
                self.driver.get('https://www.supercoach.com.au/nrl/classic/team/select')
                time.sleep(5)

                # Verify we can access the team selection page
                if 'team/select' in self.driver.current_url.lower():
                    logger.info("✅ Successfully accessed team selection page")
                    return True
                else:
                    logger.warning("⚠️ Authentication successful but couldn't access team selection page")
                    return True  # Still consider auth successful

            else:
                logger.error("❌ SuperCoach Auth0 authentication failed")
                logger.info(f"Current URL: {self.driver.current_url}")
                # Save page for debugging
                with open('auth_failed_debug.html', 'w', encoding='utf-8') as f:
                    f.write(self.driver.page_source)
                return False

        except Exception as e:
            logger.error(f"❌ Error during Auth0 authentication: {e}")
            return False
    
    def _check_authentication(self) -> bool:
        """Check if authentication was successful after Auth0 redirect"""
        try:
            current_url = self.driver.current_url.lower()
            logger.info(f"Checking authentication for URL: {current_url}")

            # Check if we're back on SuperCoach domain (successful Auth0 redirect)
            if 'supercoach.com.au' in current_url and 'login.newscorpaustralia.com' not in current_url:
                logger.info("✅ Successfully redirected back to SuperCoach domain")

                # Additional checks for authenticated state
                page_source = self.driver.page_source.lower()

                # Success indicators for SuperCoach
                success_indicators = [
                    'my team', 'logout', 'my account', 'team management',
                    'dashboard', 'profile', 'nrl/my-team', 'user-menu'
                ]

                # Failure indicators
                failure_indicators = [
                    'login failed', 'invalid credentials', 'authentication failed',
                    'please try again'
                ]

                has_success = any(indicator in page_source for indicator in success_indicators)
                has_failure = any(indicator in page_source for indicator in failure_indicators)

                if has_failure:
                    logger.warning("❌ Authentication failed - found failure indicators")
                    return False

                if has_success:
                    logger.info("✅ Authentication successful - found success indicators")
                    return True

                # If we're on SuperCoach domain and no failure indicators, likely successful
                logger.info("✅ Authentication likely successful - on SuperCoach domain")
                return True

            # Check if we're still on Auth0 login page
            elif 'login.newscorpaustralia.com' in current_url:
                logger.warning("❌ Still on Auth0 login page - authentication failed")
                return False

            else:
                logger.warning("⚠️ Authentication status unclear - unexpected URL")
                return False

        except Exception as e:
            logger.error(f"❌ Error checking authentication: {e}")
            return False
    
    def scrape_team_selection_data(self) -> Dict[str, Any]:
        """Scrape comprehensive data from team selection page"""
        try:
            if not self.is_authenticated:
                logger.error("Not authenticated")
                return {}

            logger.info("🎯 Scraping team selection page data")

            # Ensure we're on the team selection page
            self.driver.get('https://www.supercoach.com.au/nrl/classic/team/select')
            time.sleep(5)

            team_data = {
                'players': [],
                'extracted_at': datetime.now().isoformat(),
                'salary_cap_info': {},
                'player_stats': {},
                'prices_breakevens': {},
                'ownership_data': {}
            }

            # Extract player data from the team selection interface
            player_data = self._extract_team_selection_players()
            if player_data:
                team_data['players'] = player_data

            # Extract salary cap information
            salary_cap_data = self._extract_salary_cap_info()
            if salary_cap_data:
                team_data['salary_cap_info'] = salary_cap_data

            # Extract player statistics and prices
            stats_data = self._extract_player_statistics()
            if stats_data:
                team_data['player_stats'] = stats_data

            return team_data

        except Exception as e:
            logger.error(f"Error scraping team selection data: {e}")
            return {}

    def _extract_team_selection_players(self) -> List[Dict[str, Any]]:
        """Extract player data from team selection page"""
        try:
            players = []

            # Look for player cards/rows in team selection
            player_selectors = [
                '.player-card',
                '.player-row',
                '.player-item',
                '[data-player-id]',
                '.sc-player',
                '.team-player'
            ]

            player_elements = []
            for selector in player_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        player_elements = elements
                        logger.info(f"Found {len(elements)} players with selector: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue

            # Extract data from each player element
            for element in player_elements[:100]:  # Limit to prevent timeout
                try:
                    player_data = self._extract_single_player_data(element)
                    if player_data:
                        players.append(player_data)
                except Exception as e:
                    logger.debug(f"Error extracting player data: {e}")
                    continue

            logger.info(f"Extracted data for {len(players)} players")
            return players

        except Exception as e:
            logger.error(f"Error extracting team selection players: {e}")
            return []

    def _extract_single_player_data(self, element) -> Optional[Dict[str, Any]]:
        """Extract comprehensive data for a single player"""
        try:
            player_data = {}

            # Extract player name
            name_selectors = ['.player-name', '.name', 'h3', 'h4', '[data-name]', '.sc-player-name']
            for selector in name_selectors:
                try:
                    name_elem = element.find_element(By.CSS_SELECTOR, selector)
                    if name_elem:
                        player_data['name'] = name_elem.text.strip()
                        break
                except:
                    continue

            # Extract price
            price_selectors = ['.price', '.cost', '[data-price]', '.player-price', '.sc-price']
            for selector in price_selectors:
                try:
                    price_elem = element.find_element(By.CSS_SELECTOR, selector)
                    if price_elem:
                        player_data['price'] = self._extract_price(price_elem.text)
                        break
                except:
                    continue

            # Extract position
            pos_selectors = ['.position', '.pos', '[data-position]', '.sc-position']
            for selector in pos_selectors:
                try:
                    pos_elem = element.find_element(By.CSS_SELECTOR, selector)
                    if pos_elem:
                        player_data['position'] = pos_elem.text.strip()
                        break
                except:
                    continue

            # Extract team
            team_selectors = ['.team', '.club', '[data-team]', '.sc-team']
            for selector in team_selectors:
                try:
                    team_elem = element.find_element(By.CSS_SELECTOR, selector)
                    if team_elem:
                        player_data['team'] = team_elem.text.strip()
                        break
                except:
                    continue

            # Extract average/points
            avg_selectors = ['.average', '.avg', '.points', '.sc-average', '[data-average]']
            for selector in avg_selectors:
                try:
                    avg_elem = element.find_element(By.CSS_SELECTOR, selector)
                    if avg_elem:
                        player_data['average'] = self._extract_number(avg_elem.text)
                        break
                except:
                    continue

            # Extract breakeven
            be_selectors = ['.breakeven', '.be', '.sc-breakeven', '[data-breakeven]']
            for selector in be_selectors:
                try:
                    be_elem = element.find_element(By.CSS_SELECTOR, selector)
                    if be_elem:
                        player_data['breakeven'] = self._extract_number(be_elem.text)
                        break
                except:
                    continue

            # Extract ownership percentage
            own_selectors = ['.ownership', '.owned', '.sc-ownership', '[data-ownership]']
            for selector in own_selectors:
                try:
                    own_elem = element.find_element(By.CSS_SELECTOR, selector)
                    if own_elem:
                        player_data['ownership'] = self._extract_percentage(own_elem.text)
                        break
                except:
                    continue

            # Extract any additional stats from the element text
            element_text = element.text
            if element_text:
                # Look for form indicators
                if 'form' in element_text.lower():
                    player_data['form_indicator'] = True

                # Look for injury indicators
                if any(indicator in element_text.lower() for indicator in ['inj', 'injured', 'out']):
                    player_data['injury_status'] = 'injured'

            return player_data if player_data.get('name') else None

        except Exception as e:
            logger.debug(f"Error extracting single player data: {e}")
            return None

    def _extract_salary_cap_info(self) -> Dict[str, Any]:
        """Extract salary cap and budget information"""
        try:
            salary_info = {}

            # Look for salary cap indicators
            cap_selectors = [
                '.salary-cap',
                '.budget',
                '.remaining',
                '.cap-remaining',
                '[data-salary]',
                '.sc-budget'
            ]

            for selector in cap_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if '$' in text:
                            # Extract monetary values
                            amount = self._extract_price(text)
                            if amount > 0:
                                if 'remaining' in selector or 'left' in text.lower():
                                    salary_info['remaining_budget'] = amount
                                elif 'total' in text.lower() or 'cap' in selector:
                                    salary_info['total_budget'] = amount
                                else:
                                    salary_info['current_spend'] = amount
                except:
                    continue

            return salary_info

        except Exception as e:
            logger.error(f"Error extracting salary cap info: {e}")
            return {}

    def _extract_player_statistics(self) -> Dict[str, Any]:
        """Extract detailed player statistics"""
        try:
            stats_data = {}

            # Look for statistics tables or sections
            stats_selectors = [
                '.stats-table',
                '.player-stats',
                '.statistics',
                '.sc-stats'
            ]

            for selector in stats_selectors:
                try:
                    stats_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in stats_elements:
                        # Extract table data if it's a table
                        if element.tag_name == 'table':
                            table_data = self._extract_table_data(element)
                            if table_data:
                                stats_data['table_stats'] = table_data
                        else:
                            # Extract text-based stats
                            text_stats = element.text
                            if text_stats:
                                stats_data['text_stats'] = text_stats
                except:
                    continue

            return stats_data

        except Exception as e:
            logger.error(f"Error extracting player statistics: {e}")
            return {}
    
    def _scrape_round_data(self, round_num: int) -> Dict[str, Any]:
        """Scrape data for a specific round"""
        try:
            # Try to select the round
            round_selector = self.driver.find_element(By.CSS_SELECTOR, 'select[name="round"]')
            round_selector.send_keys(str(round_num))
            time.sleep(2)
            
            return self._extract_player_stats_table()
            
        except Exception as e:
            logger.error(f"Error scraping round {round_num}: {e}")
            return {}
    
    def _scrape_current_round_data(self) -> Dict[str, Any]:
        """Scrape current round data"""
        try:
            return self._extract_player_stats_table()
        except Exception as e:
            logger.error(f"Error scraping current round: {e}")
            return {}
    
    def _extract_player_stats_table(self) -> Dict[str, Any]:
        """Extract player statistics from table"""
        try:
            # Look for stats table
            table_selectors = [
                'table.stats-table',
                '.player-stats-table',
                'table[data-testid="player-stats"]',
                'table'
            ]
            
            table = None
            for selector in table_selectors:
                try:
                    table = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not table:
                logger.warning("No stats table found")
                return {}
            
            # Extract table data
            headers = []
            header_row = table.find_element(By.CSS_SELECTOR, 'thead tr, tr:first-child')
            for th in header_row.find_elements(By.CSS_SELECTOR, 'th, td'):
                headers.append(th.text.strip())
            
            players = []
            rows = table.find_elements(By.CSS_SELECTOR, 'tbody tr, tr')[1:]  # Skip header
            
            for row in rows[:50]:  # Limit to prevent timeout
                cells = row.find_elements(By.CSS_SELECTOR, 'td')
                if len(cells) >= len(headers):
                    player_data = {}
                    for i, header in enumerate(headers):
                        if i < len(cells):
                            player_data[header.lower().replace(' ', '_')] = cells[i].text.strip()
                    
                    if player_data:
                        players.append(player_data)
            
            return {
                'headers': headers,
                'players': players,
                'total_players': len(players)
            }
            
        except Exception as e:
            logger.error(f"Error extracting stats table: {e}")
            return {}
    
    def scrape_ownership_data(self) -> Dict[str, Any]:
        """Scrape ownership percentages"""
        try:
            logger.info("📊 Scraping ownership data")
            
            self.driver.get('https://www.supercoach.com.au/nrl/ownership')
            time.sleep(3)
            
            return self._extract_ownership_table()
            
        except Exception as e:
            logger.error(f"Error scraping ownership: {e}")
            return {}
    
    def _extract_ownership_table(self) -> Dict[str, Any]:
        """Extract ownership data from table"""
        try:
            # Similar table extraction logic as stats
            table = self.driver.find_element(By.CSS_SELECTOR, 'table')
            
            ownership_data = {
                'players': [],
                'extracted_at': datetime.now().isoformat()
            }
            
            rows = table.find_elements(By.CSS_SELECTOR, 'tbody tr, tr')[1:]
            
            for row in rows[:100]:
                cells = row.find_elements(By.CSS_SELECTOR, 'td')
                if len(cells) >= 3:
                    player_data = {
                        'name': cells[0].text.strip(),
                        'team': cells[1].text.strip() if len(cells) > 1 else '',
                        'ownership_percentage': self._extract_percentage(cells[2].text) if len(cells) > 2 else 0,
                        'price': self._extract_price(cells[3].text) if len(cells) > 3 else 0
                    }
                    ownership_data['players'].append(player_data)
            
            return ownership_data
            
        except Exception as e:
            logger.error(f"Error extracting ownership table: {e}")
            return {}
    
    def scrape_comprehensive_data(self) -> Dict[str, Any]:
        """Scrape all critical SuperCoach data from team selection page"""
        try:
            if not self.authenticate():
                logger.error("Authentication failed")
                return {}

            comprehensive_data = {
                'collection_timestamp': datetime.now().isoformat(),
                'data': {},
                'source': 'supercoach_team_selection'
            }

            # 1. Team Selection Data (Primary source for all player data)
            logger.info("1️⃣ Collecting team selection data (players, prices, stats)...")
            team_data = self.scrape_team_selection_data()
            if team_data:
                comprehensive_data['data']['team_selection'] = team_data
                logger.info(f"✅ Collected data for {len(team_data.get('players', []))} players")

            # 2. Additional Navigation for More Data
            logger.info("2️⃣ Collecting additional SuperCoach data...")

            # Try to get ownership data from a different page
            try:
                self.driver.get('https://www.supercoach.com.au/nrl/stats')
                time.sleep(3)
                stats_data = self._extract_stats_page_data()
                if stats_data:
                    comprehensive_data['data']['stats_page'] = stats_data
            except Exception as e:
                logger.warning(f"Could not collect stats page data: {e}")

            # Try to get ladder/rankings
            try:
                self.driver.get('https://www.supercoach.com.au/nrl/ladder')
                time.sleep(3)
                ladder_data = self._extract_ladder_data()
                if ladder_data:
                    comprehensive_data['data']['ladder'] = ladder_data
            except Exception as e:
                logger.warning(f"Could not collect ladder data: {e}")

            # 3. Save comprehensive data
            self._save_comprehensive_data(comprehensive_data)

            # 4. Generate summary
            summary = self._generate_data_summary(comprehensive_data)
            logger.info(f"📊 Data collection summary: {summary}")

            return comprehensive_data

        except Exception as e:
            logger.error(f"Error in comprehensive data collection: {e}")
            return {}
        finally:
            self.cleanup()

    def _extract_stats_page_data(self) -> Dict[str, Any]:
        """Extract data from stats page"""
        try:
            stats_data = {
                'extracted_at': datetime.now().isoformat(),
                'page_url': self.driver.current_url
            }

            # Look for any tables on the stats page
            tables = self.driver.find_elements(By.CSS_SELECTOR, 'table')
            if tables:
                stats_data['tables'] = []
                for i, table in enumerate(tables[:3]):  # Limit to first 3 tables
                    table_data = self._extract_table_data(table)
                    if table_data:
                        stats_data['tables'].append({
                            'table_index': i,
                            'data': table_data
                        })

            return stats_data

        except Exception as e:
            logger.error(f"Error extracting stats page data: {e}")
            return {}

    def _extract_ladder_data(self) -> Dict[str, Any]:
        """Extract ladder/rankings data"""
        try:
            ladder_data = {
                'extracted_at': datetime.now().isoformat(),
                'page_url': self.driver.current_url,
                'rankings': []
            }

            # Look for ladder table
            table = self.driver.find_element(By.CSS_SELECTOR, 'table')
            if table:
                table_data = self._extract_table_data(table)
                if table_data and len(table_data) > 1:
                    headers = table_data[0]
                    for row in table_data[1:]:
                        if len(row) >= 3:
                            ranking = {
                                'rank': self._extract_number(row[0]) if row[0] else 0,
                                'coach_name': row[1] if len(row) > 1 else '',
                                'total_points': self._extract_number(row[2]) if len(row) > 2 else 0
                            }
                            ladder_data['rankings'].append(ranking)

            return ladder_data

        except Exception as e:
            logger.error(f"Error extracting ladder data: {e}")
            return {}

    def _generate_data_summary(self, data: Dict[str, Any]) -> str:
        """Generate a summary of collected data"""
        try:
            summary_parts = []

            team_data = data.get('data', {}).get('team_selection', {})
            if team_data:
                player_count = len(team_data.get('players', []))
                summary_parts.append(f"{player_count} players")

                if team_data.get('salary_cap_info'):
                    summary_parts.append("salary cap data")

            if data.get('data', {}).get('stats_page'):
                summary_parts.append("stats page data")

            if data.get('data', {}).get('ladder'):
                ladder_count = len(data['data']['ladder'].get('rankings', []))
                summary_parts.append(f"{ladder_count} ladder entries")

            return ", ".join(summary_parts) if summary_parts else "minimal data collected"

        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return "summary generation failed"
    
    def _scrape_prices_breakevens(self) -> Dict[str, Any]:
        """Scrape price and breakeven data"""
        try:
            self.driver.get('https://www.supercoach.com.au/nrl/prices')
            time.sleep(3)
            return self._extract_player_stats_table()
        except Exception as e:
            logger.error(f"Error scraping prices/breakevens: {e}")
            return {}
    
    def _scrape_fixtures(self) -> Dict[str, Any]:
        """Scrape fixture data"""
        try:
            self.driver.get('https://www.supercoach.com.au/nrl/fixtures')
            time.sleep(3)
            return self._extract_fixtures_table()
        except Exception as e:
            logger.error(f"Error scraping fixtures: {e}")
            return {}
    
    def _extract_fixtures_table(self) -> Dict[str, Any]:
        """Extract fixtures from table"""
        try:
            fixtures = []
            
            # Look for fixture elements
            fixture_elements = self.driver.find_elements(By.CSS_SELECTOR, '.fixture, .match, .game')
            
            for element in fixture_elements:
                try:
                    fixture_data = {
                        'teams': element.find_element(By.CSS_SELECTOR, '.teams, .match-teams').text,
                        'date': element.find_element(By.CSS_SELECTOR, '.date, .match-date').text,
                        'venue': element.find_element(By.CSS_SELECTOR, '.venue, .match-venue').text
                    }
                    fixtures.append(fixture_data)
                except NoSuchElementException:
                    continue
            
            return {
                'fixtures': fixtures,
                'extracted_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error extracting fixtures: {e}")
            return {}
    
    def _scrape_team_sheets(self) -> Dict[str, Any]:
        """Scrape team sheet data"""
        try:
            self.driver.get('https://www.supercoach.com.au/nrl/teams')
            time.sleep(3)
            return self._extract_team_data()
        except Exception as e:
            logger.error(f"Error scraping team sheets: {e}")
            return {}
    
    def _extract_team_data(self) -> Dict[str, Any]:
        """Extract team lineup data"""
        try:
            teams = []
            
            team_elements = self.driver.find_elements(By.CSS_SELECTOR, '.team, .lineup')
            
            for element in team_elements:
                try:
                    team_data = {
                        'team_name': element.find_element(By.CSS_SELECTOR, '.team-name, h3').text,
                        'players': []
                    }
                    
                    player_elements = element.find_elements(By.CSS_SELECTOR, '.player, .lineup-player')
                    for player_elem in player_elements:
                        player_data = {
                            'name': player_elem.text.strip(),
                            'position': player_elem.get_attribute('data-position') or ''
                        }
                        team_data['players'].append(player_data)
                    
                    teams.append(team_data)
                except NoSuchElementException:
                    continue
            
            return {
                'teams': teams,
                'extracted_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error extracting team data: {e}")
            return {}
    
    def _extract_percentage(self, text: str) -> float:
        """Extract percentage from text"""
        try:
            match = re.search(r'(\d+\.?\d*)%', text)
            return float(match.group(1)) if match else 0.0
        except:
            return 0.0
    
    def _extract_price(self, text: str) -> int:
        """Extract price from text"""
        try:
            # Remove $ and commas, extract number
            clean_text = re.sub(r'[$,]', '', text)
            match = re.search(r'(\d+)', clean_text)
            return int(match.group(1)) if match else 0
        except:
            return 0
    
    def _save_comprehensive_data(self, data: Dict[str, Any]):
        """Save comprehensive data to cache"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            cache_file = self.cache_dir / f"selenium_supercoach_data_{timestamp}.json"
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Also save as latest
            latest_file = self.cache_dir / "selenium_supercoach_latest.json"
            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Saved comprehensive SuperCoach data to {cache_file}")
            
        except Exception as e:
            logger.error(f"Error saving data: {e}")
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("🧹 Browser cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

def main():
    """Test the Selenium SuperCoach scraper"""
    print("🏆 Testing Selenium SuperCoach Scraper")
    
    scraper = SeleniumSuperCoachScraper(headless=False)  # Visible for testing
    
    try:
        # Test comprehensive data collection
        data = scraper.scrape_comprehensive_data()
        
        if data:
            print("✅ Data collection successful!")
            print(f"📊 Data sections: {list(data.get('data', {}).keys())}")
            
            for section, section_data in data.get('data', {}).items():
                if isinstance(section_data, dict):
                    count = len(section_data.get('players', section_data.get('fixtures', section_data.get('teams', []))))
                    print(f"  - {section}: {count} items")
        else:
            print("❌ Data collection failed")
            
    except Exception as e:
        print(f"💥 Error: {e}")
    finally:
        scraper.cleanup()

if __name__ == "__main__":
    main()
