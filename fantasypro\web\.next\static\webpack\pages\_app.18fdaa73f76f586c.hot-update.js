"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"./node_modules/next/node_modules/@swc/helpers/esm/_class_call_check.js\");\n/* harmony import */ var _swc_helpers_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/_/_inherits */ \"./node_modules/next/node_modules/@swc/helpers/esm/_inherits.js\");\n/* harmony import */ var _swc_helpers_create_super__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_create_super */ \"./node_modules/next/node_modules/@swc/helpers/esm/_create_super.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n\n\nvar ErrorBoundary = /*#__PURE__*/ function(Component) {\n    \"use strict\";\n    (0,_swc_helpers_inherits__WEBPACK_IMPORTED_MODULE_2__._)(ErrorBoundary, Component);\n    var _super = (0,_swc_helpers_create_super__WEBPACK_IMPORTED_MODULE_3__._)(ErrorBoundary);\n    function ErrorBoundary(props) {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_4__._)(this, ErrorBoundary);\n        var _this;\n        _this = _super.call(this, props);\n        _this.resetTimeoutId = null;\n        _this.resetErrorBoundary = function() {\n            if (_this.resetTimeoutId) {\n                clearTimeout(_this.resetTimeoutId);\n            }\n            _this.setState({\n                hasError: false,\n                error: undefined,\n                errorInfo: undefined,\n                eventId: undefined\n            });\n        };\n        _this.logErrorToService = function(error, errorInfo) {\n            // In a real app, you might send this to Sentry, LogRocket, etc.\n            try {\n                var errorData = {\n                    message: error.message,\n                    stack: error.stack,\n                    componentStack: errorInfo.componentStack,\n                    timestamp: new Date().toISOString(),\n                    userAgent:  true ? window.navigator.userAgent : 0,\n                    url:  true ? window.location.href : 0\n                };\n                console.warn(\"\\uD83D\\uDCCA Error logged:\", errorData);\n                // Store in localStorage for debugging\n                if (true) {\n                    var errors = JSON.parse(localStorage.getItem(\"fantasypro-errors\") || \"[]\");\n                    errors.push(errorData);\n                    // Keep only last 10 errors\n                    if (errors.length > 10) {\n                        errors.splice(0, errors.length - 10);\n                    }\n                    localStorage.setItem(\"fantasypro-errors\", JSON.stringify(errors));\n                }\n            } catch (loggingError) {\n                console.error(\"Failed to log error:\", loggingError);\n            }\n        };\n        _this.state = {\n            hasError: false\n        };\n        return _this;\n    }\n    var _proto = ErrorBoundary.prototype;\n    _proto.componentDidCatch = function componentDidCatch(error, errorInfo) {\n        console.error(\"\\uD83D\\uDEA8 ErrorBoundary caught an error:\", error, errorInfo);\n        // Update state with error info\n        this.setState({\n            error: error,\n            errorInfo: errorInfo\n        });\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // Log to external service (if needed)\n        this.logErrorToService(error, errorInfo);\n    };\n    _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n        var _this_props = this.props, resetKeys = _this_props.resetKeys, resetOnPropsChange = _this_props.resetOnPropsChange;\n        var hasError = this.state.hasError;\n        if (hasError && prevProps.resetKeys !== resetKeys) {\n            if (resetKeys) {\n                var hasResetKeyChanged = resetKeys.some(function(key, index) {\n                    var _prevProps_resetKeys;\n                    return ((_prevProps_resetKeys = prevProps.resetKeys) === null || _prevProps_resetKeys === void 0 ? void 0 : _prevProps_resetKeys[index]) !== key;\n                });\n                if (hasResetKeyChanged) {\n                    this.resetErrorBoundary();\n                }\n            }\n        }\n        if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {\n            this.resetErrorBoundary();\n        }\n    };\n    _proto.componentWillUnmount = function componentWillUnmount() {\n        if (this.resetTimeoutId) {\n            clearTimeout(this.resetTimeoutId);\n        }\n    };\n    _proto.render = function render() {\n        if (this.state.hasError) {\n            var _this_state_error, _this_state_error1, _this_state_errorInfo;\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-slate-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/20 border border-red-500/30 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold\",\n                                                children: \"!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-red-400\",\n                                            children: \"Something went wrong\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-300 mb-2\",\n                                                    children: \"Error Details:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 rounded p-3 text-sm text-red-200 font-mono\",\n                                                    children: ((_this_state_error = this.state.error) === null || _this_state_error === void 0 ? void 0 : _this_state_error.message) || \"Unknown error occurred\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        ((_this_state_error1 = this.state.error) === null || _this_state_error1 === void 0 ? void 0 : _this_state_error1.stack) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-300 mb-2\",\n                                                    children: \"Stack Trace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto\",\n                                                    children: this.state.error.stack\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this),\n                                        ((_this_state_errorInfo = this.state.errorInfo) === null || _this_state_errorInfo === void 0 ? void 0 : _this_state_errorInfo.componentStack) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-300 mb-2\",\n                                                    children: \"Component Stack:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto\",\n                                                    children: this.state.errorInfo.componentStack\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: function() {\n                                                        return window.location.reload();\n                                                    },\n                                                    className: \"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors\",\n                                                    children: \"Reload Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: function() {\n                                                        return window.history.back();\n                                                    },\n                                                    className: \"px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg text-sm font-medium transition-colors\",\n                                                    children: \"Go Back\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"If this error persists, please check the browser console for more details.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    };\n    ErrorBoundary.getDerivedStateFromError = function getDerivedStateFromError(error) {\n        // Generate a unique event ID for this error\n        var eventId = \"error_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n        return {\n            hasError: true,\n            error: error,\n            eventId: eventId\n        };\n    };\n    return ErrorBoundary;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ErrorBoundary);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ErrorBoundary.tsx\n"));

/***/ })

});