/**
 * Data Transformation Service
 * Converts SportRadar API data to FantasyPro internal format
 */

import { SportRadarPlayer, SportRadarTeam, SportRadarInjury, SportRadarFixture } from './sportradar';

// Internal FantasyPro types (matching existing structure)
export interface Player {
  id: string;
  name: string;
  position: string;
  team: string;
  team_id?: string;
  price: number;
  points: number;
  average: number;
  form: number;
  ownership?: number;
  breakeven?: number;
  injury_status?: string;
  jersey_number?: number;
  age?: number;
  height?: number;
  weight?: number;
  nationality?: string;
  is_available?: boolean;
  created_at: string;
  updated_at: string;
}

export interface Team {
  id: string;
  name: string;
  abbreviation: string;
  city?: string;
  venue?: string;
  venue_capacity?: number;
  players?: Player[];
}

export interface Injury {
  id: string;
  player: {
    name: string;
    team: string;
    position: string;
    price: number;
    ownership: number;
  };
  injury_type: string;
  severity: 'minor' | 'moderate' | 'major' | 'season_ending';
  status: 'injured' | 'test' | 'cleared' | 'doubtful';
  expected_return: string;
  weeks_out: number;
  description: string;
  impact_rating: number;
  last_updated: string;
  source: string;
}

export interface Fixture {
  id: string;
  home_team: string;
  away_team: string;
  start_time: string;
  venue?: string;
  round?: number;
  status: string;
}

/**
 * Position mapping from SportRadar to SuperCoach positions
 */
const POSITION_MAPPING: { [key: string]: string } = {
  'fullback': 'FLB',
  'wing': 'CTW',
  'centre': 'CTW',
  'five-eighth': '5/8',
  'halfback': 'HFB',
  'hooker': 'HOK',
  'prop': 'FRF',
  'second-row': '2RF',
  'lock': 'LCK',
  // Alternative spellings
  'winger': 'CTW',
  'center': 'CTW',
  'stand-off': '5/8',
  'scrum-half': 'HFB',
  'front-row': 'FRF',
  'back-row': '2RF',
  'loose-forward': 'LCK'
};

/**
 * Team name mapping from SportRadar to common names
 */
const TEAM_MAPPING: { [key: string]: string } = {
  'brisbane': 'Brisbane Broncos',
  'sydney roosters': 'Sydney Roosters',
  'melbourne': 'Melbourne Storm',
  'penrith': 'Penrith Panthers',
  'cronulla': 'Cronulla Sharks',
  'manly': 'Manly Sea Eagles',
  'south sydney': 'South Sydney Rabbitohs',
  'parramatta': 'Parramatta Eels',
  'newcastle': 'Newcastle Knights',
  'canberra': 'Canberra Raiders',
  'gold coast': 'Gold Coast Titans',
  'st george illawarra': 'St George Illawarra Dragons',
  'canterbury': 'Canterbury Bulldogs',
  'wests tigers': 'Wests Tigers',
  'north queensland': 'North Queensland Cowboys',
  'new zealand': 'New Zealand Warriors',
  'dolphins': 'Dolphins'
};

/**
 * Data Transformation Service Class
 */
export class DataTransformService {
  
  /**
   * Transform SportRadar player to FantasyPro player format
   */
  static transformPlayer(sportRadarPlayer: any, teamName?: string): Player {
    const now = new Date().toISOString();
    
    // Calculate age if date_of_birth is available
    let age: number | undefined;
    if (sportRadarPlayer.date_of_birth) {
      const birthDate = new Date(sportRadarPlayer.date_of_birth);
      const today = new Date();
      age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
    }

    // Map position to SuperCoach format
    const position = this.mapPosition(sportRadarPlayer.position);
    
    // Map team name
    const team = teamName || sportRadarPlayer.team_name || 'Unknown Team';
    const mappedTeam = this.mapTeamName(team);

    return {
      id: sportRadarPlayer.id,
      name: sportRadarPlayer.name,
      position,
      team: mappedTeam,
      team_id: sportRadarPlayer.team_id,
      // Default fantasy values (will be updated with real data later)
      price: this.generateEstimatedPrice(position),
      points: 0,
      average: 0,
      form: 0,
      ownership: 0,
      breakeven: 0,
      // SportRadar data
      jersey_number: sportRadarPlayer.jersey_number,
      age,
      height: sportRadarPlayer.height,
      weight: sportRadarPlayer.weight,
      nationality: sportRadarPlayer.nationality,
      is_available: true,
      created_at: now,
      updated_at: now
    };
  }

  /**
   * Transform SportRadar team to FantasyPro team format
   */
  static transformTeam(sportRadarTeam: SportRadarTeam): Team {
    const mappedName = this.mapTeamName(sportRadarTeam.name);
    
    return {
      id: sportRadarTeam.id,
      name: mappedName,
      abbreviation: sportRadarTeam.abbreviation,
      city: sportRadarTeam.venue?.city,
      venue: sportRadarTeam.venue?.name,
      venue_capacity: sportRadarTeam.venue?.capacity,
      players: sportRadarTeam.players?.map(player => 
        this.transformPlayer(player, mappedName)
      )
    };
  }

  /**
   * Transform SportRadar injury to FantasyPro injury format
   */
  static transformInjury(sportRadarInjury: SportRadarInjury, playerData?: any): Injury {
    const severity = this.mapInjurySeverity(sportRadarInjury.injury_type, sportRadarInjury.status);
    const weeksOut = this.calculateWeeksOut(sportRadarInjury.expected_return);
    const impactRating = this.calculateInjuryImpact(severity, playerData?.ownership || 0);

    return {
      id: `${sportRadarInjury.player.id}_${Date.now()}`,
      player: {
        name: sportRadarInjury.player.name,
        team: this.mapTeamName((sportRadarInjury.player as any).team_name || 'Unknown'),
        position: this.mapPosition(sportRadarInjury.player.position),
        price: playerData?.price || this.generateEstimatedPrice(sportRadarInjury.player.position),
        ownership: playerData?.ownership || 0
      },
      injury_type: sportRadarInjury.injury_type,
      severity,
      status: this.mapInjuryStatus(sportRadarInjury.status),
      expected_return: sportRadarInjury.expected_return || this.estimateReturnDate(severity),
      weeks_out: weeksOut,
      description: sportRadarInjury.description || `${sportRadarInjury.injury_type} - Status: ${sportRadarInjury.status}`,
      impact_rating: impactRating,
      last_updated: sportRadarInjury.updated,
      source: 'SportRadar API'
    };
  }

  /**
   * Transform SportRadar fixture to FantasyPro fixture format
   */
  static transformFixture(sportRadarFixture: SportRadarFixture): Fixture {
    return {
      id: sportRadarFixture.id,
      home_team: this.mapTeamName(sportRadarFixture.home_team.name),
      away_team: this.mapTeamName(sportRadarFixture.away_team.name),
      start_time: sportRadarFixture.start_time,
      venue: sportRadarFixture.venue?.name,
      round: sportRadarFixture.round?.number,
      status: sportRadarFixture.status
    };
  }

  /**
   * Map SportRadar position to SuperCoach position
   */
  private static mapPosition(position: string): string {
    if (!position) return 'UTL';
    
    const normalizedPosition = position.toLowerCase().trim();
    return POSITION_MAPPING[normalizedPosition] || 'UTL';
  }

  /**
   * Map team name to standard format
   */
  private static mapTeamName(teamName: string): string {
    if (!teamName) return 'Unknown Team';
    
    const normalizedName = teamName.toLowerCase().trim();
    
    // Check for exact matches first
    if (TEAM_MAPPING[normalizedName]) {
      return TEAM_MAPPING[normalizedName];
    }
    
    // Check for partial matches
    for (const [key, value] of Object.entries(TEAM_MAPPING)) {
      if (normalizedName.includes(key) || key.includes(normalizedName)) {
        return value;
      }
    }
    
    // Return original name if no mapping found
    return teamName;
  }

  /**
   * Generate estimated price based on position
   */
  private static generateEstimatedPrice(position: string): number {
    const basePrices: { [key: string]: number } = {
      'FLB': 650000,
      'CTW': 550000,
      'HFB': 700000,
      '5/8': 650000,
      'HOK': 600000,
      'FRF': 500000,
      '2RF': 550000,
      'LCK': 580000
    };
    
    const basePrice = basePrices[position] || 500000;
    // Add some randomization (±20%)
    const variation = (Math.random() - 0.5) * 0.4;
    return Math.round(basePrice * (1 + variation));
  }

  /**
   * Map injury severity
   */
  private static mapInjurySeverity(injuryType: string, status: string): 'minor' | 'moderate' | 'major' | 'season_ending' {
    const type = injuryType.toLowerCase();
    const stat = status.toLowerCase();
    
    if (stat === 'out' || type.includes('season') || type.includes('acl') || type.includes('achilles')) {
      return 'season_ending';
    }
    
    if (type.includes('fracture') || type.includes('surgery') || type.includes('tear')) {
      return 'major';
    }
    
    if (type.includes('strain') || type.includes('sprain') || stat === 'doubtful') {
      return 'moderate';
    }
    
    return 'minor';
  }

  /**
   * Map injury status
   */
  private static mapInjuryStatus(status: string): 'injured' | 'test' | 'cleared' | 'doubtful' {
    const stat = status.toLowerCase();
    
    if (stat === 'questionable' || stat === 'doubtful') return 'doubtful';
    if (stat === 'out' || stat === 'injured') return 'injured';
    if (stat === 'probable') return 'test';
    
    return 'injured';
  }

  /**
   * Calculate weeks out from expected return date
   */
  private static calculateWeeksOut(expectedReturn?: string): number {
    if (!expectedReturn) return 0;
    
    const returnDate = new Date(expectedReturn);
    const now = new Date();
    const diffTime = returnDate.getTime() - now.getTime();
    const diffWeeks = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
    
    return Math.max(0, diffWeeks);
  }

  /**
   * Estimate return date based on severity
   */
  private static estimateReturnDate(severity: 'minor' | 'moderate' | 'major' | 'season_ending'): string {
    const now = new Date();
    let weeksToAdd = 0;
    
    switch (severity) {
      case 'minor': weeksToAdd = 1; break;
      case 'moderate': weeksToAdd = 3; break;
      case 'major': weeksToAdd = 8; break;
      case 'season_ending': weeksToAdd = 26; break;
    }
    
    now.setDate(now.getDate() + (weeksToAdd * 7));
    return now.toISOString().split('T')[0];
  }

  /**
   * Calculate injury impact rating
   */
  private static calculateInjuryImpact(severity: string, ownership: number): number {
    let baseImpact = 0;
    
    switch (severity) {
      case 'minor': baseImpact = 3; break;
      case 'moderate': baseImpact = 5; break;
      case 'major': baseImpact = 8; break;
      case 'season_ending': baseImpact = 10; break;
    }
    
    // Adjust based on ownership (higher ownership = higher impact)
    const ownershipMultiplier = 1 + (ownership / 100);
    
    return Math.min(10, baseImpact * ownershipMultiplier);
  }

  /**
   * Batch transform players
   */
  static transformPlayers(sportRadarPlayers: any[]): Player[] {
    return sportRadarPlayers.map(player => this.transformPlayer(player));
  }

  /**
   * Batch transform teams
   */
  static transformTeams(sportRadarTeams: SportRadarTeam[]): Team[] {
    return sportRadarTeams.map(team => this.transformTeam(team));
  }

  /**
   * Batch transform injuries
   */
  static transformInjuries(sportRadarInjuries: SportRadarInjury[], playerDataMap?: Map<string, any>): Injury[] {
    return sportRadarInjuries.map(injury => {
      const playerData = playerDataMap?.get(injury.player.id);
      return this.transformInjury(injury, playerData);
    });
  }

  /**
   * Batch transform fixtures
   */
  static transformFixtures(sportRadarFixtures: SportRadarFixture[]): Fixture[] {
    return sportRadarFixtures.map(fixture => this.transformFixture(fixture));
  }
}

export default DataTransformService;
