#!/usr/bin/env python3
"""
SportRadar API Client for NRL Data
Fetches comprehensive NRL player and team data from SportRadar API
"""

import requests
import json
import time
from typing import List, Dict, Optional
import logging
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SportRadarNRLClient:
    def __init__(self, api_key: str):
        self.api_key = api_key
        # Correct base URL from SportRadar documentation
        self.base_url = "https://api.sportradar.com/rugby-league/trial/v3/en"
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json',
            'User-Agent': 'FantasyPro/1.0'
        })
        
        # Rate limiting - SportRadar typically allows 1 request per second for trial
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds
        
        # NRL Competition ID - NRL Premiership from SportRadar API
        self.nrl_competition_id = "sr:competition:294"
        
        # Position mappings from SportRadar to our format
        self.position_mappings = {
            'fullback': 'FLB',
            'wing': 'CTW',
            'centre': 'CTW',
            'five_eighth': '5/8',
            'halfback': 'HFB',
            'hooker': 'HOK',
            'prop': 'FRF',
            'second_row': '2RF',
            'lock': 'LCK',
            'interchange': 'INT'
        }

    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """
        Make a rate-limited request to SportRadar API
        """
        # Rate limiting
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        url = f"{self.base_url}/{endpoint}"
        
        # Add API key to parameters
        if params is None:
            params = {}
        params['api_key'] = self.api_key
        
        try:
            logger.info(f"Making request to: {endpoint}")
            response = self.session.get(url, params=params, timeout=30)
            self.last_request_time = time.time()
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                logger.warning("Rate limit exceeded, waiting...")
                time.sleep(5)
                return self._make_request(endpoint, params)
            else:
                logger.error(f"API request failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error making request to {endpoint}: {e}")
            return None

    def get_competitions(self) -> Optional[List[Dict]]:
        """
        Get list of available competitions to find NRL competition ID
        """
        return self._make_request("competitions.json")

    def get_all_players(self) -> List[Dict]:
        """
        Get all NRL players from SportRadar using the correct API structure
        """
        players = []

        try:
            # Get current season
            seasons_data = self._make_request(f"competitions/{self.nrl_competition_id}/seasons.json")
            if not seasons_data or 'seasons' not in seasons_data:
                logger.error("Could not get seasons data")
                return players

            # Get latest season
            latest_season = seasons_data['seasons'][-1]
            season_id = latest_season.get('id')

            # Get teams from standings
            standings_data = self._make_request(f"seasons/{season_id}/standings.json")
            if not standings_data or 'standings' not in standings_data:
                logger.error("Could not get standings data")
                return players

            # Extract teams from standings
            main_standing = standings_data['standings'][0]
            if 'groups' in main_standing and main_standing['groups']:
                group = main_standing['groups'][0]
                if 'standings' in group:
                    team_standings = group['standings']

                    logger.info(f"Found {len(team_standings)} NRL teams")

                    # Get players from each team
                    for standing in team_standings:
                        competitor = standing.get('competitor', {})
                        team_id = competitor.get('id')
                        team_name = competitor.get('name')

                        if team_id:
                            logger.info(f"Getting players from {team_name}...")
                            team_players = self.get_team_players(team_id, team_name)
                            players.extend(team_players)

                            # Rate limiting between teams
                            time.sleep(1)

        except Exception as e:
            logger.error(f"Error getting all players: {e}")

        return players

    def get_team_players(self, team_id: str, team_name: str = None) -> List[Dict]:
        """
        Get all players from a specific team using the correct SportRadar endpoint
        """
        players = []

        # Use the correct endpoint: competitors/{team_id}/profile.json
        team_data = self._make_request(f"competitors/{team_id}/profile.json")

        if team_data and 'players' in team_data:
            if not team_name:
                team_name = team_data.get('competitor', {}).get('name', 'Unknown Team')

            for player_data in team_data['players']:
                player = self._format_player_data(player_data, team_name)
                if player:
                    players.append(player)

        return players

    def get_player_profile(self, player_id: str) -> Optional[Dict]:
        """
        Get detailed profile for a specific player
        """
        player_data = self._make_request(f"players/{player_id}/profile.json")

        if player_data:
            return self._format_player_data(player_data)

        return None

    def get_injury_reports(self) -> List[Dict]:
        """
        Get current injury reports for all NRL players
        """
        injuries = []

        try:
            # Try different injury endpoints based on SportRadar API structure
            season_id = self._get_current_season_id()

            injury_endpoints = [
                # Competition level injuries
                f"competitions/{self.nrl_competition_id}/injuries.json",
                # Season level injuries
                f"seasons/{season_id}/injuries.json",
                # Direct injuries endpoint
                "injuries.json",
                # Player injuries
                f"competitions/{self.nrl_competition_id}/players/injuries.json",
                # Season player injuries
                f"seasons/{season_id}/players/injuries.json"
            ]

            for endpoint in injury_endpoints:
                logger.info(f"Trying injury endpoint: {endpoint}")
                try:
                    injury_data = self._make_request(endpoint)

                    if injury_data:
                        logger.info(f"Got response from {endpoint}: {type(injury_data)}")

                        # Handle different response structures
                        if isinstance(injury_data, dict):
                            if 'injuries' in injury_data:
                                for injury in injury_data['injuries']:
                                    formatted_injury = self._format_injury_data(injury)
                                    if formatted_injury:
                                        injuries.append(formatted_injury)
                            elif 'players' in injury_data:
                                # Check if players have injury status
                                for player in injury_data['players']:
                                    injury = self._extract_injury_from_player(player, "Unknown")
                                    if injury:
                                        injuries.append(injury)
                        elif isinstance(injury_data, list):
                            for injury in injury_data:
                                formatted_injury = self._format_injury_data(injury)
                                if formatted_injury:
                                    injuries.append(formatted_injury)

                        if injuries:
                            logger.info(f"Found {len(injuries)} injuries from {endpoint}")
                            break

                except Exception as e:
                    logger.debug(f"Endpoint {endpoint} failed: {e}")
                    continue

            # If no direct injury endpoint works, try getting from team profiles
            if not injuries:
                logger.info("No direct injury endpoints found, trying team profiles...")
                injuries = self._get_injuries_from_teams()

        except Exception as e:
            logger.error(f"Error getting injury reports: {e}")

        logger.info(f"Total injuries found: {len(injuries)}")
        return injuries

    def _get_current_season_id(self) -> str:
        """Get the current season ID"""
        try:
            seasons_data = self._make_request(f"competitions/{self.nrl_competition_id}/seasons.json")
            if seasons_data and 'seasons' in seasons_data:
                return seasons_data['seasons'][-1].get('id', '')
        except:
            pass
        return ""

    def _get_injuries_from_teams(self) -> List[Dict]:
        """Get injury data from team profiles"""
        injuries = []

        try:
            # Get teams from standings
            season_id = self._get_current_season_id()
            standings_data = self._make_request(f"seasons/{season_id}/standings.json")

            if standings_data and 'standings' in standings_data:
                main_standing = standings_data['standings'][0]
                if 'groups' in main_standing and main_standing['groups']:
                    group = main_standing['groups'][0]
                    if 'standings' in group:
                        team_standings = group['standings']

                        for standing in team_standings[:5]:  # Limit to first 5 teams for testing
                            competitor = standing.get('competitor', {})
                            team_id = competitor.get('id')
                            team_name = competitor.get('name')

                            if team_id:
                                # Get team profile which might include injury data
                                team_data = self._make_request(f"competitors/{team_id}/profile.json")
                                if team_data and 'players' in team_data:
                                    for player in team_data['players']:
                                        # Check if player has injury status
                                        if 'injury_status' in player or 'status' in player:
                                            injury = self._extract_injury_from_player(player, team_name)
                                            if injury:
                                                injuries.append(injury)
        except Exception as e:
            logger.error(f"Error getting injuries from teams: {e}")

        return injuries

    def _format_injury_data(self, injury_data: Dict) -> Optional[Dict]:
        """Format injury data to standard format"""
        try:
            player_info = injury_data.get('player', {})

            return {
                'player_id': player_info.get('id'),
                'player_name': player_info.get('name'),
                'team': injury_data.get('team', {}).get('name'),
                'injury_type': injury_data.get('type', 'Unknown'),
                'injury_status': injury_data.get('status', 'Injured'),
                'expected_return': injury_data.get('expected_return'),
                'severity': injury_data.get('severity', 'Unknown'),
                'date_reported': injury_data.get('date', datetime.now().isoformat()),
                'description': injury_data.get('description', ''),
                'source': 'sportradar'
            }
        except Exception as e:
            logger.error(f"Error formatting injury data: {e}")
            return None

    def _extract_injury_from_player(self, player_data: Dict, team_name: str) -> Optional[Dict]:
        """Extract injury information from player data"""
        try:
            status = player_data.get('status', '').lower()
            injury_status = player_data.get('injury_status', '').lower()

            # Check if player is injured
            if any(keyword in status + injury_status for keyword in ['injured', 'doubtful', 'questionable', 'out']):
                return {
                    'player_id': player_data.get('id'),
                    'player_name': player_data.get('name'),
                    'team': team_name,
                    'injury_type': 'Unknown',
                    'injury_status': status or injury_status or 'Injured',
                    'expected_return': None,
                    'severity': 'Unknown',
                    'date_reported': datetime.now().isoformat(),
                    'description': f"Status: {status or injury_status}",
                    'source': 'sportradar_team_profile'
                }
        except Exception as e:
            logger.error(f"Error extracting injury from player: {e}")

        return None

    def _format_player_data(self, raw_data: Dict, team_name: str = None) -> Optional[Dict]:
        """
        Format raw SportRadar player data to our standard format
        """
        try:
            # Extract basic info
            player_id = raw_data.get('id', '')
            name = raw_data.get('name', '')
            
            if not name:
                return None
            
            # Extract team info
            if not team_name and 'team' in raw_data:
                team_name = raw_data['team'].get('name', 'Unknown Team')
            
            # Extract position
            position = raw_data.get('position', 'Unknown')
            position_code = self.position_mappings.get(position.lower(), position)
            
            # Extract other details
            jersey_number = raw_data.get('jersey_number')
            height = raw_data.get('height')
            weight = raw_data.get('weight')
            date_of_birth = raw_data.get('date_of_birth')
            
            # Calculate age if DOB available
            age = None
            if date_of_birth:
                try:
                    dob = datetime.strptime(date_of_birth, '%Y-%m-%d')
                    age = (datetime.now() - dob).days // 365
                except:
                    pass
            
            # Extract statistics if available
            stats = raw_data.get('statistics', {})
            
            formatted_player = {
                'id': hash(player_id) % 1000000,  # Convert to integer ID
                'sportradar_id': player_id,
                'name': name,
                'team': team_name or 'Unknown Team',
                'position': position_code,
                'jersey_number': jersey_number,
                'height': height,
                'weight': weight,
                'age': age,
                'date_of_birth': date_of_birth,
                'statistics': stats,
                'source': 'sportradar',
                'last_updated': datetime.now().isoformat()
            }
            
            return formatted_player
            
        except Exception as e:
            logger.error(f"Error formatting player data: {e}")
            return None

    def get_team_list(self) -> List[Dict]:
        """
        Get list of all NRL teams
        """
        teams = []
        
        competition_data = self._make_request(f"competitions/{self.nrl_competition_id}/info.json")
        
        if competition_data and 'teams' in competition_data:
            for team_data in competition_data['teams']:
                team = {
                    'id': team_data.get('id'),
                    'name': team_data.get('name'),
                    'abbreviation': team_data.get('abbreviation'),
                    'country': team_data.get('country', {}).get('name'),
                    'venue': team_data.get('venue', {}).get('name') if 'venue' in team_data else None
                }
                teams.append(team)
        
        return teams

    def save_players_to_json(self, players: List[Dict], filename: str = 'sportradar_nrl_players.json'):
        """
        Save players data to JSON file
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(players, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(players)} players to {filename}")
        except Exception as e:
            logger.error(f"Error saving to {filename}: {e}")

    def update_player_database(self) -> Dict:
        """
        Update the complete player database
        """
        logger.info("Starting SportRadar NRL data update...")
        
        # Get all players
        players = self.get_all_players()
        
        # Get team information
        teams = self.get_team_list()
        
        # Save to files
        if players:
            self.save_players_to_json(players)
        
        result = {
            'players_count': len(players),
            'teams_count': len(teams),
            'last_updated': datetime.now().isoformat(),
            'players': players,
            'teams': teams
        }
        
        logger.info(f"Update complete: {len(players)} players, {len(teams)} teams")
        return result

def main():
    """
    Test function with provided API key
    """
    api_key = "aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN"
    
    client = SportRadarNRLClient(api_key)
    
    # Test getting competitions first
    competitions_data = client.get_competitions()
    if competitions_data and 'competitions' in competitions_data:
        competitions = competitions_data['competitions']
        print("Available competitions:")
        for comp in competitions[:5]:  # Show first 5
            print(f"  {comp.get('name')} - ID: {comp.get('id')}")

        # Find NRL Premiership
        nrl_comp = next((c for c in competitions if c.get('name') == 'NRL Premiership'), None)
        if nrl_comp:
            print(f"\n✅ Found NRL Premiership: {nrl_comp.get('id')}")
            client.nrl_competition_id = nrl_comp.get('id')
    
    # Test getting competition info
    print(f"\n🔍 Testing competition info for NRL Premiership...")
    comp_info = client._make_request(f"competitions/{client.nrl_competition_id}/info.json")
    if comp_info:
        print(f"Competition info keys: {list(comp_info.keys())}")
        if 'teams' in comp_info:
            print(f"Found {len(comp_info['teams'])} teams")
            for team in comp_info['teams'][:3]:  # Show first 3 teams
                print(f"  Team: {team.get('name')} - ID: {team.get('id')}")
        else:
            print("No 'teams' key found in competition info")
            print(f"Available keys: {list(comp_info.keys())}")
            print(f"Competition data: {comp_info}")

    # Try different endpoints to find teams/players
    print(f"\n🔍 Trying different endpoints...")

    endpoints_to_try = [
        f"competitions/{client.nrl_competition_id}/teams.json",
        f"competitions/{client.nrl_competition_id}/standings.json",
        f"competitions/{client.nrl_competition_id}/seasons.json",
        f"competitions/{client.nrl_competition_id}/schedule.json",
        "teams.json",  # Try global teams endpoint
    ]

    for endpoint in endpoints_to_try:
        print(f"\nTrying: {endpoint}")
        data = client._make_request(endpoint)
        if data:
            print(f"✅ Success! Keys: {list(data.keys())}")

            # Check for seasons data
            if 'seasons' in data:
                seasons = data['seasons']
                print(f"Found {len(seasons)} seasons")
                if seasons:
                    latest_season = seasons[-1]  # Get latest season
                    season_id = latest_season.get('id')
                    print(f"Latest season: {latest_season.get('name')} (ID: {season_id})")

                    # Try different season endpoints
                    if season_id:
                        season_endpoints = [
                            f"seasons/{season_id}/teams.json",
                            f"seasons/{season_id}/standings.json",
                            f"seasons/{season_id}/info.json",
                            f"seasons/{season_id}/competitors.json"
                        ]

                        for season_endpoint in season_endpoints:
                            print(f"\n🔍 Trying season endpoint: {season_endpoint}")
                            season_data = client._make_request(season_endpoint)
                            if season_data:
                                print(f"✅ Success! Keys: {list(season_data.keys())}")

                                # Extract teams from standings
                                if 'standings' in season_data:
                                    standings = season_data['standings']
                                    if standings and len(standings) > 0:
                                        # Get the main standings (first one is usually total)
                                        main_standing = standings[0]
                                        if 'groups' in main_standing and main_standing['groups']:
                                            group = main_standing['groups'][0]
                                            if 'standings' in group:
                                                team_standings = group['standings']
                                                print(f"Found {len(team_standings)} NRL teams:")

                                                teams = []
                                                for standing in team_standings[:5]:  # Show first 5
                                                    competitor = standing.get('competitor', {})
                                                    team_name = competitor.get('name')
                                                    team_id = competitor.get('id')
                                                    teams.append({'name': team_name, 'id': team_id})
                                                    print(f"  - {team_name} (ID: {team_id})")

                                                # Try to get players from first team
                                                if teams:
                                                    first_team = teams[0]
                                                    team_id = first_team['id']
                                                    print(f"\n🔍 Getting players from {first_team['name']} ({team_id})...")

                                                    # Try different player endpoints
                                                    player_endpoints = [
                                                        f"teams/{team_id}/players.json",
                                                        f"teams/{team_id}/profile.json",
                                                        f"competitors/{team_id}/players.json",
                                                        f"competitors/{team_id}/profile.json"
                                                    ]

                                                    for player_endpoint in player_endpoints:
                                                        print(f"Trying: {player_endpoint}")
                                                        team_data = client._make_request(player_endpoint)
                                                        if team_data:
                                                            print(f"✅ Success! Keys: {list(team_data.keys())}")

                                                            # Look for players in different keys
                                                            for player_key in ['players', 'squad', 'roster']:
                                                                if player_key in team_data:
                                                                    players = team_data[player_key]
                                                                    if isinstance(players, list) and players:
                                                                        print(f"Found {len(players)} players:")
                                                                        for player in players[:5]:  # Show first 5
                                                                            name = player.get('name', 'Unknown')
                                                                            position = player.get('position', 'Unknown')
                                                                            print(f"  - {name} ({position})")
                                                                        return  # Success! Exit here
                                                            break
                                                        else:
                                                            print("❌ Failed")

                                                return  # Exit after trying first team
                                break
                            else:
                                print("❌ Failed")
                break
        else:
            print("❌ Failed")

    # Now test getting all players
    print(f"\n🏈 Getting all NRL players...")
    all_players = client.get_all_players()
    print(f"✅ Successfully retrieved {len(all_players)} total NRL players!")

    if all_players:
        print(f"\n📋 Sample players:")
        for player in all_players[:10]:  # Show first 10
            print(f"  - {player.get('name')} ({player.get('team')}) - {player.get('position')}")

        # Save to file
        client.save_players_to_json(all_players)
        print(f"\n💾 Saved all players to sportradar_nrl_players.json")

if __name__ == "__main__":
    main()
