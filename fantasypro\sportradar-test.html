<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SportRadar API Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            color: #e2e8f0;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #2563eb;
        }
        .btn:disabled {
            background: #64748b;
            cursor: not-allowed;
        }
        .success {
            color: #10b981;
        }
        .error {
            color: #ef4444;
        }
        .warning {
            color: #f59e0b;
        }
        .log-container {
            background: #020617;
            border: 1px solid #334155;
            border-radius: 6px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .result-item {
            background: #334155;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #64748b;
        }
        .result-item.success {
            border-left-color: #10b981;
        }
        .result-item.error {
            border-left-color: #ef4444;
        }
        .api-key {
            font-family: monospace;
            background: #334155;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 SportRadar NRL API Test</h1>
        <p>Testing real SportRadar API connectivity with your trial key</p>

        <!-- API Configuration -->
        <div class="card">
            <h2>🔑 API Configuration</h2>
            <p><strong>API Key:</strong> <span class="api-key">aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN</span></p>
            <p><strong>Base URL:</strong> <span class="api-key">https://api.sportradar.com/rugby-league/trial/v3</span></p>
            <p><strong>Status:</strong> <span class="success">Trial Key Active</span></p>
        </div>

        <!-- Test Controls -->
        <div class="card">
            <h2>🚀 Run Tests</h2>
            <div class="grid">
                <button class="btn" onclick="testSingleEndpoint()">Quick Test</button>
                <button class="btn" onclick="testCORS()">CORS Test</button>
                <button class="btn" onclick="testMultipleEndpoints()">Multiple Endpoints</button>
                <button class="btn" onclick="clearLogs()">Clear Logs</button>
            </div>
        </div>

        <!-- Live Logs -->
        <div class="card">
            <h2>📋 Live Test Logs</h2>
            <div id="logs" class="log-container">
                Ready to test SportRadar API...
            </div>
        </div>

        <!-- Results -->
        <div class="card" id="results-section" style="display: none;">
            <h2>📊 Test Results</h2>
            <div id="results-grid" class="grid">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>

    <script>
        const API_KEY = 'aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN';
        const BASE_URL = 'https://api.sportradar.com/rugby-league/trial/v3';
        
        const endpoints = [
            'competitions.json',
            'seasons.json',
            'teams.json',
            'players.json',
            'schedules.json',
            'fixtures.json',
            'injuries.json'
        ];

        let testResults = [];

        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            
            logsDiv.innerHTML += `<span class="${colorClass}">[${timestamp}] ${message}</span>\n`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = 'Logs cleared...\n';
            document.getElementById('results-section').style.display = 'none';
            testResults = [];
        }

        async function testSingleEndpoint() {
            log('🧪 Testing single endpoint...', 'info');
            
            const url = `${BASE_URL}/competitions.json?api_key=${API_KEY}`;
            log(`📡 URL: ${url}`, 'info');

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    },
                });

                log(`📊 Status: ${response.status} ${response.statusText}`, 'info');
                log(`🔗 Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`, 'info');

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ SUCCESS! Data received`, 'success');
                    log(`📊 Data keys: ${Object.keys(data).join(', ')}`, 'success');
                    log(`📄 Sample data: ${JSON.stringify(data, null, 2).substring(0, 300)}...`, 'info');
                    
                    testResults.push({
                        endpoint: 'competitions.json',
                        success: true,
                        status: response.status,
                        data: data
                    });
                } else {
                    const errorText = await response.text();
                    log(`❌ FAILED: ${errorText}`, 'error');
                    
                    testResults.push({
                        endpoint: 'competitions.json',
                        success: false,
                        status: response.status,
                        error: errorText
                    });
                }
            } catch (error) {
                log(`💥 ERROR: ${error.message}`, 'error');
                
                if (error.message.includes('CORS')) {
                    log(`💡 SOLUTION: CORS blocked - need server-side proxy`, 'warning');
                }
                
                testResults.push({
                    endpoint: 'competitions.json',
                    success: false,
                    error: error.message
                });
            }

            updateResults();
        }

        async function testCORS() {
            log('🌐 Testing CORS policy...', 'info');
            
            const url = `${BASE_URL}/competitions.json?api_key=${API_KEY}`;

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                });
                
                log('✅ CORS is working!', 'success');
            } catch (error) {
                if (error.message.includes('CORS')) {
                    log('❌ CORS blocked - need server-side proxy', 'error');
                } else {
                    log(`💥 Other error: ${error.message}`, 'error');
                }
            }
        }

        async function testMultipleEndpoints() {
            log('🧪 Testing multiple endpoints...', 'info');
            testResults = [];

            for (const endpoint of endpoints) {
                const url = `${BASE_URL}/${endpoint}?api_key=${API_KEY}`;
                log(`📡 Testing: ${endpoint}`, 'info');

                try {
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                        },
                    });

                    if (response.ok) {
                        const data = await response.json();
                        log(`   ✅ ${endpoint} - SUCCESS`, 'success');
                        
                        testResults.push({
                            endpoint: endpoint,
                            success: true,
                            status: response.status,
                            data: data
                        });
                    } else {
                        const errorText = await response.text();
                        log(`   ❌ ${endpoint} - FAILED: ${response.status}`, 'error');
                        
                        testResults.push({
                            endpoint: endpoint,
                            success: false,
                            status: response.status,
                            error: errorText
                        });
                    }
                } catch (error) {
                    log(`   💥 ${endpoint} - ERROR: ${error.message}`, 'error');
                    
                    testResults.push({
                        endpoint: endpoint,
                        success: false,
                        error: error.message
                    });
                }

                // Rate limiting delay
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Summary
            const successful = testResults.filter(r => r.success).length;
            const failed = testResults.filter(r => !r.success).length;
            
            log(`📊 SUMMARY: ${successful} successful, ${failed} failed`, 'info');
            
            if (successful > 0) {
                log(`🎉 Working endpoints: ${testResults.filter(r => r.success).map(r => r.endpoint).join(', ')}`, 'success');
            }

            updateResults();
        }

        function updateResults() {
            const resultsSection = document.getElementById('results-section');
            const resultsGrid = document.getElementById('results-grid');
            
            if (testResults.length === 0) {
                resultsSection.style.display = 'none';
                return;
            }

            resultsSection.style.display = 'block';
            resultsGrid.innerHTML = '';

            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `result-item ${result.success ? 'success' : 'error'}`;
                
                div.innerHTML = `
                    <h4>${result.endpoint}</h4>
                    <p><strong>Status:</strong> ${result.success ? 'SUCCESS' : 'FAILED'}</p>
                    ${result.status ? `<p><strong>HTTP:</strong> ${result.status}</p>` : ''}
                    ${result.success && result.data ? `<p><strong>Data Keys:</strong> ${Object.keys(result.data).join(', ')}</p>` : ''}
                    ${result.error ? `<p><strong>Error:</strong> ${result.error}</p>` : ''}
                `;
                
                resultsGrid.appendChild(div);
            });
        }

        // Auto-run a quick test on page load
        window.addEventListener('load', () => {
            log('🚀 Page loaded - ready to test SportRadar API', 'info');
            log('💡 Click "Quick Test" to test a single endpoint', 'info');
        });
    </script>
</body>
</html>
