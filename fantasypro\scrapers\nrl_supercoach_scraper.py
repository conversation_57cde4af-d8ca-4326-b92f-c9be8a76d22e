#!/usr/bin/env python3
"""
NRL SuperCoach Stats Scraper

Comprehensive scraper for nrlsupercoachstats.com to extract:
- Player statistics and performance data
- Break-even scores and price information
- Team data and fixture information
- Historical performance trends
"""

import asyncio
import aiohttp
import re
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from bs4 import BeautifulSoup
import pandas as pd
from urllib.parse import urljoin, urlparse
import logging
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.logging_config import setup_logging

logger = setup_logging(__name__)

@dataclass
class PlayerData:
    """Player data structure"""
    name: str
    team: str
    position: str
    price: Optional[float] = None
    breakeven: Optional[int] = None
    points: Optional[int] = None
    average: Optional[float] = None
    games_played: Optional[int] = None
    form: Optional[float] = None
    ownership: Optional[float] = None
    minutes_avg: Optional[float] = None
    consistency: Optional[float] = None
    recent_scores: Optional[List[int]] = None
    season_high: Optional[int] = None
    season_low: Optional[int] = None
    last_updated: Optional[datetime] = None

@dataclass
class TeamData:
    """Team data structure"""
    name: str
    abbreviation: str
    players: List[PlayerData]
    avg_breakeven: Optional[float] = None
    total_value: Optional[float] = None
    points_for: Optional[int] = None
    points_against: Optional[int] = None
    last_updated: Optional[datetime] = None

@dataclass
class FixtureData:
    """Fixture data structure"""
    round_number: int
    home_team: str
    away_team: str
    date: Optional[datetime] = None
    venue: Optional[str] = None
    difficulty_home: Optional[float] = None
    difficulty_away: Optional[float] = None

class NRLSuperCoachScraper:
    """Main scraper class for NRL SuperCoach Stats"""
    
    BASE_URL = "https://www.nrlsupercoachstats.com"
    
    # Team mappings
    TEAM_MAPPINGS = {
        'BRIS': 'Brisbane Broncos',
        'BULL': 'Canterbury Bulldogs', 
        'CANB': 'Canberra Raiders',
        'DRAG': 'St George Dragons',
        'MANL': 'Manly Sea Eagles',
        'MELB': 'Melbourne Storm',
        'NEWC': 'Newcastle Knights',
        'NQLD': 'North Queensland Cowboys',
        'DOL': 'Dolphins',
        'PARR': 'Parramatta Eels',
        'PENR': 'Penrith Panthers',
        'SHRK': 'Cronulla Sharks',
        'SSYD': 'South Sydney Rabbitohs',
        'SYDR': 'Sydney Roosters',
        'TITN': 'Gold Coast Titans',
        'WARR': 'New Zealand Warriors',
        'WTIG': 'Wests Tigers'
    }
    
    def __init__(self, session: Optional[aiohttp.ClientSession] = None):
        self.session = session
        self.should_close_session = session is None
        self.rate_limit_delay = 1.0  # Respectful rate limiting
        
    async def __aenter__(self):
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    'User-Agent': 'FantasyPro-Bot/1.0 (Educational/Research Purpose)',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
            )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.should_close_session and self.session:
            await self.session.close()
    
    async def _fetch_page(self, url: str, params: Optional[Dict] = None) -> Optional[str]:
        """Fetch a single page with error handling and rate limiting"""
        try:
            await asyncio.sleep(self.rate_limit_delay)  # Rate limiting
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    content = await response.text()
                    logger.debug(f"Successfully fetched: {url}")
                    return content
                else:
                    logger.warning(f"HTTP {response.status} for {url}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error fetching {url}: {e}")
            return None
    
    def _parse_player_name(self, name_text: str) -> str:
        """Clean and standardize player names"""
        if not name_text:
            return ""
        
        # Remove extra whitespace and normalize
        name = re.sub(r'\s+', ' ', name_text.strip())
        
        # Handle "Surname, Firstname" format
        if ',' in name:
            parts = name.split(',', 1)
            if len(parts) == 2:
                surname = parts[0].strip()
                firstname = parts[1].strip()
                name = f"{firstname} {surname}"
        
        return name
    
    def _parse_numeric_value(self, value_text: str) -> Optional[float]:
        """Parse numeric values from text, handling various formats"""
        if not value_text or value_text.strip() in ['', '-', 'N/A', 'n/a']:
            return None
            
        # Remove commas, dollar signs, and other formatting
        cleaned = re.sub(r'[,$%]', '', value_text.strip())
        
        try:
            return float(cleaned)
        except ValueError:
            return None
    
    async def scrape_team_breakevens(self, year: int = 2025) -> Dict[str, List[Dict]]:
        """Scrape team break-evens data"""
        url = f"{self.BASE_URL}/TeamBEs.php"
        params = {'year': year} if year != 2025 else None

        content = await self._fetch_page(url, params)
        if not content:
            return {}

        soup = BeautifulSoup(content, 'html.parser')
        teams_data = {}

        # Find tables with team data
        tables = soup.find_all('table')

        for table in tables:
            rows = table.find_all('tr')
            if len(rows) < 2:  # Skip tables without data
                continue

            # Look for team images in the table to identify team sections
            team_images = table.find_all('img', src=re.compile(r'TeamPictures/\w+\.png'))

            for team_img in team_images:
                # Extract team abbreviation from image path
                team_match = re.search(r'TeamPictures/(\w+)\.png', team_img['src'])
                if not team_match:
                    continue

                team_abbr = team_match.group(1)
                team_name = self.TEAM_MAPPINGS.get(team_abbr, team_abbr)

                if team_name not in teams_data:
                    teams_data[team_name] = []

                # Find the row containing this team image
                team_row = team_img.find_parent('tr')
                if not team_row:
                    continue

                # Look for player data in subsequent rows after the team header
                next_rows = team_row.find_next_siblings('tr')

                for next_row in next_rows:
                    cells = next_row.find_all(['td', 'th'])

                    for cell in cells:
                        cell_text = cell.get_text(strip=True)

                        # Skip empty cells or cells that are just numbers
                        if not cell_text or cell_text.isdigit() or (cell_text.startswith('-') and cell_text[1:].isdigit()):
                            continue

                        # Look for player patterns: "Surname, Firstname[breakeven]"
                        # Pattern 1: "Karapani, Josiah-30" (negative breakeven)
                        # Pattern 2: "Piakura, Brendan7" (positive breakeven)

                        if ',' in cell_text:
                            # Try to parse player name and breakeven
                            parts = cell_text.split(',', 1)
                            if len(parts) == 2:
                                surname = parts[0].strip()
                                rest = parts[1].strip()

                                # Extract firstname and breakeven
                                # Handle negative breakevens: "Josiah-30"
                                if '-' in rest and not rest.startswith('-'):
                                    # Split on the last dash to handle names with dashes
                                    dash_pos = rest.rfind('-')
                                    if dash_pos > 0:
                                        firstname = rest[:dash_pos].strip()
                                        breakeven_str = '-' + rest[dash_pos+1:]

                                        if breakeven_str[1:].isdigit():
                                            player_name = f"{firstname} {surname}"
                                            breakeven = self._parse_numeric_value(breakeven_str)

                                            teams_data[team_name].append({
                                                'name': player_name,
                                                'team': team_name,
                                                'team_abbr': team_abbr,
                                                'breakeven': breakeven
                                            })
                                            continue

                                # Handle positive breakevens: "Brendan7"
                                firstname_match = re.match(r'^([A-Za-z\s]+?)(\d+)$', rest)
                                if firstname_match:
                                    firstname = firstname_match.group(1).strip()
                                    breakeven_str = firstname_match.group(2)

                                    player_name = f"{firstname} {surname}"
                                    breakeven = self._parse_numeric_value(breakeven_str)

                                    teams_data[team_name].append({
                                        'name': player_name,
                                        'team': team_name,
                                        'team_abbr': team_abbr,
                                        'breakeven': breakeven
                                    })
                                    continue

                                # Handle regular name format without breakeven in same cell
                                if re.match(r'^[A-Za-z\s]+$', rest):
                                    player_name = f"{rest.strip()} {surname}"

                                    teams_data[team_name].append({
                                        'name': player_name,
                                        'team': team_name,
                                        'team_abbr': team_abbr,
                                        'breakeven': None
                                    })

                    # Stop looking at rows once we hit another team or empty rows
                    if any(img.get('src', '').find('TeamPictures') != -1 for img in next_row.find_all('img')):
                        break

        # Log results
        for team_name, players in teams_data.items():
            if players:
                logger.info(f"Scraped {len(players)} players for {team_name}")

        return teams_data
    
    async def scrape_team_prices(self, year: int = 2025) -> Dict[str, List[Dict]]:
        """Scrape team prices data"""
        url = f"{self.BASE_URL}/TeamPrices.php"
        params = {'year': year} if year != 2025 else None
        
        content = await self._fetch_page(url, params)
        if not content:
            return {}
        
        soup = BeautifulSoup(content, 'html.parser')
        teams_data = {}
        
        # Similar parsing logic to breakevens but for prices
        team_sections = soup.find_all('img', src=re.compile(r'TeamPictures/\w+\.png'))
        
        for team_img in team_sections:
            team_match = re.search(r'TeamPictures/(\w+)\.png', team_img['src'])
            if not team_match:
                continue
                
            team_abbr = team_match.group(1)
            team_name = self.TEAM_MAPPINGS.get(team_abbr, team_abbr)
            
            team_container = team_img.find_parent()
            if not team_container:
                continue
            
            players = []
            player_links = team_container.find_all('a', href=re.compile(r'index\.php\?player='))
            
            for link in player_links:
                player_name = self._parse_player_name(link.text)
                if not player_name:
                    continue
                
                # Find the price value
                price_text = link.find_next_sibling(text=True)
                if price_text:
                    price = self._parse_numeric_value(price_text.strip())
                else:
                    price = None
                
                players.append({
                    'name': player_name,
                    'team': team_name,
                    'team_abbr': team_abbr,
                    'price': price
                })
            
            if players:
                teams_data[team_name] = players
        
        return teams_data

    async def scrape_player_stats(self, year: int = 2025) -> List[Dict]:
        """Scrape comprehensive player statistics"""
        url = f"{self.BASE_URL}/stats.php"
        params = {'year': year} if year != 2025 else None

        content = await self._fetch_page(url, params)
        if not content:
            return []

        soup = BeautifulSoup(content, 'html.parser')
        players = []

        # Look for data tables or structured player data
        tables = soup.find_all('table')

        for table in tables:
            rows = table.find_all('tr')
            headers = []

            # Extract headers
            header_row = rows[0] if rows else None
            if header_row:
                headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]

            # Process data rows
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 3:  # Skip rows with insufficient data
                    continue

                player_data = {}
                for i, cell in enumerate(cells):
                    if i < len(headers):
                        header = headers[i].lower().replace(' ', '_')
                        value = cell.get_text(strip=True)

                        # Parse specific fields
                        if header in ['price', 'average', 'points', 'games']:
                            player_data[header] = self._parse_numeric_value(value)
                        else:
                            player_data[header] = value

                if player_data.get('name'):  # Only add if we have a player name
                    players.append(player_data)

        logger.info(f"Scraped {len(players)} player statistics")
        return players

    async def scrape_fixture_data(self, year: int = 2025) -> List[FixtureData]:
        """Scrape fixture/draw data"""
        url = f"{self.BASE_URL}/drawV2.php"
        params = {'year': year} if year != 2025 else None

        content = await self._fetch_page(url, params)
        if not content:
            return []

        soup = BeautifulSoup(content, 'html.parser')
        fixtures = []

        # Parse fixture table
        tables = soup.find_all('table')

        for table in tables:
            rows = table.find_all('tr')

            for row in rows[1:]:  # Skip header
                cells = row.find_all(['td', 'th'])
                if len(cells) < 3:
                    continue

                # Extract fixture data based on table structure
                round_num = self._parse_numeric_value(cells[0].get_text(strip=True))
                if round_num:
                    fixture = FixtureData(
                        round_number=int(round_num),
                        home_team=cells[1].get_text(strip=True) if len(cells) > 1 else "",
                        away_team=cells[2].get_text(strip=True) if len(cells) > 2 else ""
                    )
                    fixtures.append(fixture)

        logger.info(f"Scraped {len(fixtures)} fixtures")
        return fixtures

    async def scrape_all_data(self, year: int = 2025) -> Dict[str, Any]:
        """Scrape all available data for a given year"""
        logger.info(f"Starting comprehensive scrape for year {year}")

        results = {}

        try:
            # Scrape all data types with delays
            results['team_breakevens'] = await self.scrape_team_breakevens(year)
            await asyncio.sleep(2)

            results['team_prices'] = await self.scrape_team_prices(year)
            await asyncio.sleep(2)

            results['player_stats'] = await self.scrape_player_stats(year)
            await asyncio.sleep(2)

            results['fixtures'] = await self.scrape_fixture_data(year)

            # Combine and normalize data
            results['combined_players'] = self._combine_player_data(
                results.get('team_breakevens', {}),
                results.get('team_prices', {}),
                results.get('player_stats', [])
            )

            results['scrape_timestamp'] = datetime.now()
            results['year'] = year

            logger.info(f"Completed scrape for year {year}")
            return results

        except Exception as e:
            logger.error(f"Error in comprehensive scrape: {e}")
            return {}

    def _combine_player_data(self, breakevens: Dict, prices: Dict, stats: List) -> List[PlayerData]:
        """Combine data from different sources into unified player objects"""
        players_dict = {}

        # Process breakevens
        for team_name, team_players in breakevens.items():
            for player in team_players:
                name = player['name']
                if name not in players_dict:
                    players_dict[name] = PlayerData(
                        name=name,
                        team=team_name,
                        position="Unknown"
                    )
                players_dict[name].breakeven = player.get('breakeven')

        # Process prices
        for team_name, team_players in prices.items():
            for player in team_players:
                name = player['name']
                if name not in players_dict:
                    players_dict[name] = PlayerData(
                        name=name,
                        team=team_name,
                        position="Unknown"
                    )
                players_dict[name].price = player.get('price')

        # Process stats
        for stat in stats:
            name = stat.get('name')
            if name and name in players_dict:
                player = players_dict[name]
                player.points = stat.get('points')
                player.average = stat.get('average')
                player.games_played = stat.get('games')
                player.position = stat.get('position', player.position)

        # Set last updated timestamp
        for player in players_dict.values():
            player.last_updated = datetime.now()

        return list(players_dict.values())


# Utility functions for running the scraper
async def run_scraper_test():
    """Test function to run the scraper"""
    async with NRLSuperCoachScraper() as scraper:
        # Test individual methods
        print("Testing team breakevens...")
        breakevens = await scraper.scrape_team_breakevens()
        print(f"Found {len(breakevens)} teams with breakeven data")

        print("Testing team prices...")
        prices = await scraper.scrape_team_prices()
        print(f"Found {len(prices)} teams with price data")

        # Test comprehensive scrape
        print("Running comprehensive scrape...")
        all_data = await scraper.scrape_all_data()
        print(f"Scraped data for {len(all_data.get('combined_players', []))} players")

        return all_data

if __name__ == "__main__":
    # Install required packages
    import subprocess
    import sys

    required_packages = ['aiohttp', 'beautifulsoup4', 'pandas']
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

    # Run the test
    asyncio.run(run_scraper_test())
