#!/usr/bin/env python3
"""
SuperCoach Database Management

Database operations and utilities for NRL SuperCoach data storage and retrieval.
"""

import os
import asyncio
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from contextlib import asynccontextmanager
import logging
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.supercoach_models import (
    Base, NRLTeam, SuperCoachPlayer, PlayerPriceHistory, 
    PlayerGameStats, NRLFixture, PositionVsTeamStats
)
from scrapers.nrl_supercoach_scraper import PlayerData, TeamData, FixtureData
from utils.logging_config import setup_logging

logger = setup_logging(__name__)

class SuperCoachDatabase:
    """Database manager for SuperCoach data"""
    
    def __init__(self, database_url: Optional[str] = None):
        """Initialize database connection"""
        if database_url is None:
            # Default to SQLite for development
            db_path = project_root / "data" / "supercoach.db"
            db_path.parent.mkdir(exist_ok=True)
            database_url = f"sqlite:///{db_path}"
        
        self.database_url = database_url
        self.engine = create_engine(database_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Create tables
        self.create_tables()
        
        # Initialize teams if not exists
        self.initialize_teams()
    
    def create_tables(self):
        """Create all database tables"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            raise
    
    def get_session(self) -> Session:
        """Get database session"""
        return self.SessionLocal()
    
    @asynccontextmanager
    async def get_async_session(self):
        """Get async database session (for future async operations)"""
        # For now, we'll use sync operations
        session = self.get_session()
        try:
            yield session
        finally:
            session.close()
    
    def initialize_teams(self):
        """Initialize NRL teams in database"""
        teams_data = [
            {"name": "Brisbane Broncos", "abbreviation": "BRIS", "city": "Brisbane"},
            {"name": "Canterbury Bulldogs", "abbreviation": "BULL", "city": "Sydney"},
            {"name": "Canberra Raiders", "abbreviation": "CANB", "city": "Canberra"},
            {"name": "St George Dragons", "abbreviation": "DRAG", "city": "Sydney"},
            {"name": "Manly Sea Eagles", "abbreviation": "MANL", "city": "Sydney"},
            {"name": "Melbourne Storm", "abbreviation": "MELB", "city": "Melbourne"},
            {"name": "Newcastle Knights", "abbreviation": "NEWC", "city": "Newcastle"},
            {"name": "North Queensland Cowboys", "abbreviation": "NQLD", "city": "Townsville"},
            {"name": "Dolphins", "abbreviation": "DOL", "city": "Brisbane"},
            {"name": "Parramatta Eels", "abbreviation": "PARR", "city": "Sydney"},
            {"name": "Penrith Panthers", "abbreviation": "PENR", "city": "Sydney"},
            {"name": "Cronulla Sharks", "abbreviation": "SHRK", "city": "Sydney"},
            {"name": "South Sydney Rabbitohs", "abbreviation": "SSYD", "city": "Sydney"},
            {"name": "Sydney Roosters", "abbreviation": "SYDR", "city": "Sydney"},
            {"name": "Gold Coast Titans", "abbreviation": "TITN", "city": "Gold Coast"},
            {"name": "New Zealand Warriors", "abbreviation": "WARR", "city": "Auckland"},
            {"name": "Wests Tigers", "abbreviation": "WTIG", "city": "Sydney"},
        ]
        
        with self.get_session() as session:
            try:
                for team_data in teams_data:
                    # Check if team already exists
                    existing_team = session.query(NRLTeam).filter_by(
                        abbreviation=team_data["abbreviation"]
                    ).first()
                    
                    if not existing_team:
                        team = NRLTeam(**team_data)
                        session.add(team)
                
                session.commit()
                logger.info("Teams initialized successfully")
                
            except Exception as e:
                session.rollback()
                logger.error(f"Error initializing teams: {e}")
                raise
    
    def store_scraped_data(self, scraped_data: Dict[str, Any]) -> bool:
        """Store scraped data in database"""
        try:
            with self.get_session() as session:
                # Store combined player data
                players_data = scraped_data.get('combined_players', [])
                self._store_players(session, players_data)
                
                # Store fixtures
                fixtures_data = scraped_data.get('fixtures', [])
                self._store_fixtures(session, fixtures_data)
                
                session.commit()
                logger.info(f"Successfully stored data for {len(players_data)} players")
                return True
                
        except Exception as e:
            logger.error(f"Error storing scraped data: {e}")
            return False
    
    def _store_players(self, session: Session, players_data: List[PlayerData]):
        """Store player data in database"""
        for player_data in players_data:
            try:
                # Get team
                team = session.query(NRLTeam).filter_by(name=player_data.team).first()
                if not team:
                    logger.warning(f"Team not found: {player_data.team}")
                    continue
                
                # Check if player exists
                existing_player = session.query(SuperCoachPlayer).filter_by(
                    name=player_data.name,
                    team_id=team.id
                ).first()
                
                if existing_player:
                    # Update existing player
                    self._update_player(existing_player, player_data)
                else:
                    # Create new player
                    new_player = SuperCoachPlayer(
                        name=player_data.name,
                        team_id=team.id,
                        position=player_data.position,
                        current_price=player_data.price,
                        current_breakeven=player_data.breakeven,
                        season_points=player_data.points,
                        season_average=player_data.average,
                        games_played=player_data.games_played,
                        form_rating=player_data.form,
                        ownership_percentage=player_data.ownership,
                        minutes_per_game=player_data.minutes_avg,
                        consistency_rating=player_data.consistency,
                        season_high=player_data.season_high,
                        season_low=player_data.season_low,
                        recent_scores=player_data.recent_scores,
                        last_updated=datetime.utcnow()
                    )
                    session.add(new_player)
                
            except Exception as e:
                logger.error(f"Error storing player {player_data.name}: {e}")
                continue
    
    def _update_player(self, player: SuperCoachPlayer, player_data: PlayerData):
        """Update existing player with new data"""
        # Store price history if price changed
        if player_data.price and player.current_price != player_data.price:
            price_history = PlayerPriceHistory(
                player_id=player.id,
                round_number=self._get_current_round(),
                season_year=datetime.now().year,
                price_before=player.current_price or 0,
                price_after=player_data.price,
                price_change=player_data.price - (player.current_price or 0),
                breakeven_before=player.current_breakeven,
                breakeven_after=player_data.breakeven
            )
            player.price_history.append(price_history)
        
        # Update player data
        if player_data.price is not None:
            player.current_price = player_data.price
        if player_data.breakeven is not None:
            player.current_breakeven = player_data.breakeven
        if player_data.points is not None:
            player.season_points = player_data.points
        if player_data.average is not None:
            player.season_average = player_data.average
        if player_data.games_played is not None:
            player.games_played = player_data.games_played
        if player_data.form is not None:
            player.form_rating = player_data.form
        if player_data.ownership is not None:
            player.ownership_percentage = player_data.ownership
        if player_data.minutes_avg is not None:
            player.minutes_per_game = player_data.minutes_avg
        if player_data.consistency is not None:
            player.consistency_rating = player_data.consistency
        if player_data.season_high is not None:
            player.season_high = player_data.season_high
        if player_data.season_low is not None:
            player.season_low = player_data.season_low
        if player_data.recent_scores is not None:
            player.recent_scores = player_data.recent_scores
        
        player.last_updated = datetime.utcnow()
    
    def _store_fixtures(self, session: Session, fixtures_data: List[FixtureData]):
        """Store fixture data in database"""
        for fixture_data in fixtures_data:
            try:
                # Get teams
                home_team = session.query(NRLTeam).filter_by(name=fixture_data.home_team).first()
                away_team = session.query(NRLTeam).filter_by(name=fixture_data.away_team).first()
                
                if not home_team or not away_team:
                    logger.warning(f"Teams not found for fixture: {fixture_data.home_team} vs {fixture_data.away_team}")
                    continue
                
                # Check if fixture exists
                existing_fixture = session.query(NRLFixture).filter_by(
                    round_number=fixture_data.round_number,
                    season_year=datetime.now().year,
                    home_team_id=home_team.id,
                    away_team_id=away_team.id
                ).first()
                
                if not existing_fixture:
                    new_fixture = NRLFixture(
                        season_year=datetime.now().year,
                        round_number=fixture_data.round_number,
                        home_team_id=home_team.id,
                        away_team_id=away_team.id,
                        kickoff_time=fixture_data.date,
                        venue=fixture_data.venue,
                        home_difficulty=fixture_data.difficulty_home,
                        away_difficulty=fixture_data.difficulty_away
                    )
                    session.add(new_fixture)
                
            except Exception as e:
                logger.error(f"Error storing fixture: {e}")
                continue
    
    def _get_current_round(self) -> int:
        """Get current NRL round number"""
        # This is a simplified implementation
        # In reality, you'd calculate based on current date and season schedule
        return 1
    
    def get_all_players(self, team_id: Optional[int] = None, position: Optional[str] = None) -> List[SuperCoachPlayer]:
        """Get all players with optional filtering"""
        with self.get_session() as session:
            query = session.query(SuperCoachPlayer).filter(SuperCoachPlayer.is_active == True)
            
            if team_id:
                query = query.filter(SuperCoachPlayer.team_id == team_id)
            
            if position:
                query = query.filter(SuperCoachPlayer.position == position)
            
            return query.all()
    
    def get_player_by_name(self, name: str) -> Optional[SuperCoachPlayer]:
        """Get player by name"""
        with self.get_session() as session:
            return session.query(SuperCoachPlayer).filter(
                SuperCoachPlayer.name == name,
                SuperCoachPlayer.is_active == True
            ).first()
    
    def get_top_players_by_metric(self, metric: str, limit: int = 10, position: Optional[str] = None) -> List[SuperCoachPlayer]:
        """Get top players by specific metric"""
        with self.get_session() as session:
            query = session.query(SuperCoachPlayer).filter(SuperCoachPlayer.is_active == True)
            
            if position:
                query = query.filter(SuperCoachPlayer.position == position)
            
            # Order by metric
            if metric == 'points':
                query = query.order_by(SuperCoachPlayer.season_points.desc())
            elif metric == 'average':
                query = query.order_by(SuperCoachPlayer.season_average.desc())
            elif metric == 'form':
                query = query.order_by(SuperCoachPlayer.form_rating.desc())
            elif metric == 'value':
                query = query.order_by(SuperCoachPlayer.value_score.desc())
            elif metric == 'ownership':
                query = query.order_by(SuperCoachPlayer.ownership_percentage.desc())
            
            return query.limit(limit).all()
    
    def get_price_risers_fallers(self, limit: int = 10) -> Tuple[List[SuperCoachPlayer], List[SuperCoachPlayer]]:
        """Get biggest price risers and fallers"""
        with self.get_session() as session:
            # Get recent price changes
            recent_changes = session.query(PlayerPriceHistory).filter(
                PlayerPriceHistory.recorded_at >= datetime.utcnow() - timedelta(days=7)
            ).all()
            
            # Sort by price change
            risers = sorted([change for change in recent_changes if change.price_change > 0], 
                          key=lambda x: x.price_change, reverse=True)[:limit]
            fallers = sorted([change for change in recent_changes if change.price_change < 0], 
                           key=lambda x: x.price_change)[:limit]
            
            return ([change.player for change in risers], 
                   [change.player for change in fallers])
    
    def get_team_stats(self, team_id: int) -> Dict[str, Any]:
        """Get comprehensive team statistics"""
        with self.get_session() as session:
            team = session.query(NRLTeam).filter_by(id=team_id).first()
            if not team:
                return {}
            
            players = session.query(SuperCoachPlayer).filter_by(
                team_id=team_id, 
                is_active=True
            ).all()
            
            total_value = sum(p.current_price or 0 for p in players)
            avg_breakeven = sum(p.current_breakeven or 0 for p in players if p.current_breakeven) / len([p for p in players if p.current_breakeven])
            total_points = sum(p.season_points or 0 for p in players)
            
            return {
                'team': team,
                'player_count': len(players),
                'total_value': total_value,
                'average_breakeven': avg_breakeven,
                'total_points': total_points,
                'players': players
            }


# Database instance
supercoach_db = SuperCoachDatabase()
