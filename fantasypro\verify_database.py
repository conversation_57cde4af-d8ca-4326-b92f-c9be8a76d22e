#!/usr/bin/env python3
"""
Verify Database Contents

Quick script to verify and display the populated database contents.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.supercoach_db import supercoach_db
from database.supercoach_models import SuperCoachPlayer, NRLTeam

def verify_database_contents():
    """Verify and display database contents"""
    print("🏈 NRL SuperCoach Database Verification")
    print("=" * 50)
    
    try:
        with supercoach_db.get_session() as session:
            # Get all teams
            teams = session.query(NRLTeam).all()
            print(f"🏈 Total teams: {len(teams)}")
            
            # Get all players
            players = session.query(SuperCoachPlayer).all()
            print(f"👥 Total players: {len(players)}")
            
            print("\n📋 Team-by-Team Breakdown:")
            print("=" * 50)
            
            total_players_with_be = 0
            total_players_with_prices = 0
            
            for team in sorted(teams, key=lambda x: x.name):
                # Get players for this team
                team_players = session.query(SuperCoachPlayer).filter_by(team_id=team.id).all()
                
                if team_players:
                    # Calculate stats
                    players_with_be = len([p for p in team_players if p.current_breakeven is not None])
                    players_with_prices = len([p for p in team_players if p.current_price is not None])
                    avg_breakeven = sum(p.current_breakeven or 0 for p in team_players if p.current_breakeven) / players_with_be if players_with_be > 0 else 0
                    
                    total_players_with_be += players_with_be
                    total_players_with_prices += players_with_prices
                    
                    print(f"\n🏈 {team.name} ({team.abbreviation})")
                    print(f"   👥 Players: {len(team_players)}")
                    print(f"   📊 With breakevens: {players_with_be}")
                    print(f"   💰 With prices: {players_with_prices}")
                    print(f"   📈 Avg breakeven: {avg_breakeven:.1f}")
                    
                    # Show players
                    print("   Players:")
                    for player in sorted(team_players, key=lambda x: x.name):
                        be_str = f"BE {player.current_breakeven}" if player.current_breakeven is not None else "No BE"
                        price_str = f"${player.current_price:,.0f}" if player.current_price else "No price"
                        pos_str = player.position if player.position and player.position != "Unknown" else "No pos"
                        
                        print(f"      - {player.name}: {be_str}, {price_str}, {pos_str}")
            
            print(f"\n📊 Summary Statistics:")
            print("=" * 30)
            print(f"✅ Total teams: {len(teams)}")
            print(f"✅ Total players: {len(players)}")
            print(f"✅ Players with breakevens: {total_players_with_be}")
            print(f"✅ Players with prices: {total_players_with_prices}")
            print(f"✅ Data coverage: {total_players_with_be/len(players)*100:.1f}% breakevens")
            
            # Show some interesting stats
            if players:
                breakevens = [p.current_breakeven for p in players if p.current_breakeven is not None]
                if breakevens:
                    print(f"\n📈 Breakeven Analysis:")
                    print(f"   Highest breakeven: {max(breakevens)}")
                    print(f"   Lowest breakeven: {min(breakevens)}")
                    print(f"   Average breakeven: {sum(breakevens)/len(breakevens):.1f}")
                    
                    # Players with negative breakevens (likely to rise)
                    negative_be = [be for be in breakevens if be < 0]
                    print(f"   Players likely to rise (negative BE): {len(negative_be)}")
                    
                    # Players with high positive breakevens (likely to fall)
                    high_positive_be = [be for be in breakevens if be > 10]
                    print(f"   Players at risk of falling (BE > 10): {len(high_positive_be)}")
            
            print(f"\n🚀 Database is ready for production use!")
            return True
            
    except Exception as e:
        print(f"❌ Error verifying database: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_readiness():
    """Test if the database is ready for API operations"""
    print("\n🌐 Testing API Readiness...")
    print("=" * 30)
    
    try:
        # Test basic queries that the API will use
        all_players = supercoach_db.get_all_players()
        print(f"✅ get_all_players(): {len(all_players)} players")
        
        # Test team filtering
        if all_players:
            first_team_id = all_players[0].team_id
            team_players = supercoach_db.get_all_players(team_id=first_team_id)
            print(f"✅ get_all_players(team_id): {len(team_players)} players")
        
        # Test top players query
        top_players = supercoach_db.get_top_players_by_metric('points', limit=5)
        print(f"✅ get_top_players_by_metric(): {len(top_players)} players")
        
        # Test player by name
        if all_players:
            test_player = supercoach_db.get_player_by_name(all_players[0].name)
            if test_player:
                print(f"✅ get_player_by_name(): Found {test_player.name}")
        
        # Test team stats
        with supercoach_db.get_session() as session:
            from database.supercoach_models import NRLTeam
            teams = session.query(NRLTeam).all()
            if teams:
                team_stats = supercoach_db.get_team_stats(teams[0].id)
                print(f"✅ get_team_stats(): {team_stats.get('player_count', 0)} players in team")
        
        print("🎉 All API operations working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ API readiness test failed: {e}")
        return False

if __name__ == "__main__":
    success = verify_database_contents()
    
    if success:
        test_api_readiness()
        
        print("\n🚀 Ready to start servers!")
        print("=" * 30)
        print("1. Start API: python simple_api.py")
        print("2. Start frontend: cd web && npm run dev")
        print("3. Access dashboard: http://localhost:3000/dashboard")
    else:
        print("\n❌ Database verification failed!")
