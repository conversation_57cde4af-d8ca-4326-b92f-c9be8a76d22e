/**
 * Web Scraper Service for NRL SuperCoach Data
 * Scrapes nrlsupercoachstats.com and nrlsupercoachlive.com
 */

// Types for scraped data
export interface ScrapedPlayer {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  points: number;
  average: number;
  form: number;
  ownership?: number;
  breakeven?: number;
  price_change?: number;
  games_played?: number;
  last_5_scores?: number[];
  injury_status?: string;
  source: 'nrlsupercoachstats' | 'nrlsupercoachlive';
  scraped_at: string;
}

export interface ScrapedTeam {
  id: string;
  name: string;
  abbreviation: string;
  players: ScrapedPlayer[];
}

export interface ScrapeResult {
  success: boolean;
  data?: any;
  error?: string;
  timestamp: string;
  source: string;
}

/**
 * Base Web Scraper Class
 */
export class WebScraperService {
  private static readonly USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
  private static readonly REQUEST_DELAY = 1000; // 1 second between requests
  private static lastRequestTime = 0;

  /**
   * Rate-limited fetch with proper headers
   */
  private static async rateLimitedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    // Rate limiting
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.REQUEST_DELAY) {
      await new Promise(resolve => setTimeout(resolve, this.REQUEST_DELAY - timeSinceLastRequest));
    }
    
    this.lastRequestTime = Date.now();

    // Default headers to mimic a real browser
    const defaultHeaders = {
      'User-Agent': this.USER_AGENT,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
    };

    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  }

  /**
   * Parse HTML content using DOMParser
   */
  private static parseHTML(html: string): Document {
    const parser = new DOMParser();
    return parser.parseFromString(html, 'text/html');
  }

  /**
   * Extract text content safely
   */
  private static extractText(element: Element | null): string {
    return element?.textContent?.trim() || '';
  }

  /**
   * Extract number from text
   */
  private static extractNumber(text: string): number {
    const match = text.replace(/[,$%]/g, '').match(/[\d.]+/);
    return match ? parseFloat(match[0]) : 0;
  }

  /**
   * Generate unique player ID from name and team
   */
  private static generatePlayerId(name: string, team: string): string {
    return `${name.toLowerCase().replace(/\s+/g, '_')}_${team.toLowerCase().replace(/\s+/g, '_')}`;
  }

  /**
   * Scrape NRL SuperCoach Stats website
   */
  static async scrapeNRLSuperCoachStats(): Promise<ScrapeResult> {
    const timestamp = new Date().toISOString();
    
    try {
      console.log('🕷️ Scraping nrlsupercoachstats.com...');
      
      // Main players page
      const url = 'https://www.nrlsupercoachstats.com/players';
      const response = await this.rateLimitedFetch(url);
      const html = await response.text();
      const doc = this.parseHTML(html);

      console.log('📄 HTML content received, parsing players...');

      const players: ScrapedPlayer[] = [];
      
      // Look for player table rows (adjust selectors based on actual site structure)
      const playerRows = doc.querySelectorAll('table tbody tr, .player-row, .player-item');
      
      console.log(`🔍 Found ${playerRows.length} potential player rows`);

      playerRows.forEach((row, index) => {
        try {
          // Extract player data (adjust selectors based on actual HTML structure)
          const nameElement = row.querySelector('.player-name, .name, td:nth-child(1)');
          const teamElement = row.querySelector('.team, .player-team, td:nth-child(2)');
          const positionElement = row.querySelector('.position, .pos, td:nth-child(3)');
          const priceElement = row.querySelector('.price, .salary, td:nth-child(4)');
          const pointsElement = row.querySelector('.points, .total-points, td:nth-child(5)');
          const averageElement = row.querySelector('.average, .avg, td:nth-child(6)');

          const name = this.extractText(nameElement);
          const team = this.extractText(teamElement);
          const position = this.extractText(positionElement);
          
          if (name && team && position) {
            const player: ScrapedPlayer = {
              id: this.generatePlayerId(name, team),
              name,
              team,
              position,
              price: this.extractNumber(this.extractText(priceElement)),
              points: this.extractNumber(this.extractText(pointsElement)),
              average: this.extractNumber(this.extractText(averageElement)),
              form: 0, // Will be calculated or scraped separately
              source: 'nrlsupercoachstats',
              scraped_at: timestamp
            };

            players.push(player);
          }
        } catch (error) {
          console.warn(`⚠️ Error parsing player row ${index}:`, error);
        }
      });

      console.log(`✅ Successfully scraped ${players.length} players from nrlsupercoachstats.com`);

      return {
        success: true,
        data: players,
        timestamp,
        source: 'nrlsupercoachstats.com'
      };

    } catch (error) {
      console.error('❌ Error scraping nrlsupercoachstats.com:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown scraping error',
        timestamp,
        source: 'nrlsupercoachstats.com'
      };
    }
  }

  /**
   * Scrape NRL SuperCoach Live website
   */
  static async scrapeNRLSuperCoachLive(): Promise<ScrapeResult> {
    const timestamp = new Date().toISOString();
    
    try {
      console.log('🕷️ Scraping nrlsupercoachlive.com...');
      
      // Main players page
      const url = 'https://www.nrlsupercoachlive.com/players';
      const response = await this.rateLimitedFetch(url);
      const html = await response.text();
      const doc = this.parseHTML(html);

      console.log('📄 HTML content received, parsing ownership data...');

      const ownershipData: Array<{
        id: string;
        name: string;
        team: string;
        ownership: number;
        price_change: number;
      }> = [];
      
      // Look for ownership table rows (adjust selectors based on actual site structure)
      const ownershipRows = doc.querySelectorAll('table tbody tr, .ownership-row, .player-ownership');
      
      console.log(`🔍 Found ${ownershipRows.length} potential ownership rows`);

      ownershipRows.forEach((row, index) => {
        try {
          // Extract ownership data (adjust selectors based on actual HTML structure)
          const nameElement = row.querySelector('.player-name, .name, td:nth-child(1)');
          const teamElement = row.querySelector('.team, .player-team, td:nth-child(2)');
          const ownershipElement = row.querySelector('.ownership, .owned, td:nth-child(3)');
          const priceChangeElement = row.querySelector('.price-change, .change, td:nth-child(4)');

          const name = this.extractText(nameElement);
          const team = this.extractText(teamElement);
          
          if (name && team) {
            const ownershipInfo = {
              id: this.generatePlayerId(name, team),
              name,
              team,
              ownership: this.extractNumber(this.extractText(ownershipElement)),
              price_change: this.extractNumber(this.extractText(priceChangeElement))
            };

            ownershipData.push(ownershipInfo);
          }
        } catch (error) {
          console.warn(`⚠️ Error parsing ownership row ${index}:`, error);
        }
      });

      console.log(`✅ Successfully scraped ownership data for ${ownershipData.length} players from nrlsupercoachlive.com`);

      return {
        success: true,
        data: ownershipData,
        timestamp,
        source: 'nrlsupercoachlive.com'
      };

    } catch (error) {
      console.error('❌ Error scraping nrlsupercoachlive.com:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown scraping error',
        timestamp,
        source: 'nrlsupercoachlive.com'
      };
    }
  }

  /**
   * Combine data from both sources
   */
  static async scrapeAllSources(): Promise<{
    players: ScrapedPlayer[];
    ownership: any[];
    errors: string[];
    timestamp: string;
  }> {
    console.log('🚀 Starting comprehensive NRL data scraping...');
    
    const errors: string[] = [];
    let players: ScrapedPlayer[] = [];
    let ownership: any[] = [];

    // Scrape player stats
    const statsResult = await this.scrapeNRLSuperCoachStats();
    if (statsResult.success) {
      players = statsResult.data || [];
    } else {
      errors.push(`nrlsupercoachstats.com: ${statsResult.error}`);
    }

    // Scrape ownership data
    const ownershipResult = await this.scrapeNRLSuperCoachLive();
    if (ownershipResult.success) {
      ownership = ownershipResult.data || [];
    } else {
      errors.push(`nrlsupercoachlive.com: ${ownershipResult.error}`);
    }

    // Merge ownership data into player data
    const ownershipMap = new Map(ownership.map(o => [o.id, o]));
    
    players.forEach(player => {
      const ownershipInfo = ownershipMap.get(player.id);
      if (ownershipInfo) {
        player.ownership = ownershipInfo.ownership;
        player.price_change = ownershipInfo.price_change;
      }
    });

    console.log(`📊 Scraping complete: ${players.length} players, ${ownership.length} ownership records, ${errors.length} errors`);

    return {
      players,
      ownership,
      errors,
      timestamp: new Date().toISOString()
    };
  }
}

export default WebScraperService;
