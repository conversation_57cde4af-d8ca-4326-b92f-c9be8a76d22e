#!/usr/bin/env python3
"""
SportRadar API Server for FantasyPro
Provides NRL player data from SportRadar API
"""

import os
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from sportradar_client import SportRadarNRLClient

# Create FastAPI app
app = FastAPI(
    title="FantasyPro SportRadar API",
    description="NRL player data from SportRadar",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize SportRadar client
sportradar_client = None
SPORTRADAR_API_KEY = "aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN"

if SPORTRADAR_API_KEY:
    try:
        sportradar_client = SportRadarNRLClient(SPORTRADAR_API_KEY)
        print("✅ SportRadar client initialized")
    except Exception as e:
        print(f"❌ Failed to initialize SportRadar client: {e}")
else:
    print("⚠️  SportRadar API key not found")

@app.get("/")
async def root():
    return {"message": "FantasyPro SportRadar API", "status": "running"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "sportradar_available": sportradar_client is not None
    }

@app.get("/players")
async def get_all_players():
    """Get all NRL players from SportRadar"""
    if not sportradar_client:
        raise HTTPException(status_code=503, detail="SportRadar API not available")
    
    try:
        players = sportradar_client.get_all_players()
        return {
            "source": "sportradar",
            "count": len(players),
            "players": players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving players: {str(e)}")

@app.get("/teams")
async def get_teams():
    """Get all NRL teams from SportRadar"""
    if not sportradar_client:
        raise HTTPException(status_code=503, detail="SportRadar API not available")
    
    try:
        teams = sportradar_client.get_team_list()
        return {
            "source": "sportradar",
            "count": len(teams),
            "teams": teams
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving teams: {str(e)}")

@app.get("/players/search")
async def search_players(q: str = "", limit: int = 20):
    """Search players by name"""
    if not sportradar_client:
        raise HTTPException(status_code=503, detail="SportRadar API not available")
    
    try:
        all_players = sportradar_client.get_all_players()
        
        if q:
            # Filter players by name
            filtered_players = [
                player for player in all_players 
                if q.lower() in player.get('name', '').lower()
            ]
        else:
            filtered_players = all_players
        
        # Limit results
        limited_players = filtered_players[:limit]
        
        return {
            "source": "sportradar",
            "query": q,
            "count": len(limited_players),
            "total_matches": len(filtered_players),
            "players": limited_players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching players: {str(e)}")

@app.get("/team/{team_id}/players")
async def get_team_players(team_id: str):
    """Get players from a specific team"""
    if not sportradar_client:
        raise HTTPException(status_code=503, detail="SportRadar API not available")
    
    try:
        players = sportradar_client.get_team_players(team_id)
        return {
            "source": "sportradar",
            "team_id": team_id,
            "count": len(players),
            "players": players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving team players: {str(e)}")

@app.post("/update")
async def update_data():
    """Force update of SportRadar data"""
    if not sportradar_client:
        raise HTTPException(status_code=503, detail="SportRadar API not available")
    
    try:
        result = sportradar_client.update_player_database()
        return {
            "status": "success",
            "message": "Data updated successfully",
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating data: {str(e)}")

if __name__ == "__main__":
    print("🏈 Starting SportRadar API Server")
    print("📊 Players: http://localhost:8002/players")
    print("🏈 Teams: http://localhost:8002/teams")
    print("🔍 Search: http://localhost:8002/players/search?q=player_name")
    print("🔍 Health: http://localhost:8002/health")
    print("📖 Docs: http://localhost:8002/docs")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8002,  # Different port
        reload=False,
        log_level="info"
    )
