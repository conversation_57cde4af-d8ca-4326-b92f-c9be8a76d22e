#!/usr/bin/env python3
"""
Database Population Script

Comprehensive script to pull all player names, team data, and statistics
from NRL SuperCoach Stats and populate the database for faster access.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from scrapers.nrl_supercoach_scraper import NRLSuperCoachScraper
from database.supercoach_db import supercoach_db
from utils.logging_config import setup_logging

logger = setup_logging(__name__)

async def populate_comprehensive_data():
    """Populate database with comprehensive NRL SuperCoach data"""
    print("🏈 Starting Comprehensive NRL SuperCoach Data Population")
    print("=" * 60)
    
    try:
        async with NRLSuperCoachScraper() as scraper:
            print("✅ Scraper initialized successfully")
            
            # 1. Scrape comprehensive data
            print("\n📊 Scraping comprehensive data from NRL SuperCoach Stats...")
            all_data = await scraper.scrape_all_data()
            
            if not all_data:
                print("❌ Failed to scrape data")
                return False
            
            players_count = len(all_data.get('combined_players', []))
            fixtures_count = len(all_data.get('fixtures', []))
            
            print(f"✅ Successfully scraped:")
            print(f"   - Players: {players_count}")
            print(f"   - Fixtures: {fixtures_count}")
            print(f"   - Teams with breakevens: {len(all_data.get('team_breakevens', {}))}")
            print(f"   - Teams with prices: {len(all_data.get('team_prices', {}))}")
            
            # 2. Store in database
            print("\n💾 Storing data in database...")
            success = supercoach_db.store_scraped_data(all_data)
            
            if success:
                print("✅ Data stored successfully")
            else:
                print("❌ Failed to store data")
                return False
            
            # 3. Verify database population
            print("\n🔍 Verifying database population...")
            
            # Get all players
            all_players = supercoach_db.get_all_players()
            print(f"📊 Total players in database: {len(all_players)}")
            
            # Get teams with player counts
            with supercoach_db.get_session() as session:
                from database.supercoach_models import NRLTeam
                teams = session.query(NRLTeam).all()
                
                print(f"🏈 Teams in database: {len(teams)}")
                print("\n📋 Team Summary:")
                
                for team in teams:
                    team_players = [p for p in all_players if p.team_id == team.id]
                    if team_players:
                        # Calculate team stats
                        total_value = sum(p.current_price or 0 for p in team_players)
                        avg_breakeven = sum(p.current_breakeven or 0 for p in team_players if p.current_breakeven) / len([p for p in team_players if p.current_breakeven]) if any(p.current_breakeven for p in team_players) else 0
                        
                        print(f"   🏈 {team.name} ({team.abbreviation}): {len(team_players)} players")
                        print(f"      💰 Total value: ${total_value:,.0f}")
                        print(f"      📊 Avg breakeven: {avg_breakeven:.1f}")
                        
                        # Show sample players
                        sample_players = team_players[:3]
                        for player in sample_players:
                            price_str = f"${player.current_price:,.0f}" if player.current_price else "No price"
                            be_str = f"BE {player.current_breakeven}" if player.current_breakeven else "No BE"
                            print(f"         - {player.name}: {price_str}, {be_str}")
                        
                        if len(team_players) > 3:
                            print(f"         ... and {len(team_players) - 3} more players")
                        print()
            
            # 4. Test analysis capabilities
            print("🧠 Testing analysis capabilities...")
            
            from analysis.supercoach_algorithms import supercoach_analyzer
            
            # Test price predictions
            price_predictions = supercoach_analyzer.predict_price_changes()
            print(f"💹 Generated {len(price_predictions)} price predictions")
            
            if price_predictions:
                print("   Top 3 price predictions:")
                for i, pred in enumerate(price_predictions[:3]):
                    with supercoach_db.get_session() as session:
                        from database.supercoach_models import SuperCoachPlayer
                        player = session.query(SuperCoachPlayer).filter_by(id=pred.player_id).first()
                        if player:
                            change_str = f"+${pred.price_change:,.0f}" if pred.price_change > 0 else f"${pred.price_change:,.0f}"
                            print(f"      {i+1}. {player.name} ({player.team.name}): {change_str} ({pred.confidence:.1%} confidence)")
            
            # Test trade recommendations
            trade_recs = supercoach_analyzer.generate_trade_recommendations(limit=3)
            print(f"🔄 Generated {len(trade_recs)} trade recommendations")
            
            if trade_recs:
                print("   Top trade recommendations:")
                for i, trade in enumerate(trade_recs):
                    with supercoach_db.get_session() as session:
                        from database.supercoach_models import SuperCoachPlayer
                        player_out = session.query(SuperCoachPlayer).filter_by(id=trade.player_out_id).first()
                        player_in = session.query(SuperCoachPlayer).filter_by(id=trade.player_in_id).first()
                        
                        if player_out and player_in:
                            print(f"      {i+1}. {player_out.name} → {player_in.name}")
                            print(f"         Expected gain: +{trade.expected_points_gain:.1f} pts")
                            print(f"         Confidence: {trade.confidence:.1%}")
            
            # 5. Test AI agent
            print("\n🤖 Testing AI agent capabilities...")
            
            from agents.supercoach_agent import supercoach_agent
            
            recommendations = supercoach_agent.generate_weekly_recommendations()
            print(f"💡 Generated {len(recommendations)} AI recommendations")
            
            if recommendations:
                print("   AI Recommendations by type:")
                rec_types = {}
                for rec in recommendations:
                    rec_types[rec.type] = rec_types.get(rec.type, 0) + 1
                
                for rec_type, count in rec_types.items():
                    print(f"      - {rec_type.replace('_', ' ').title()}: {count}")
            
            # 6. Final summary
            print("\n🎯 Database Population Summary")
            print("=" * 60)
            print(f"✅ Players populated: {len(all_players)}")
            print(f"✅ Teams populated: {len(teams)}")
            print(f"✅ Price predictions: {len(price_predictions)}")
            print(f"✅ Trade recommendations: {len(trade_recs)}")
            print(f"✅ AI recommendations: {len(recommendations)}")
            print(f"✅ Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            print("\n🚀 Database is now fully populated and ready for production use!")
            return True
            
    except Exception as e:
        logger.error(f"Error populating database: {e}")
        import traceback
        traceback.print_exc()
        return False

async def update_player_positions_and_stats():
    """Update player positions and additional stats from stats page"""
    print("\n📈 Updating player positions and statistics...")
    
    try:
        async with NRLSuperCoachScraper() as scraper:
            # Try to get more detailed player stats
            player_stats = await scraper.scrape_player_stats()
            
            if player_stats:
                print(f"📊 Found {len(player_stats)} player statistics")
                
                # Update database with additional stats
                with supercoach_db.get_session() as session:
                    from database.supercoach_models import SuperCoachPlayer
                    
                    updated_count = 0
                    for stat in player_stats:
                        player_name = stat.get('name')
                        if not player_name:
                            continue
                        
                        # Find player in database
                        player = session.query(SuperCoachPlayer).filter(
                            SuperCoachPlayer.name.ilike(f"%{player_name}%")
                        ).first()
                        
                        if player:
                            # Update additional stats
                            if stat.get('position'):
                                player.position = stat['position']
                            if stat.get('points'):
                                player.season_points = stat['points']
                            if stat.get('average'):
                                player.season_average = stat['average']
                            if stat.get('games'):
                                player.games_played = stat['games']
                            
                            updated_count += 1
                    
                    session.commit()
                    print(f"✅ Updated {updated_count} players with additional statistics")
            else:
                print("⚠️ No additional player statistics found")
                
    except Exception as e:
        logger.error(f"Error updating player stats: {e}")

def show_database_contents():
    """Show current database contents"""
    print("\n📋 Current Database Contents:")
    print("=" * 40)
    
    try:
        all_players = supercoach_db.get_all_players()
        
        # Group by team
        teams_dict = {}
        for player in all_players:
            team_name = player.team.name if player.team else "Unknown"
            if team_name not in teams_dict:
                teams_dict[team_name] = []
            teams_dict[team_name].append(player)
        
        for team_name, players in sorted(teams_dict.items()):
            print(f"\n🏈 {team_name}: {len(players)} players")
            for player in sorted(players, key=lambda x: x.name)[:5]:  # Show first 5
                price_str = f"${player.current_price:,.0f}" if player.current_price else "No price"
                be_str = f"BE {player.current_breakeven}" if player.current_breakeven else "No BE"
                pos_str = player.position if player.position != "Unknown" else "No pos"
                print(f"   - {player.name}: {price_str}, {be_str}, {pos_str}")
            
            if len(players) > 5:
                print(f"   ... and {len(players) - 5} more players")
    
    except Exception as e:
        logger.error(f"Error showing database contents: {e}")

if __name__ == "__main__":
    print("🏈 NRL SuperCoach Database Population Tool")
    print("=" * 50)
    
    # Run the population
    success = asyncio.run(populate_comprehensive_data())
    
    if success:
        # Update additional stats
        asyncio.run(update_player_positions_and_stats())
        
        # Show final contents
        show_database_contents()
        
        print("\n🎉 Database population completed successfully!")
        print("🚀 Ready to start API servers with full data!")
    else:
        print("\n❌ Database population failed!")
        print("🔧 Check logs for details and try again.")
