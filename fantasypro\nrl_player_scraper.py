#!/usr/bin/env python3
"""
NRL Player Data Scraper
Scrapes comprehensive player data from NRL.com and other sources
"""

import requests
import json
import time
import re
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NRLPlayerScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # NRL team mappings
        self.team_mappings = {
            'broncos': 'Brisbane Broncos',
            'bulldogs': 'Canterbury-Bankstown Bulldogs',
            'cowboys': 'North Queensland Cowboys',
            'dolphins': 'Dolphins',
            'dragons': 'St George Illawarra Dragons',
            'eels': 'Parramatta Eels',
            'knights': 'Newcastle Knights',
            'panthers': 'Penrith Panthers',
            'rabbitohs': 'South Sydney Rabbitohs',
            'raiders': 'Canberra Raiders',
            'roosters': 'Sydney Roosters',
            'sea-eagles': 'Manly Sea Eagles',
            'sharks': 'Cronulla Sharks',
            'storm': 'Melbourne Storm',
            'titans': 'Gold Coast Titans',
            'warriors': 'New Zealand Warriors',
            'wests-tigers': 'Wests Tigers'
        }
        
        self.position_mappings = {
            'Fullback': 'FLB',
            'Wing': 'CTW',
            'Centre': 'CTW',
            'Five-eighth': '5/8',
            'Halfback': 'HFB',
            'Hooker': 'HOK',
            'Prop': 'FRF',
            'Second-row': '2RF',
            'Lock': 'LCK',
            'Interchange': 'INT'
        }

    def get_nrl_players_api(self) -> List[Dict]:
        """
        Try to find and use the NRL API endpoints
        """
        players = []
        
        # Try different potential API endpoints
        api_endpoints = [
            'https://www.nrl.com/api/players',
            'https://api.nrl.com/players',
            'https://www.nrl.com/data/players',
            'https://nrl.com/api/v1/players'
        ]
        
        for endpoint in api_endpoints:
            try:
                logger.info(f"Trying API endpoint: {endpoint}")
                response = self.session.get(endpoint, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Success! Found {len(data)} players from {endpoint}")
                    return data
                    
            except Exception as e:
                logger.debug(f"Failed to fetch from {endpoint}: {e}")
                continue
        
        return players

    def scrape_nrl_players_page(self) -> List[Dict]:
        """
        Scrape players from the main NRL players page
        """
        players = []
        
        try:
            url = 'https://www.nrl.com/players/?competition=111'
            logger.info(f"Scraping NRL players page: {url}")
            
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for JavaScript data or AJAX endpoints
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string and ('player' in script.string.lower() or 'api' in script.string.lower()):
                    # Try to extract API endpoints or data from JavaScript
                    content = script.string
                    
                    # Look for API URLs
                    api_matches = re.findall(r'["\']https?://[^"\']*api[^"\']*players[^"\']*["\']', content)
                    for match in api_matches:
                        api_url = match.strip('"\'')
                        logger.info(f"Found potential API URL: {api_url}")
                        
                        try:
                            api_response = self.session.get(api_url, timeout=10)
                            if api_response.status_code == 200:
                                api_data = api_response.json()
                                if isinstance(api_data, list) and len(api_data) > 0:
                                    return api_data
                        except:
                            continue
            
            # If no API found, try to scrape HTML content
            player_elements = soup.find_all(['div', 'article', 'section'], class_=re.compile(r'player', re.I))
            
            for element in player_elements:
                player_data = self.extract_player_from_element(element)
                if player_data:
                    players.append(player_data)
                    
        except Exception as e:
            logger.error(f"Error scraping NRL players page: {e}")
        
        return players

    def scrape_team_rosters(self) -> List[Dict]:
        """
        Scrape individual team rosters
        """
        all_players = []
        
        for team_slug, team_name in self.team_mappings.items():
            try:
                logger.info(f"Scraping {team_name} roster...")
                
                # Try different URL patterns
                urls = [
                    f'https://www.nrl.com/teams/{team_slug}/players/',
                    f'https://www.{team_slug}.com.au/teams/nrl/players/',
                    f'https://www.nrl.com/clubs/{team_slug}/players/'
                ]
                
                for url in urls:
                    try:
                        response = self.session.get(url, timeout=10)
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.content, 'html.parser')
                            team_players = self.extract_team_players(soup, team_name)
                            all_players.extend(team_players)
                            break
                    except:
                        continue
                
                # Rate limiting
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error scraping {team_name}: {e}")
                continue
        
        return all_players

    def extract_player_from_element(self, element) -> Optional[Dict]:
        """
        Extract player data from HTML element
        """
        try:
            name_elem = element.find(['h1', 'h2', 'h3', 'h4', 'a'], class_=re.compile(r'name|title', re.I))
            if not name_elem:
                name_elem = element.find('a')
            
            if name_elem:
                name = name_elem.get_text(strip=True)
                
                # Extract other details
                team = self.extract_team_from_element(element)
                position = self.extract_position_from_element(element)
                
                return {
                    'name': name,
                    'team': team,
                    'position': position,
                    'source': 'nrl_scrape'
                }
        except:
            pass
        
        return None

    def extract_team_from_element(self, element) -> str:
        """Extract team name from element"""
        team_elem = element.find(['span', 'div', 'p'], class_=re.compile(r'team|club', re.I))
        if team_elem:
            return team_elem.get_text(strip=True)
        return 'Unknown'

    def extract_position_from_element(self, element) -> str:
        """Extract position from element"""
        pos_elem = element.find(['span', 'div', 'p'], class_=re.compile(r'position|pos', re.I))
        if pos_elem:
            pos_text = pos_elem.get_text(strip=True)
            return self.position_mappings.get(pos_text, pos_text)
        return 'Unknown'

    def extract_team_players(self, soup, team_name: str) -> List[Dict]:
        """Extract players from team page"""
        players = []
        
        # Look for player elements
        player_elements = soup.find_all(['div', 'article', 'li'], class_=re.compile(r'player|roster', re.I))
        
        for element in player_elements:
            player_data = self.extract_player_from_element(element)
            if player_data:
                player_data['team'] = team_name
                players.append(player_data)
        
        return players

    def get_all_players(self) -> List[Dict]:
        """
        Get all NRL players using multiple methods
        """
        all_players = []
        
        # Method 1: Try API endpoints
        logger.info("Attempting to fetch from API endpoints...")
        api_players = self.get_nrl_players_api()
        if api_players:
            all_players.extend(api_players)
            logger.info(f"Found {len(api_players)} players from API")
        
        # Method 2: Scrape main players page
        if not all_players:
            logger.info("Attempting to scrape main players page...")
            page_players = self.scrape_nrl_players_page()
            all_players.extend(page_players)
            logger.info(f"Found {len(page_players)} players from main page")
        
        # Method 3: Scrape individual team rosters
        if not all_players:
            logger.info("Attempting to scrape team rosters...")
            roster_players = self.scrape_team_rosters()
            all_players.extend(roster_players)
            logger.info(f"Found {len(roster_players)} players from team rosters")
        
        # Remove duplicates and clean data
        unique_players = self.deduplicate_players(all_players)
        
        logger.info(f"Total unique players found: {len(unique_players)}")
        return unique_players

    def deduplicate_players(self, players: List[Dict]) -> List[Dict]:
        """Remove duplicate players based on name"""
        seen_names = set()
        unique_players = []
        
        for player in players:
            name = player.get('name', '').strip()
            if name and name not in seen_names:
                seen_names.add(name)
                unique_players.append(player)
        
        return unique_players

    def save_players_to_json(self, players: List[Dict], filename: str = 'nrl_players.json'):
        """Save players data to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(players, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(players)} players to {filename}")
        except Exception as e:
            logger.error(f"Error saving to {filename}: {e}")

def main():
    """Main function to run the scraper"""
    scraper = NRLPlayerScraper()
    
    logger.info("Starting NRL player data scraping...")
    players = scraper.get_all_players()
    
    if players:
        scraper.save_players_to_json(players)
        
        # Print sample data
        logger.info("Sample players:")
        for player in players[:5]:
            logger.info(f"  {player}")
    else:
        logger.warning("No players found!")

if __name__ == "__main__":
    main()
