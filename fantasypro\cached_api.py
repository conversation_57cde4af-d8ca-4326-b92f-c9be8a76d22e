#!/usr/bin/env python3
"""
Cached API Server for FantasyPro
Uses local cached data for fast queries, with SportRadar fallback
"""

import os
from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from data_cache import FantasyProDataCache

# Create FastAPI app
app = FastAPI(
    title="FantasyPro Cached API",
    description="Fast NRL player data from local cache with SportRadar integration",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize cache
cache = FantasyProDataCache()

@app.get("/")
async def root():
    return {"message": "FantasyPro Cached API", "status": "running"}

@app.get("/health")
async def health_check():
    stats = cache.get_cache_stats()
    return {
        "status": "healthy",
        "cache_stats": stats
    }

@app.get("/players")
async def get_all_players(limit: int = Query(default=100, le=1000)):
    """Get all NRL players from cache"""
    try:
        players = cache.get_all_players()
        
        # Apply limit
        limited_players = players[:limit] if limit else players
        
        return {
            "source": "cache",
            "count": len(limited_players),
            "total_available": len(players),
            "players": limited_players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving players: {str(e)}")

@app.get("/teams")
async def get_teams():
    """Get all NRL teams from cache"""
    try:
        teams = cache.get_all_teams()
        return {
            "source": "cache",
            "count": len(teams),
            "teams": teams
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving teams: {str(e)}")

@app.get("/players/search")
async def search_players(
    q: str = Query(..., min_length=1, description="Search query"),
    limit: int = Query(default=20, le=100)
):
    """Search players by name or team"""
    try:
        players = cache.search_players(q, limit)
        return {
            "source": "cache",
            "query": q,
            "count": len(players),
            "players": players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching players: {str(e)}")

@app.get("/team/{team_id}/players")
async def get_team_players(team_id: str):
    """Get players from a specific team"""
    try:
        players = cache.get_team_players(team_id)
        return {
            "source": "cache",
            "team_id": team_id,
            "count": len(players),
            "players": players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving team players: {str(e)}")

@app.get("/players/by-position")
async def get_players_by_position(position: str = Query(..., description="Player position")):
    """Get players by position"""
    try:
        all_players = cache.get_all_players()
        filtered_players = [
            player for player in all_players 
            if player.get('position', '').lower() == position.lower()
        ]
        
        return {
            "source": "cache",
            "position": position,
            "count": len(filtered_players),
            "players": filtered_players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving players by position: {str(e)}")

@app.get("/players/by-team")
async def get_players_by_team(team: str = Query(..., description="Team name")):
    """Get players by team name"""
    try:
        all_players = cache.get_all_players()
        filtered_players = [
            player for player in all_players 
            if team.lower() in player.get('team', '').lower()
        ]
        
        return {
            "source": "cache",
            "team": team,
            "count": len(filtered_players),
            "players": filtered_players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving players by team: {str(e)}")

@app.get("/stats")
async def get_stats():
    """Get comprehensive statistics about the cached data"""
    try:
        stats = cache.get_cache_stats()
        
        # Get additional stats
        all_players = cache.get_all_players()
        teams = cache.get_all_teams()
        
        # Count players by team
        team_counts = {}
        for player in all_players:
            team = player.get('team', 'Unknown')
            team_counts[team] = team_counts.get(team, 0) + 1
        
        # Count players by position
        position_counts = {}
        for player in all_players:
            position = player.get('position', 'Unknown')
            position_counts[position] = position_counts.get(position, 0) + 1
        
        return {
            "cache_stats": stats,
            "team_player_counts": team_counts,
            "position_counts": position_counts,
            "total_teams": len(teams),
            "total_players": len(all_players)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving stats: {str(e)}")

@app.post("/cache/refresh")
async def refresh_cache():
    """Refresh cache from SportRadar (requires API key)"""
    try:
        api_key = "aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN"
        result = cache.update_from_sportradar(api_key)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error refreshing cache: {str(e)}")

@app.get("/cache/status")
async def cache_status():
    """Get detailed cache status"""
    try:
        stats = cache.get_cache_stats()
        return {
            "cache_fresh": cache.is_cache_fresh(),
            "cache_age_hours": "calculated_if_needed",
            "stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting cache status: {str(e)}")

# Predictive search endpoint optimized for frontend
@app.get("/search/predictive")
async def predictive_search(
    q: str = Query(..., min_length=1, description="Search query"),
    limit: int = Query(default=10, le=50),
    include_team: bool = Query(default=True, description="Include team in search"),
    fuzzy: bool = Query(default=False, description="Enable fuzzy matching")
):
    """
    Optimized predictive search for frontend typeahead components
    """
    try:
        all_players = cache.get_all_players()
        query_lower = q.lower()
        
        # Score-based matching for better relevance
        scored_players = []
        
        for player in all_players:
            name = player.get('name', '').lower()
            team = player.get('team', '').lower()
            
            score = 0
            
            # Exact name start match (highest priority)
            if name.startswith(query_lower):
                score += 100
            # Name contains query
            elif query_lower in name:
                score += 50
            # Team contains query (if enabled)
            elif include_team and query_lower in team:
                score += 25
            
            if score > 0:
                player_copy = player.copy()
                player_copy['_search_score'] = score
                scored_players.append(player_copy)
        
        # Sort by score (descending) then by name
        scored_players.sort(key=lambda x: (-x['_search_score'], x.get('name', '')))
        
        # Remove score from results and limit
        results = []
        for player in scored_players[:limit]:
            player_result = {k: v for k, v in player.items() if k != '_search_score'}
            results.append(player_result)
        
        return {
            "source": "cache",
            "query": q,
            "count": len(results),
            "total_matches": len(scored_players),
            "players": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in predictive search: {str(e)}")

if __name__ == "__main__":
    print("🏈 Starting FantasyPro Cached API Server")
    print("📊 Players: http://localhost:8003/players")
    print("🔍 Search: http://localhost:8003/players/search?q=player_name")
    print("🚀 Predictive: http://localhost:8003/search/predictive?q=ted")
    print("📈 Stats: http://localhost:8003/stats")
    print("🔍 Health: http://localhost:8003/health")
    print("📖 Docs: http://localhost:8003/docs")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8003,  # Different port for cached API
        reload=False,
        log_level="info"
    )
