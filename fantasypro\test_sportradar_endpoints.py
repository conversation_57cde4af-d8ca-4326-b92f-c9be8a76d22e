#!/usr/bin/env python3
"""
Test SportRadar API endpoints to discover available injury data
"""

import requests
import json
import time
from typing import Dict, List, Optional

class SportRadarAPIExplorer:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.sportradar.com/rugby-league/trial/v3"
        self.nrl_competition_id = "sr:competition:294"
        
    def _make_request(self, endpoint: str) -> Optional[Dict]:
        """Make a request to SportRadar API"""
        url = f"{self.base_url}/{endpoint}"
        params = {"api_key": self.api_key}
        
        try:
            print(f"Testing: {url}")
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ SUCCESS: {endpoint}")
                return data
            elif response.status_code == 429:
                print(f"⚠️  RATE LIMITED: {endpoint}")
                time.sleep(2)
                return None
            else:
                print(f"❌ FAILED: {endpoint} - Status: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ ERROR: {endpoint} - {e}")
            return None
    
    def get_current_season_id(self) -> str:
        """Get the current season ID"""
        try:
            seasons_data = self._make_request(f"competitions/{self.nrl_competition_id}/seasons.json")
            if seasons_data and 'seasons' in seasons_data:
                current_season = seasons_data['seasons'][-1]
                season_id = current_season.get('id', '')
                print(f"Current season ID: {season_id}")
                return season_id
        except:
            pass
        return ""
    
    def explore_injury_endpoints(self):
        """Explore potential injury endpoints"""
        print("🔍 Exploring SportRadar NRL API for injury endpoints...")
        print("=" * 60)
        
        season_id = self.get_current_season_id()
        
        # List of potential injury endpoints to test
        endpoints_to_test = [
            # Direct injury endpoints
            "injuries.json",
            f"competitions/{self.nrl_competition_id}/injuries.json",
            f"seasons/{season_id}/injuries.json",
            
            # Player injury endpoints
            f"competitions/{self.nrl_competition_id}/players/injuries.json",
            f"seasons/{season_id}/players/injuries.json",
            
            # Team injury endpoints
            f"competitions/{self.nrl_competition_id}/teams/injuries.json",
            f"seasons/{season_id}/teams/injuries.json",
            
            # Medical/health endpoints
            f"competitions/{self.nrl_competition_id}/medical.json",
            f"seasons/{season_id}/medical.json",
            f"competitions/{self.nrl_competition_id}/health.json",
            f"seasons/{season_id}/health.json",
            
            # Player status endpoints
            f"competitions/{self.nrl_competition_id}/player_status.json",
            f"seasons/{season_id}/player_status.json",
            
            # Availability endpoints
            f"competitions/{self.nrl_competition_id}/availability.json",
            f"seasons/{season_id}/availability.json",
        ]
        
        successful_endpoints = []
        
        for endpoint in endpoints_to_test:
            if season_id or "seasons/" not in endpoint:
                data = self._make_request(endpoint)
                if data:
                    successful_endpoints.append((endpoint, data))
                    self._analyze_response_structure(endpoint, data)
                time.sleep(1)  # Rate limiting
        
        print("\n" + "=" * 60)
        print(f"✅ Found {len(successful_endpoints)} working endpoints")
        
        return successful_endpoints
    
    def _analyze_response_structure(self, endpoint: str, data: Dict):
        """Analyze the structure of API response"""
        print(f"\n📊 Analyzing response from: {endpoint}")
        
        if isinstance(data, dict):
            print(f"   Type: Dictionary with {len(data)} keys")
            print(f"   Keys: {list(data.keys())}")
            
            # Look for injury-related fields
            injury_keywords = ['injury', 'injured', 'medical', 'health', 'status', 'availability']
            for key, value in data.items():
                if any(keyword in key.lower() for keyword in injury_keywords):
                    print(f"   🏥 INJURY-RELATED: {key} = {type(value)}")
                    if isinstance(value, list) and value:
                        print(f"      Sample item: {value[0] if value else 'Empty'}")
        
        elif isinstance(data, list):
            print(f"   Type: List with {len(data)} items")
            if data:
                print(f"   Sample item keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not a dict'}")
    
    def explore_team_profiles(self):
        """Explore team profiles for injury data"""
        print("\n🏉 Exploring team profiles for injury data...")
        
        # Get teams from standings
        season_id = self.get_current_season_id()
        standings_data = self._make_request(f"seasons/{season_id}/standings.json")
        
        if standings_data and 'standings' in standings_data:
            main_standing = standings_data['standings'][0]
            if 'groups' in main_standing and main_standing['groups']:
                group = main_standing['groups'][0]
                if 'standings' in group:
                    team_standings = group['standings']
                    
                    # Test first 3 teams
                    for i, standing in enumerate(team_standings[:3]):
                        competitor = standing.get('competitor', {})
                        team_id = competitor.get('id')
                        team_name = competitor.get('name')
                        
                        if team_id:
                            print(f"\n🔍 Testing team profile: {team_name}")
                            team_data = self._make_request(f"competitors/{team_id}/profile.json")
                            
                            if team_data and 'players' in team_data:
                                players = team_data['players']
                                print(f"   Found {len(players)} players")
                                
                                # Check for injury-related fields in players
                                injury_fields = []
                                for player in players[:5]:  # Check first 5 players
                                    for key in player.keys():
                                        if any(keyword in key.lower() for keyword in ['injury', 'status', 'medical', 'health', 'available']):
                                            if key not in injury_fields:
                                                injury_fields.append(key)
                                                print(f"   🏥 Found injury field: {key} = {player[key]}")
                            
                            time.sleep(1)  # Rate limiting
    
    def test_specific_endpoints(self):
        """Test specific endpoints that might contain injury data"""
        print("\n🎯 Testing specific endpoints...")
        
        season_id = self.get_current_season_id()
        
        # Test match-related endpoints that might have injury reports
        match_endpoints = [
            f"seasons/{season_id}/summaries.json",
            f"competitions/{self.nrl_competition_id}/info.json",
            f"seasons/{season_id}/info.json",
        ]
        
        for endpoint in match_endpoints:
            data = self._make_request(endpoint)
            if data:
                self._analyze_response_structure(endpoint, data)
            time.sleep(1)

def main():
    """Test SportRadar API endpoints"""
    api_key = "aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN"
    
    explorer = SportRadarAPIExplorer(api_key)
    
    # Explore injury endpoints
    successful_endpoints = explorer.explore_injury_endpoints()
    
    # Explore team profiles
    explorer.explore_team_profiles()
    
    # Test specific endpoints
    explorer.test_specific_endpoints()
    
    print("\n" + "=" * 60)
    print("🏁 API Exploration Complete!")
    
    if successful_endpoints:
        print("\n📋 Summary of working endpoints:")
        for endpoint, _ in successful_endpoints:
            print(f"   ✅ {endpoint}")
    else:
        print("\n❌ No injury-specific endpoints found")
        print("💡 Recommendation: Use team profiles and player status fields")

if __name__ == "__main__":
    main()
