#!/usr/bin/env python3
"""
Test the updated scraper
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from scrapers.nrl_supercoach_scraper import NRLSuperCoachScraper

async def test_updated_scraper():
    """Test the updated scraper"""
    print("🔍 Testing Updated NRL SuperCoach Scraper...")
    
    try:
        async with NRLSuperCoachScraper() as scraper:
            print("  ✅ Scraper initialized successfully")
            
            # Test team breakevens
            print("  📊 Testing team breakevens scraping...")
            breakevens = await scraper.scrape_team_breakevens()
            print(f"  ✅ Scraped breakevens for {len(breakevens)} teams")
            
            # Show some results
            for team_name, players in list(breakevens.items())[:3]:
                print(f"    🏈 {team_name}: {len(players)} players")
                for player in players[:3]:  # Show first 3 players
                    print(f"      - {player['name']}: BE {player['breakeven']}")
            
            # Test team prices
            print("  💰 Testing team prices scraping...")
            prices = await scraper.scrape_team_prices()
            print(f"  ✅ Scraped prices for {len(prices)} teams")
            
            # Show some results
            for team_name, players in list(prices.items())[:2]:
                print(f"    🏈 {team_name}: {len(players)} players")
                for player in players[:2]:  # Show first 2 players
                    print(f"      - {player['name']}: ${player['price']:,.0f}" if player['price'] else f"      - {player['name']}: No price")
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_updated_scraper())
