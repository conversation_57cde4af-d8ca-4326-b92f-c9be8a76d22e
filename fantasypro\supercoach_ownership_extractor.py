#!/usr/bin/env python3
"""
SuperCoach Ownership Extractor
FINAL IMPLEMENTATION: Extract "Picked % Teams" ownership data from SuperCoach player tables
"""

import time
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)

class SuperCoachOwnershipExtractor:
    """Extract ownership percentages from SuperCoach 'Picked % Teams' column"""
    
    def __init__(self, email: str = "<EMAIL>", password: str = "SuperKenny123!"):
        self.email = email
        self.password = password
        self.driver = None
        self.wait = None
        self.is_authenticated = False
        
        # Target URL for player stats with ownership data
        self.players_url = 'https://www.supercoach.com.au/nrl/classic/stats/players'
        
        self.ownership_data = {}
        self.cache_dir = Path("data/ownership_data")
        self.cache_dir.mkdir(exist_ok=True)
    
    def setup_driver(self) -> bool:
        """Setup Chrome driver for ownership extraction"""
        try:
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Run in visible mode to see the interface
            # options.add_argument('--headless')
            
            self.driver = webdriver.Chrome(
                service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
                options=options
            )
            
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 20)
            
            logger.info("✅ Chrome driver setup successful")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error setting up Chrome driver: {e}")
            return False
    
    def authenticate(self) -> bool:
        """Authenticate with SuperCoach using Auth0"""
        try:
            if not self.driver:
                if not self.setup_driver():
                    return False
            
            logger.info("🔐 Authenticating with SuperCoach...")
            
            # Navigate to Auth0 login directly
            auth0_url = "https://login.newscorpaustralia.com/login?state=hKFo2SBER3lEYktxVFNnN2RHSS1LLVlUQVNfOXlqclBtUng5VaFupWxvZ2luo3RpZNkgT2lsanBRUkhpY1lfM2JwOWJ6T19TNUFwZmx0TEhoY2SjY2lk2SBaWUNvdGxpaHFhR3VhcVNzU3Z1MEwydnhEZFFYQ3cxNg&client=ZYCotlihqaGuaqSsSvu0L2vxDdQXCw16&protocol=oauth2&response_type=token%20id_token&scope=openid%20profile&audience=newscorpaustralia&site=supercoach&redirect_uri=https%3A%2F%2Fwww.supercoach.com.au%2Fassets%2Fsites%2Fnews%2Fauth0%2Fcallback.html%3FredirectUri%3Dhttps%253A%252F%252Fwww.supercoach.com.au%252Fnrl&prevent_sign_up=&nonce=MwchNFYLpvljaSzA0FtG0kx5JhtggrZ5&auth0Client=eyJuYW1lIjoiYXV0aDAuanMiLCJ2ZXJzaW9uIjoiOS4yOC4wIn0%3D"
            self.driver.get(auth0_url)
            time.sleep(5)
            
            # Find and fill email field
            email_field = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'input[type="email"]')))
            email_field.clear()
            email_field.send_keys(self.email)
            time.sleep(1)
            
            # Find and fill password field
            password_field = self.driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
            password_field.clear()
            password_field.send_keys(self.password)
            time.sleep(1)
            
            # Submit form
            submit_button = self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            self.driver.execute_script("arguments[0].click();", submit_button)
            time.sleep(8)
            
            # Check if authentication was successful
            current_url = self.driver.current_url.lower()
            if 'supercoach.com.au' in current_url and 'login.newscorpaustralia.com' not in current_url:
                self.is_authenticated = True
                logger.info("✅ SuperCoach authentication successful")
                return True
            else:
                logger.error("❌ SuperCoach authentication failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error during authentication: {e}")
            return False
    
    def extract_ownership_data(self) -> Dict[str, Any]:
        """Extract ownership percentages from 'Picked % Teams' column"""
        try:
            if not self.authenticate():
                logger.error("Authentication failed")
                return {}
            
            logger.info("🎯 Navigating to player stats page...")
            self.driver.get(self.players_url)
            time.sleep(10)  # Wait for page to fully load
            
            ownership_results = {
                'extraction_timestamp': datetime.now().isoformat(),
                'source_url': self.players_url,
                'players_with_ownership': [],
                'total_players_found': 0,
                'extraction_method': 'picked_percent_teams_column'
            }
            
            # Look for the player stats table
            logger.info("🔍 Looking for player stats table...")

            # First, let's see what's on the page
            page_text = self.driver.page_source
            logger.info(f"📄 Page title: {self.driver.title}")
            logger.info(f"📄 Current URL: {self.driver.current_url}")

            # Check if we can find "Picked" text anywhere on the page
            if 'picked' in page_text.lower():
                logger.info("✅ Found 'picked' text on page")
            else:
                logger.warning("⚠️ No 'picked' text found on page")

            # Try multiple selectors for the table
            table_selectors = [
                'table',
                '.stats-table',
                '[data-testid="stats-table"]',
                '.player-stats-table',
                'table.table',
                '[class*="table"]',
                '[class*="stats"]'
            ]

            table = None
            all_tables_info = []

            for selector in table_selectors:
                try:
                    tables = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    logger.info(f"🔍 Selector '{selector}' found {len(tables)} tables")

                    for i, t in enumerate(tables):
                        try:
                            table_text = t.text.strip()
                            table_info = {
                                'selector': selector,
                                'index': i,
                                'text_length': len(table_text),
                                'has_picked': 'picked' in table_text.lower(),
                                'has_percent': '%' in table_text,
                                'has_teams': 'teams' in table_text.lower(),
                                'sample_text': table_text[:200] + '...' if len(table_text) > 200 else table_text
                            }
                            all_tables_info.append(table_info)

                            # Check if this table contains ownership data
                            if ('picked' in table_text.lower() and '%' in table_text and 'teams' in table_text.lower()) or \
                               ('ownership' in table_text.lower() and '%' in table_text) or \
                               (table_text.count('%') > 5):  # Likely has percentage data
                                table = t
                                logger.info(f"✅ Found potential stats table with selector: {selector} (index {i})")
                                break
                        except Exception as e:
                            logger.debug(f"Error analyzing table {i}: {e}")
                            continue

                    if table:
                        break
                except Exception as e:
                    logger.debug(f"Error with selector {selector}: {e}")
                    continue

            # Log all table information for debugging
            logger.info(f"📊 Found {len(all_tables_info)} total tables:")
            for info in all_tables_info:
                logger.info(f"   {info['selector']}[{info['index']}]: {info['text_length']} chars, picked={info['has_picked']}, %={info['has_percent']}, teams={info['has_teams']}")

            if not table:
                logger.error("❌ Could not find player stats table with ownership data")

                # Try to find any table with percentage data
                logger.info("🔍 Looking for any table with percentage data...")
                all_tables = self.driver.find_elements(By.CSS_SELECTOR, 'table')
                for i, t in enumerate(all_tables):
                    try:
                        text = t.text
                        if text.count('%') >= 3:  # Has multiple percentages
                            logger.info(f"📊 Table {i} has {text.count('%')} percentage signs")
                            table = t
                            break
                    except:
                        continue

                if not table:
                    # Last resort: try to find ownership data by text patterns
                    logger.info("🔍 Last resort: searching for ownership patterns in page text...")
                    ownership_results['extraction_method'] = 'text_pattern_fallback'

                    # Look for percentage patterns near player names
                    import re

                    # Find all text elements that might contain player data
                    all_elements = self.driver.find_elements(By.CSS_SELECTOR, '*')

                    for element in all_elements:
                        try:
                            element_text = element.text.strip()
                            if element_text and '%' in element_text and len(element_text) < 100:
                                # Look for percentage patterns
                                percentage_matches = re.findall(r'(\d+\.?\d*)%', element_text)
                                if percentage_matches:
                                    logger.info(f"📊 Found percentage in element: {element_text[:50]}...")
                        except:
                            continue

                    return ownership_results
            
            # Extract table headers to find the "Picked % Teams" column index
            headers = []
            header_elements = table.find_elements(By.CSS_SELECTOR, 'thead th, thead td, tr:first-child th, tr:first-child td')
            
            picked_teams_column_index = -1
            player_name_column_index = -1
            
            for i, header in enumerate(header_elements):
                header_text = header.text.strip()
                headers.append(header_text)
                
                # Look for "Picked % Teams" column
                if 'picked' in header_text.lower() and '%' in header_text and 'teams' in header_text.lower():
                    picked_teams_column_index = i
                    logger.info(f"✅ Found 'Picked % Teams' column at index {i}")
                
                # Look for player name column
                if any(keyword in header_text.lower() for keyword in ['player', 'name']) or i == 0:
                    player_name_column_index = i
            
            if picked_teams_column_index == -1:
                logger.error("❌ Could not find 'Picked % Teams' column")
                logger.info(f"Available headers: {headers}")
                return ownership_results
            
            logger.info(f"📊 Headers found: {headers}")
            logger.info(f"🎯 Player name column: {player_name_column_index}, Ownership column: {picked_teams_column_index}")
            
            # Extract player data rows
            rows = table.find_elements(By.CSS_SELECTOR, 'tbody tr, tr')
            
            for row_index, row in enumerate(rows):
                try:
                    cells = row.find_elements(By.CSS_SELECTOR, 'td, th')
                    
                    if len(cells) <= max(picked_teams_column_index, player_name_column_index):
                        continue  # Skip rows that don't have enough columns
                    
                    # Extract player name
                    player_name = ""
                    if player_name_column_index >= 0 and player_name_column_index < len(cells):
                        player_name_element = cells[player_name_column_index]
                        player_name = player_name_element.text.strip()
                        
                        # Sometimes player name is in a nested element
                        if not player_name:
                            nested_elements = player_name_element.find_elements(By.CSS_SELECTOR, 'a, span, div')
                            for elem in nested_elements:
                                if elem.text.strip():
                                    player_name = elem.text.strip()
                                    break
                    
                    # Extract ownership percentage
                    ownership_percentage = ""
                    if picked_teams_column_index < len(cells):
                        ownership_cell = cells[picked_teams_column_index]
                        ownership_text = ownership_cell.text.strip()
                        
                        # Extract percentage value
                        import re
                        percentage_match = re.search(r'(\d+\.?\d*)%?', ownership_text)
                        if percentage_match:
                            ownership_percentage = percentage_match.group(1)
                    
                    # Only add if we have both player name and ownership data
                    if player_name and ownership_percentage:
                        player_data = {
                            'player_name': player_name,
                            'ownership_percentage': float(ownership_percentage) if ownership_percentage else 0.0,
                            'raw_ownership_text': ownership_text,
                            'row_index': row_index
                        }
                        
                        ownership_results['players_with_ownership'].append(player_data)
                        logger.info(f"✅ {player_name}: {ownership_percentage}%")
                
                except Exception as e:
                    logger.debug(f"Error processing row {row_index}: {e}")
                    continue
            
            ownership_results['total_players_found'] = len(ownership_results['players_with_ownership'])
            
            # Save results
            self._save_ownership_data(ownership_results)
            
            logger.info(f"🎉 Successfully extracted ownership data for {ownership_results['total_players_found']} players!")
            
            return ownership_results
            
        except Exception as e:
            logger.error(f"❌ Error extracting ownership data: {e}")
            return {}
        finally:
            if self.driver:
                self.driver.quit()
    
    def _save_ownership_data(self, data: Dict[str, Any]):
        """Save ownership data to cache"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_file = self.cache_dir / f"ownership_data_{timestamp}.json"
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Also save as latest
            latest_file = self.cache_dir / "ownership_data_latest.json"
            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Saved ownership data to {results_file}")
            
        except Exception as e:
            logger.error(f"Error saving ownership data: {e}")

def main():
    """Main ownership extraction function"""
    print("🎯 SuperCoach Ownership Data Extractor")
    print("=" * 50)
    print("🔍 Extracting 'Picked % Teams' ownership percentages...")
    
    extractor = SuperCoachOwnershipExtractor()
    results = extractor.extract_ownership_data()
    
    if results and results.get('total_players_found', 0) > 0:
        print("\n🎉 OWNERSHIP EXTRACTION SUCCESSFUL!")
        
        total_players = results['total_players_found']
        print(f"📊 Players with ownership data: {total_players}")
        
        # Show sample data
        sample_players = results.get('players_with_ownership', [])[:10]
        if sample_players:
            print(f"\n📋 Sample ownership data:")
            for player in sample_players:
                name = player['player_name']
                ownership = player['ownership_percentage']
                print(f"   {name}: {ownership}%")
        
        print(f"\n✅ FINAL STEP COMPLETE!")
        print(f"   🎯 Ownership percentages successfully extracted from 'Picked % Teams' column")
        print(f"   💾 Data saved for integration with FantasyPro ML models")
        print(f"   🚀 Ready for production use!")
        
    else:
        print("❌ Ownership extraction failed")
        print("💡 Check authentication and table structure")

if __name__ == "__main__":
    main()
