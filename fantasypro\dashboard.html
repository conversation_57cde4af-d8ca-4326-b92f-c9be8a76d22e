<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FantasyPro - NRL SuperCoach Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1rem;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .players-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .players-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .player-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .player-card:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .player-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .player-team {
            color: #667eea;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .player-stats {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
        }

        .breakeven {
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
            color: white;
        }

        .breakeven.positive {
            background: #dc3545;
        }

        .breakeven.negative {
            background: #28a745;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin: 50px 0;
        }

        .error {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
            margin: 20px auto;
            display: block;
        }

        .refresh-btn:hover {
            background: #5a6fd8;
        }

        .last-updated {
            text-align: center;
            color: white;
            margin-top: 20px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏈 FantasyPro Dashboard</h1>
            <p>Real NRL SuperCoach Data & Intelligence</p>
        </div>

        <div id="loading" class="loading">
            🔄 Loading real NRL SuperCoach data...
        </div>

        <div id="error" class="error" style="display: none;">
            ❌ Error loading data. Please check if the API server is running.
        </div>

        <div id="dashboard-content" style="display: none;">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-players">-</div>
                    <div class="stat-label">Total Players</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="active-players">-</div>
                    <div class="stat-label">Active Players</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-teams">-</div>
                    <div class="stat-label">NRL Teams</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="data-source">Real</div>
                    <div class="stat-label">Data Source</div>
                </div>
            </div>

            <div class="players-section">
                <h2 class="section-title">📊 Sample Players with Breakevens</h2>
                <div id="players-grid" class="players-grid">
                    <!-- Players will be loaded here -->
                </div>
            </div>

            <button class="refresh-btn" onclick="loadDashboard()">🔄 Refresh Data</button>
            
            <div class="last-updated">
                Last updated: <span id="last-updated">-</span>
            </div>
        </div>
    </div>

    <script>
        async function loadDashboard() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const content = document.getElementById('dashboard-content');
            
            loading.style.display = 'block';
            error.style.display = 'none';
            content.style.display = 'none';

            try {
                // Fetch dashboard data
                const response = await fetch('http://localhost:8001/dashboard');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Update stats
                document.getElementById('total-players').textContent = data.total_players;
                document.getElementById('active-players').textContent = data.active_players;
                document.getElementById('total-teams').textContent = data.total_teams;
                
                // Update last updated
                const lastUpdated = new Date(data.last_updated).toLocaleString();
                document.getElementById('last-updated').textContent = lastUpdated;
                
                // Update players grid
                const playersGrid = document.getElementById('players-grid');
                playersGrid.innerHTML = '';
                
                data.sample_players.forEach(player => {
                    const playerCard = document.createElement('div');
                    playerCard.className = 'player-card';
                    
                    const breakeven = player.current_breakeven;
                    const breakevenClass = breakeven < 0 ? 'negative' : 'positive';
                    const breakevenText = breakeven < 0 ? `BE ${breakeven}` : `BE +${breakeven}`;
                    
                    playerCard.innerHTML = `
                        <div class="player-name">${player.name}</div>
                        <div class="player-team">${player.team}</div>
                        <div class="player-stats">
                            <span>Position: ${player.position}</span>
                            <span class="breakeven ${breakevenClass}">${breakevenText}</span>
                        </div>
                    `;
                    
                    playersGrid.appendChild(playerCard);
                });
                
                loading.style.display = 'none';
                content.style.display = 'block';
                
            } catch (err) {
                console.error('Error loading dashboard:', err);
                loading.style.display = 'none';
                error.style.display = 'block';
                error.innerHTML = `❌ Error loading data: ${err.message}<br>Make sure the API server is running on port 8001`;
            }
        }

        // Load dashboard on page load
        document.addEventListener('DOMContentLoaded', loadDashboard);
    </script>
</body>
</html>
