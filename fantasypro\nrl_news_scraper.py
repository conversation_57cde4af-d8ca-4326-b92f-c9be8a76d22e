#!/usr/bin/env python3
"""
NRL News Scraper
Scrapes trending news from NRL.com with 24-hour caching
"""

import requests
from bs4 import BeautifulSoup
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import os
from pathlib import Path
import re

logger = logging.getLogger(__name__)

class NRLNewsScraper:
    """Scraper for NRL.com trending news"""
    
    def __init__(self, cache_hours: int = 24):
        self.cache_hours = cache_hours
        self.cache_file = Path("data/nrl_news_cache.json")
        self.cache_file.parent.mkdir(exist_ok=True)
        
        # Headers to mimic a real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
    
    def get_trending_news(self, limit: int = 10) -> List[Dict]:
        """Get trending NRL news with caching"""
        try:
            # Check cache first
            cached_data = self._load_cache()
            if cached_data and self._is_cache_fresh(cached_data):
                logger.info("Using cached NRL news data")
                return cached_data['news'][:limit]
            
            # Scrape fresh data
            logger.info("Scraping fresh NRL news data")
            news_data = self._scrape_nrl_news()
            
            if news_data:
                # Cache the data
                cache_data = {
                    'news': news_data,
                    'last_updated': datetime.now().isoformat(),
                    'source': 'nrl.com'
                }
                self._save_cache(cache_data)
                return news_data[:limit]
            else:
                # Return cached data if scraping fails
                if cached_data:
                    logger.warning("Scraping failed, using cached data")
                    return cached_data['news'][:limit]
                else:
                    return self._get_fallback_news()
                    
        except Exception as e:
            logger.error(f"Error getting trending news: {e}")
            return self._get_fallback_news()
    
    def _scrape_nrl_news(self) -> List[Dict]:
        """Scrape news from NRL.com"""
        try:
            # Try multiple NRL news URLs
            urls = [
                'https://www.nrl.com/news/',
                'https://www.nrl.com/news/latest/',
                'https://www.nrl.com/'
            ]
            
            for url in urls:
                try:
                    response = requests.get(url, headers=self.headers, timeout=10)
                    response.raise_for_status()
                    
                    soup = BeautifulSoup(response.content, 'html.parser')
                    news_items = self._extract_news_items(soup, url)
                    
                    if news_items:
                        logger.info(f"Successfully scraped {len(news_items)} news items from {url}")
                        return news_items
                        
                except Exception as e:
                    logger.warning(f"Failed to scrape {url}: {e}")
                    continue
            
            return []
            
        except Exception as e:
            logger.error(f"Error scraping NRL news: {e}")
            return []
    
    def _extract_news_items(self, soup: BeautifulSoup, base_url: str) -> List[Dict]:
        """Extract news items from BeautifulSoup object"""
        news_items = []
        
        try:
            # Try different selectors for news articles
            selectors = [
                'article',
                '.news-item',
                '.article-card',
                '.story-card',
                '.news-card',
                '[data-testid="article"]',
                '.content-card'
            ]
            
            for selector in selectors:
                articles = soup.select(selector)
                if articles:
                    logger.info(f"Found {len(articles)} articles with selector: {selector}")
                    break
            
            if not articles:
                # Fallback: look for any links that might be news
                articles = soup.find_all('a', href=True)
                articles = [a for a in articles if any(keyword in a.get('href', '').lower() 
                           for keyword in ['news', 'article', 'story'])][:10]
            
            for article in articles[:15]:  # Limit to prevent too much processing
                try:
                    news_item = self._extract_single_news_item(article, base_url)
                    if news_item:
                        news_items.append(news_item)
                        
                except Exception as e:
                    logger.debug(f"Error extracting news item: {e}")
                    continue
            
            return news_items[:10]  # Return top 10
            
        except Exception as e:
            logger.error(f"Error extracting news items: {e}")
            return []
    
    def _extract_single_news_item(self, article, base_url: str) -> Optional[Dict]:
        """Extract a single news item"""
        try:
            # Try to find title
            title = None
            title_selectors = ['h1', 'h2', 'h3', '.title', '.headline', '[data-testid="headline"]']
            
            for selector in title_selectors:
                title_elem = article.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    break
            
            # If no title found in selectors, try the article text or href
            if not title:
                if hasattr(article, 'get_text'):
                    title = article.get_text(strip=True)[:100]
                elif article.get('href'):
                    title = article.get('href').split('/')[-1].replace('-', ' ').title()
            
            if not title or len(title) < 10:
                return None
            
            # Try to find URL
            url = None
            if article.name == 'a':
                url = article.get('href')
            else:
                link = article.find('a', href=True)
                if link:
                    url = link.get('href')
            
            # Make URL absolute
            if url and not url.startswith('http'):
                if url.startswith('/'):
                    url = 'https://www.nrl.com' + url
                else:
                    url = 'https://www.nrl.com/' + url
            
            # Try to find time
            time_ago = self._extract_time_info(article)
            
            return {
                'title': title[:150],  # Limit title length
                'url': url,
                'time_ago': time_ago,
                'source': 'NRL.com'
            }
            
        except Exception as e:
            logger.debug(f"Error extracting single news item: {e}")
            return None
    
    def _extract_time_info(self, article) -> str:
        """Extract time information from article"""
        try:
            # Look for time indicators
            time_selectors = [
                'time', '.time', '.date', '.published', 
                '[datetime]', '.timestamp', '.ago'
            ]
            
            for selector in time_selectors:
                time_elem = article.select_one(selector)
                if time_elem:
                    time_text = time_elem.get_text(strip=True)
                    if time_text:
                        return time_text
            
            # Look for text patterns that indicate time
            text = article.get_text()
            time_patterns = ['hours ago', 'minutes ago', 'days ago', 'yesterday', 'today']
            for pattern in time_patterns:
                if pattern in text.lower():
                    # Extract the time phrase
                    words = text.split()
                    for i, word in enumerate(words):
                        if pattern.split()[0] in word.lower():
                            if i > 0:
                                return f"{words[i-1]} {pattern}"
            
            return "Recently"
            
        except Exception:
            return "Recently"
    
    def _load_cache(self) -> Optional[Dict]:
        """Load cached news data"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading cache: {e}")
        return None
    
    def _save_cache(self, data: Dict):
        """Save news data to cache"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving cache: {e}")
    
    def _is_cache_fresh(self, cached_data: Dict) -> bool:
        """Check if cached data is still fresh"""
        try:
            last_updated = datetime.fromisoformat(cached_data['last_updated'])
            return datetime.now() - last_updated < timedelta(hours=self.cache_hours)
        except Exception:
            return False
    
    def _get_fallback_news(self) -> List[Dict]:
        """Get fallback news data when scraping fails"""
        return [
            {
                'title': 'Panthers maintain top spot with commanding victory',
                'url': 'https://www.nrl.com',
                'time_ago': '2 hours ago',
                'source': 'NRL.com'
            },
            {
                'title': 'Origin stars return to club duties ahead of finals',
                'url': 'https://www.nrl.com',
                'time_ago': '4 hours ago',
                'source': 'NRL.com'
            },
            {
                'title': 'Trade period heating up with major signings',
                'url': 'https://www.nrl.com',
                'time_ago': '6 hours ago',
                'source': 'NRL.com'
            },
            {
                'title': 'Injury update: Key players set for return',
                'url': 'https://www.nrl.com',
                'time_ago': '8 hours ago',
                'source': 'NRL.com'
            }
        ]

def main():
    """Test the NRL news scraper"""
    print("🏈 Testing NRL News Scraper")
    
    scraper = NRLNewsScraper()
    news = scraper.get_trending_news(5)
    
    print(f"Found {len(news)} news items:")
    for i, item in enumerate(news, 1):
        print(f"{i}. {item['title']}")
        print(f"   {item['time_ago']} • {item['source']}")
        print()

if __name__ == "__main__":
    main()
