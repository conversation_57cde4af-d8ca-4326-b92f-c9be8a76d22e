#!/usr/bin/env python3
"""
Data Cache System for FantasyPro
Stores SportRadar data locally for fast access and reduced API dependency
"""

import json
import sqlite3
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from sportradar_client import SportRadarNRLClient

logger = logging.getLogger(__name__)

class FantasyProDataCache:
    def __init__(self, cache_dir: str = "data", db_file: str = "fantasypro_cache.db"):
        self.cache_dir = cache_dir
        self.db_file = os.path.join(cache_dir, db_file)
        self.json_players_file = os.path.join(cache_dir, "nrl_players.json")
        self.json_teams_file = os.path.join(cache_dir, "nrl_teams.json")
        
        # Ensure cache directory exists
        os.makedirs(cache_dir, exist_ok=True)
        
        # Initialize database
        self.init_database()
        
    def init_database(self):
        """Initialize SQLite database for caching"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            
            # Create teams table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS teams (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    abbreviation TEXT,
                    city TEXT,
                    venue TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create players table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS players (
                    id INTEGER PRIMARY KEY,
                    sportradar_id TEXT UNIQUE,
                    name TEXT NOT NULL,
                    team_id TEXT,
                    team_name TEXT,
                    position TEXT,
                    jersey_number INTEGER,
                    height INTEGER,
                    weight INTEGER,
                    age INTEGER,
                    date_of_birth TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (team_id) REFERENCES teams (id)
                )
            ''')
            
            # Create cache metadata table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cache_metadata (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            logger.info("Database initialized successfully")

    def is_cache_fresh(self, max_age_hours: int = 24) -> bool:
        """Check if cached data is still fresh"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT last_updated FROM cache_metadata 
                    WHERE key = 'last_full_update'
                ''')
                result = cursor.fetchone()
                
                if not result:
                    return False
                
                last_update = datetime.fromisoformat(result[0])
                age = datetime.now() - last_update
                
                return age < timedelta(hours=max_age_hours)
        except Exception as e:
            logger.error(f"Error checking cache freshness: {e}")
            return False

    def update_from_sportradar(self, api_key: str) -> Dict:
        """Update cache with fresh data from SportRadar"""
        logger.info("Updating cache from SportRadar...")
        
        try:
            client = SportRadarNRLClient(api_key)
            
            # Get all teams and players
            all_players = client.get_all_players()
            teams = client.get_team_list()
            
            # Store in database
            self.store_teams(teams)
            self.store_players(all_players)
            
            # Store in JSON files for backup
            self.save_json_backup(all_players, teams)
            
            # Update metadata
            self.update_cache_metadata('last_full_update', datetime.now().isoformat())
            self.update_cache_metadata('total_players', str(len(all_players)))
            self.update_cache_metadata('total_teams', str(len(teams)))
            
            result = {
                'status': 'success',
                'players_count': len(all_players),
                'teams_count': len(teams),
                'last_updated': datetime.now().isoformat()
            }
            
            logger.info(f"Cache updated: {len(all_players)} players, {len(teams)} teams")
            return result
            
        except Exception as e:
            logger.error(f"Error updating cache from SportRadar: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'last_updated': datetime.now().isoformat()
            }

    def store_teams(self, teams: List[Dict]):
        """Store teams in database"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            
            # Clear existing teams
            cursor.execute('DELETE FROM teams')
            
            # Insert new teams
            for team in teams:
                cursor.execute('''
                    INSERT INTO teams (id, name, abbreviation, city, venue)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    team.get('id'),
                    team.get('name'),
                    team.get('abbreviation'),
                    team.get('city'),
                    team.get('venue')
                ))
            
            conn.commit()
            logger.info(f"Stored {len(teams)} teams in database")

    def store_players(self, players: List[Dict]):
        """Store players in database"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            
            # Clear existing players
            cursor.execute('DELETE FROM players')
            
            # Insert new players
            for player in players:
                cursor.execute('''
                    INSERT INTO players (
                        sportradar_id, name, team_id, team_name, position,
                        jersey_number, height, weight, age, date_of_birth
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    player.get('sportradar_id'),
                    player.get('name'),
                    player.get('team_id'),
                    player.get('team'),
                    player.get('position'),
                    player.get('jersey_number'),
                    player.get('height'),
                    player.get('weight'),
                    player.get('age'),
                    player.get('date_of_birth')
                ))
            
            conn.commit()
            logger.info(f"Stored {len(players)} players in database")

    def save_json_backup(self, players: List[Dict], teams: List[Dict]):
        """Save data as JSON backup files"""
        try:
            with open(self.json_players_file, 'w', encoding='utf-8') as f:
                json.dump(players, f, indent=2, ensure_ascii=False)
            
            with open(self.json_teams_file, 'w', encoding='utf-8') as f:
                json.dump(teams, f, indent=2, ensure_ascii=False)
            
            logger.info("JSON backup files saved")
        except Exception as e:
            logger.error(f"Error saving JSON backup: {e}")

    def get_all_players(self) -> List[Dict]:
        """Get all players from cache"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, sportradar_id, name, team_id, team_name, position,
                           jersey_number, height, weight, age, date_of_birth
                    FROM players
                    ORDER BY team_name, name
                ''')
                
                players = []
                for row in cursor.fetchall():
                    players.append({
                        'id': row[0],
                        'sportradar_id': row[1],
                        'name': row[2],
                        'team_id': row[3],
                        'team': row[4],
                        'position': row[5],
                        'jersey_number': row[6],
                        'height': row[7],
                        'weight': row[8],
                        'age': row[9],
                        'date_of_birth': row[10],
                        'source': 'cache'
                    })
                
                return players
        except Exception as e:
            logger.error(f"Error getting players from cache: {e}")
            return []

    def get_all_teams(self) -> List[Dict]:
        """Get all teams from cache"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, name, abbreviation, city, venue
                    FROM teams
                    ORDER BY name
                ''')
                
                teams = []
                for row in cursor.fetchall():
                    teams.append({
                        'id': row[0],
                        'name': row[1],
                        'abbreviation': row[2],
                        'city': row[3],
                        'venue': row[4],
                        'source': 'cache'
                    })
                
                return teams
        except Exception as e:
            logger.error(f"Error getting teams from cache: {e}")
            return []

    def search_players(self, query: str, limit: int = 20) -> List[Dict]:
        """Search players by name"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, sportradar_id, name, team_id, team_name, position,
                           jersey_number, height, weight, age, date_of_birth
                    FROM players
                    WHERE name LIKE ? OR team_name LIKE ?
                    ORDER BY name
                    LIMIT ?
                ''', (f'%{query}%', f'%{query}%', limit))
                
                players = []
                for row in cursor.fetchall():
                    players.append({
                        'id': row[0],
                        'sportradar_id': row[1],
                        'name': row[2],
                        'team_id': row[3],
                        'team': row[4],
                        'position': row[5],
                        'jersey_number': row[6],
                        'height': row[7],
                        'weight': row[8],
                        'age': row[9],
                        'date_of_birth': row[10],
                        'source': 'cache'
                    })
                
                return players
        except Exception as e:
            logger.error(f"Error searching players: {e}")
            return []

    def get_team_players(self, team_id: str) -> List[Dict]:
        """Get players from a specific team"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, sportradar_id, name, team_id, team_name, position,
                           jersey_number, height, weight, age, date_of_birth
                    FROM players
                    WHERE team_id = ?
                    ORDER BY jersey_number, name
                ''', (team_id,))
                
                players = []
                for row in cursor.fetchall():
                    players.append({
                        'id': row[0],
                        'sportradar_id': row[1],
                        'name': row[2],
                        'team_id': row[3],
                        'team': row[4],
                        'position': row[5],
                        'jersey_number': row[6],
                        'height': row[7],
                        'weight': row[8],
                        'age': row[9],
                        'date_of_birth': row[10],
                        'source': 'cache'
                    })
                
                return players
        except Exception as e:
            logger.error(f"Error getting team players: {e}")
            return []

    def update_cache_metadata(self, key: str, value: str):
        """Update cache metadata"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO cache_metadata (key, value, last_updated)
                VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (key, value))
            conn.commit()

    def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                
                # Get player count
                cursor.execute('SELECT COUNT(*) FROM players')
                player_count = cursor.fetchone()[0]
                
                # Get team count
                cursor.execute('SELECT COUNT(*) FROM teams')
                team_count = cursor.fetchone()[0]
                
                # Get last update
                cursor.execute('''
                    SELECT value FROM cache_metadata 
                    WHERE key = 'last_full_update'
                ''')
                last_update = cursor.fetchone()
                last_update = last_update[0] if last_update else None
                
                return {
                    'players_count': player_count,
                    'teams_count': team_count,
                    'last_updated': last_update,
                    'cache_fresh': self.is_cache_fresh(),
                    'db_file': self.db_file,
                    'json_backup_exists': os.path.exists(self.json_players_file)
                }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {
                'error': str(e)
            }

def main():
    """Test the cache system"""
    cache = FantasyProDataCache()
    
    # Check cache status
    stats = cache.get_cache_stats()
    print(f"Cache stats: {stats}")
    
    # If cache is empty or stale, update from SportRadar
    if not cache.is_cache_fresh() or stats.get('players_count', 0) == 0:
        print("Updating cache from SportRadar...")
        api_key = "aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN"
        result = cache.update_from_sportradar(api_key)
        print(f"Update result: {result}")
    
    # Test queries
    players = cache.get_all_players()
    print(f"Total players in cache: {len(players)}")
    
    teams = cache.get_all_teams()
    print(f"Total teams in cache: {len(teams)}")
    
    # Test search
    search_results = cache.search_players("Tedesco", 5)
    print(f"Search results for 'Tedesco': {len(search_results)}")
    for player in search_results:
        print(f"  - {player['name']} ({player['team']})")

if __name__ == "__main__":
    main()
