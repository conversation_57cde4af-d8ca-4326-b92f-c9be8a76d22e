"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/globals.css":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/globals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_1_node_modules_next_dist_build_webpack_loaders_postcss_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_2_themes_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! -!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./themes.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/themes.css\");\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n___CSS_LOADER_EXPORT___.i(_node_modules_next_dist_build_webpack_loaders_css_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_1_node_modules_next_dist_build_webpack_loaders_postcss_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_2_themes_css__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */ /* 3 */\\n  tab-size: 4; /* 3 */\\n  font-family: Inter, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: JetBrains Mono, Menlo, Monaco, monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\\n  appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n\\ninput::placeholder,textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\n::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n\\n::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n\\n::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n\\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n\\nselect {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  print-color-adjust: exact;\\n}\\n\\n[multiple],[size]:where(select:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  print-color-adjust: unset;\\n}\\n\\n[type='checkbox'],[type='radio'] {\\n  appearance: none;\\n  padding: 0;\\n  print-color-adjust: exact;\\n  display: inline-block;\\n  vertical-align: middle;\\n  background-origin: border-box;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  flex-shrink: 0;\\n  height: 1rem;\\n  width: 1rem;\\n  color: #2563eb;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='checkbox'] {\\n  border-radius: 0px;\\n}\\n\\n[type='radio'] {\\n  border-radius: 100%;\\n}\\n\\n[type='checkbox']:focus,[type='radio']:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n}\\n\\n[type='checkbox']:checked,[type='radio']:checked {\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n[type='checkbox']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:checked {\\n    appearance: auto;\\n  }\\n}\\n\\n[type='radio']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='radio']:checked {\\n    appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='checkbox']:indeterminate {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e\\\");\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:indeterminate {\\n    appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='file'] {\\n  background: unset;\\n  border-color: inherit;\\n  border-width: 0;\\n  border-radius: 0;\\n  padding: 0;\\n  font-size: unset;\\n  line-height: inherit;\\n}\\n\\n[type='file']:focus {\\n  outline: 1px solid ButtonText;\\n  outline: 1px auto -webkit-focus-ring-color;\\n}\\n  html {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n    background-color: var(--bg-primary);\\n    color: var(--text-primary);\\n    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';\\n    transition: var(--theme-transition);\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n\\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 6px;\\n    height: 6px;\\n  }\\n\\n  ::-webkit-scrollbar-track {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n}\\n\\n  ::-webkit-scrollbar-thumb {\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n}\\n\\n  ::-webkit-scrollbar-thumb:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));\\n}\\n\\n  /* Firefox scrollbar */\\n  * {\\n    scrollbar-width: thin;\\n    scrollbar-color: #475569 #1e293b;\\n  }\\n.container {\\n  width: 100%;\\n}\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .container {\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\n/* Enhanced Card components with theme-aware immersive effects */\\n.card {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transition: var(--theme-transition);\\n  }\\n.card-hover {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n.card-hover:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n/* Immersive card with glow effect */\\n.card-glow {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n.card-glow:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n.card-glow {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.card-glow::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\\n    transition: left 0.5s;\\n  }\\n.card-glow:hover::before {\\n    left: 100%;\\n  }\\n/* Premium card with enhanced theme-aware effects */\\n.card-premium {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n.card-premium:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n.card-premium {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.card-premium::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\\n    transition: left 0.5s;\\n  }\\n.card-premium:hover::before {\\n    left: 100%;\\n  }\\n.card-premium {\\n  will-change: transform, box-shadow;\\n    background: var(--bg-glass-strong);\\n    backdrop-filter: blur(15px);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n}\\n.card-premium:hover {\\n    background: var(--bg-glass-strong);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n/* Enhanced Button variants with immersive effects */\\n.btn-primary {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-primary {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  }\\n.btn-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.3);\\n}\\n.btn-primary:active {\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-secondary {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-secondary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-secondary {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-secondary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 15px rgba(71, 85, 105, 0.3);\\n}\\n.btn-outline {\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\\n}\\n.btn-outline:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-outline {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    background: transparent;\\n  }\\n.btn-outline:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 0 15px rgba(34, 197, 94, 0.2);\\n}\\n.btn-danger {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-danger:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-danger {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-danger:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(239, 68, 68, 0.3);\\n}\\n/* Button ripple effect */\\n.btn-ripple {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.btn-ripple::before {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 50%;\\n    width: 0;\\n    height: 0;\\n    border-radius: 50%;\\n    background: rgba(255, 255, 255, 0.3);\\n    transform: translate(-50%, -50%);\\n    transition: width 0.3s, height 0.3s;\\n  }\\n.btn-ripple:active::before {\\n    width: 300px;\\n    height: 300px;\\n  }\\n/* Enhanced Input styles with immersive effects */\\n.input-primary {\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.input-primary::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));\\n}\\n.input-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.input-primary {\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n  }\\n.input-primary:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.1);\\n    transform: translateY(-1px);\\n}\\n.input-primary:hover:not(:focus) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n}\\n/* Floating label input */\\n/* Enhanced Status indicators with glow effects */\\n.status-warning {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  border-width: 1px;\\n  border-color: rgb(234 179 8 / 0.2);\\n  background-color: rgb(113 63 18 / 0.2);\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n    position: relative;\\n    overflow: hidden;\\n}\\n/* Live indicator with pulse */\\n.status-live {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  border-width: 1px;\\n  border-color: rgb(34 197 94 / 0.2);\\n  background-color: rgb(20 83 45 / 0.2);\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n    position: relative;\\n    overflow: hidden;\\n}\\n.status-live::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background: rgba(34, 197, 94, 0.1);\\n    border-radius: inherit;\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n.status-live::after {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 0.5rem;\\n    width: 6px;\\n    height: 6px;\\n    background: rgb(34, 197, 94);\\n    border-radius: 50%;\\n    transform: translateY(-50%);\\n    animation: pulse 1.5s ease-in-out infinite;\\n  }\\n/* Premium status indicator */\\n.status-premium {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(79, 70, 229, 0.2));\\n    color: rgb(196, 181, 253);\\n    border: 1px solid rgba(147, 51, 234, 0.3);\\n    box-shadow: 0 0 15px rgba(147, 51, 234, 0.2);\\n}\\n/* Risk level indicators */\\n/* Loading states */\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.loading-spinner {\\n  animation: spin 1s linear infinite;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\\n}\\n/* Data visualization */\\n/* Navigation */\\n.nav-link-active {\\n  display: flex;\\n  align-items: center;\\n  border-radius: 0.5rem;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  border-width: 1px;\\n  border-color: rgb(34 197 94 / 0.2);\\n  background-color: rgb(20 83 45 / 0.2);\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-inactive {\\n  display: flex;\\n  align-items: center;\\n  border-radius: 0.5rem;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  --tw-text-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-inactive:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n/* Enhanced Tables with immersive effects */\\n/* Interactive table row with glow */\\n/* Theme-aware text classes */\\n.themed-text-primary {\\n    color: var(--text-primary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-text-secondary {\\n    color: var(--text-secondary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-text-tertiary {\\n    color: var(--text-tertiary);\\n    transition: var(--theme-transition);\\n  }\\n/* Theme-aware border classes */\\n.themed-border {\\n    border-color: var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-border-b {\\n    border-bottom: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-border-t {\\n    border-top: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n/* Theme-aware card classes */\\n.themed-card-secondary {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-card-secondary:hover {\\n    background-color: var(--bg-tertiary);\\n    border-color: var(--border-primary);\\n  }\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\n.static {\\n  position: static;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.sticky {\\n  position: sticky;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.left-0 {\\n  left: 0px;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.right-8 {\\n  right: 2rem;\\n}\\n.top-0 {\\n  top: 0px;\\n}\\n.z-10 {\\n  z-index: 10;\\n}\\n.z-20 {\\n  z-index: 20;\\n}\\n.z-40 {\\n  z-index: 40;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.-m-2\\\\.5 {\\n  margin: -0.625rem;\\n}\\n.-mx-2 {\\n  margin-left: -0.5rem;\\n  margin-right: -0.5rem;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.ml-auto {\\n  margin-left: auto;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.mt-auto {\\n  margin-top: auto;\\n}\\n.line-clamp-2 {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 2;\\n}\\n.block {\\n  display: block;\\n}\\n.inline-block {\\n  display: inline-block;\\n}\\n.inline {\\n  display: inline;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.table {\\n  display: table;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.h-10 {\\n  height: 2.5rem;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-16 {\\n  height: 4rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-3 {\\n  height: 0.75rem;\\n}\\n.h-32 {\\n  height: 8rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-64 {\\n  height: 16rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.max-h-32 {\\n  max-height: 8rem;\\n}\\n.max-h-40 {\\n  max-height: 10rem;\\n}\\n.max-h-64 {\\n  max-height: 16rem;\\n}\\n.max-h-80 {\\n  max-height: 20rem;\\n}\\n.max-h-96 {\\n  max-height: 24rem;\\n}\\n.max-h-\\\\[90vh\\\\] {\\n  max-height: 90vh;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-10 {\\n  width: 2.5rem;\\n}\\n.w-11 {\\n  width: 2.75rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-16 {\\n  width: 4rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-24 {\\n  width: 6rem;\\n}\\n.w-3 {\\n  width: 0.75rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-48 {\\n  width: 12rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.w-px {\\n  width: 1px;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.shrink-0 {\\n  flex-shrink: 0;\\n}\\n.grow {\\n  flex-grow: 1;\\n}\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.resize {\\n  resize: both;\\n}\\n.list-inside {\\n  list-style-position: inside;\\n}\\n.list-disc {\\n  list-style-type: disc;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-end {\\n  align-items: flex-end;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.gap-8 {\\n  gap: 2rem;\\n}\\n.gap-x-4 {\\n  column-gap: 1rem;\\n}\\n.gap-y-5 {\\n  row-gap: 1.25rem;\\n}\\n.gap-y-7 {\\n  row-gap: 1.75rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.self-start {\\n  align-self: flex-start;\\n}\\n.self-stretch {\\n  align-self: stretch;\\n}\\n.overflow-auto {\\n  overflow: auto;\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.whitespace-pre-wrap {\\n  white-space: pre-wrap;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.rounded-t {\\n  border-top-left-radius: 0.25rem;\\n  border-top-right-radius: 0.25rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\n.border-l-4 {\\n  border-left-width: 4px;\\n}\\n.border-r {\\n  border-right-width: 1px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-blue-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-500\\\\/20 {\\n  border-color: rgb(59 130 246 / 0.2);\\n}\\n.border-blue-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-600\\\\/30 {\\n  border-color: rgb(37 99 235 / 0.3);\\n}\\n.border-green-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\\n}\\n.border-green-500\\\\/20 {\\n  border-color: rgb(34 197 94 / 0.2);\\n}\\n.border-orange-500\\\\/20 {\\n  border-color: rgb(249 115 22 / 0.2);\\n}\\n.border-purple-500\\\\/20 {\\n  border-color: rgb(168 85 247 / 0.2);\\n}\\n.border-red-500\\\\/20 {\\n  border-color: rgb(239 68 68 / 0.2);\\n}\\n.border-red-500\\\\/30 {\\n  border-color: rgb(239 68 68 / 0.3);\\n}\\n.border-slate-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n}\\n.border-slate-700 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-500\\\\/20 {\\n  border-color: rgb(234 179 8 / 0.2);\\n}\\n.border-yellow-500\\\\/30 {\\n  border-color: rgb(234 179 8 / 0.3);\\n}\\n.border-gray-500\\\\/30 {\\n  border-color: rgb(107 114 128 / 0.3);\\n}\\n.border-green-500\\\\/30 {\\n  border-color: rgb(34 197 94 / 0.3);\\n}\\n.border-t-transparent {\\n  border-top-color: transparent;\\n}\\n.bg-black\\\\/50 {\\n  background-color: rgb(0 0 0 / 0.5);\\n}\\n.bg-blue-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-400\\\\/10 {\\n  background-color: rgb(96 165 250 / 0.1);\\n}\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500\\\\/10 {\\n  background-color: rgb(59 130 246 / 0.1);\\n}\\n.bg-blue-500\\\\/20 {\\n  background-color: rgb(59 130 246 / 0.2);\\n}\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-600\\\\/20 {\\n  background-color: rgb(37 99 235 / 0.2);\\n}\\n.bg-blue-900\\\\/20 {\\n  background-color: rgb(30 58 138 / 0.2);\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500\\\\/20 {\\n  background-color: rgb(107 114 128 / 0.2);\\n}\\n.bg-gray-900\\\\/20 {\\n  background-color: rgb(17 24 39 / 0.2);\\n}\\n.bg-green-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-400\\\\/10 {\\n  background-color: rgb(74 222 128 / 0.1);\\n}\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500\\\\/10 {\\n  background-color: rgb(34 197 94 / 0.1);\\n}\\n.bg-green-500\\\\/20 {\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\n.bg-green-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-600\\\\/20 {\\n  background-color: rgb(22 163 74 / 0.2);\\n}\\n.bg-green-900\\\\/20 {\\n  background-color: rgb(20 83 45 / 0.2);\\n}\\n.bg-orange-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(251 146 60 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-500\\\\/10 {\\n  background-color: rgb(249 115 22 / 0.1);\\n}\\n.bg-orange-500\\\\/20 {\\n  background-color: rgb(249 115 22 / 0.2);\\n}\\n.bg-orange-600\\\\/20 {\\n  background-color: rgb(234 88 12 / 0.2);\\n}\\n.bg-orange-900\\\\/20 {\\n  background-color: rgb(124 45 18 / 0.2);\\n}\\n.bg-pink-500\\\\/20 {\\n  background-color: rgb(236 72 153 / 0.2);\\n}\\n.bg-purple-500\\\\/10 {\\n  background-color: rgb(168 85 247 / 0.1);\\n}\\n.bg-purple-500\\\\/20 {\\n  background-color: rgb(168 85 247 / 0.2);\\n}\\n.bg-purple-600\\\\/20 {\\n  background-color: rgb(147 51 234 / 0.2);\\n}\\n.bg-red-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-400\\\\/10 {\\n  background-color: rgb(248 113 113 / 0.1);\\n}\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500\\\\/10 {\\n  background-color: rgb(239 68 68 / 0.1);\\n}\\n.bg-red-500\\\\/20 {\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\n.bg-red-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-600\\\\/20 {\\n  background-color: rgb(220 38 38 / 0.2);\\n}\\n.bg-red-900\\\\/20 {\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\n.bg-red-900\\\\/40 {\\n  background-color: rgb(127 29 29 / 0.4);\\n}\\n.bg-slate-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-600\\\\/20 {\\n  background-color: rgb(71 85 105 / 0.2);\\n}\\n.bg-slate-700 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-700\\\\/30 {\\n  background-color: rgb(51 65 85 / 0.3);\\n}\\n.bg-slate-800 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-800\\\\/50 {\\n  background-color: rgb(30 41 59 / 0.5);\\n}\\n.bg-slate-900 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-900\\\\/20 {\\n  background-color: rgb(15 23 42 / 0.2);\\n}\\n.bg-yellow-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-400\\\\/10 {\\n  background-color: rgb(250 204 21 / 0.1);\\n}\\n.bg-yellow-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500\\\\/10 {\\n  background-color: rgb(234 179 8 / 0.1);\\n}\\n.bg-yellow-500\\\\/20 {\\n  background-color: rgb(234 179 8 / 0.2);\\n}\\n.bg-yellow-900\\\\/20 {\\n  background-color: rgb(113 63 18 / 0.2);\\n}\\n.bg-gray-500\\\\/10 {\\n  background-color: rgb(107 114 128 / 0.1);\\n}\\n.bg-gray-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-yellow-500\\\\/20 {\\n  --tw-gradient-from: rgb(234 179 8 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-orange-500\\\\/20 {\\n  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-2\\\\.5 {\\n  padding: 0.625rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-5 {\\n  padding: 1.25rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-5 {\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-2\\\\.5 {\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\n.pl-3 {\\n  padding-left: 0.75rem;\\n}\\n.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-right {\\n  text-align: right;\\n}\\n.font-mono {\\n  font-family: JetBrains Mono, Menlo, Monaco, monospace;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-green-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\\n}\\n.text-pink-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(244 114 182 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\\n}\\n.text-red-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\\n}\\n.text-red-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.opacity-0 {\\n  opacity: 0;\\n}\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\n.shadow-glow-blue {\\n  --tw-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-glow-orange {\\n  --tw-shadow: 0 0 20px rgba(251, 191, 36, 0.3);\\n  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-150 {\\n  transition-duration: 150ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n/* Glass morphism effect */\\n/* Gradient text */\\n/* Enhanced Custom shadows and glow effects */\\n.shadow-glow-green {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3),\\n                0 0 40px rgba(34, 197, 94, 0.1);\\n  }\\n.shadow-glow-blue {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),\\n                0 0 40px rgba(59, 130, 246, 0.1);\\n  }\\n.shadow-glow-orange {\\n    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3),\\n                0 0 40px rgba(251, 191, 36, 0.1);\\n  }\\n.shadow-glow-red {\\n    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3),\\n                0 0 40px rgba(239, 68, 68, 0.1);\\n  }\\n.shadow-glow-purple {\\n    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3),\\n                0 0 40px rgba(147, 51, 234, 0.1);\\n  }\\n/* Pulsing glow effect */\\n.glow-pulse-green {\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n.glow-pulse-blue {\\n    animation: glowPulseBlue 2s ease-in-out infinite alternate;\\n  }\\n/* Floating effect */\\n.float {\\n    animation: float 3s ease-in-out infinite;\\n  }\\n.float-delayed {\\n    animation: float 3s ease-in-out infinite;\\n    animation-delay: 1s;\\n  }\\n/* Shimmer effect */\\n.shimmer {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.shimmer::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\\n    animation: shimmer 2s infinite;\\n  }\\n/* Magnetic hover effect */\\n.magnetic {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n.magnetic:hover {\\n    transform: scale(1.02);\\n  }\\n/* Tilt effect */\\n.tilt {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n.tilt:hover {\\n    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);\\n  }\\n/* Enhanced Animation utilities */\\n.animate-slide-in-left {\\n    animation: slideInLeft 0.4s ease-out;\\n  }\\n.animate-slide-in-right {\\n    animation: slideInRight 0.4s ease-out;\\n  }\\n/* Staggered animations */\\n.animate-stagger-1 { animation-delay: 0.1s; }\\n.animate-stagger-2 { animation-delay: 0.2s; }\\n.animate-stagger-3 { animation-delay: 0.3s; }\\n/* Hide scrollbar but keep functionality */\\n/* Custom focus styles */\\n\\n/* Import theme system */\\n\\n/* Import Inter font */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');\\n\\n/* Base styles */\\n\\n/* Component styles */\\n\\n/* Utility styles */\\n\\n/* Enhanced Custom animations */\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideDown {\\n  from {\\n    transform: translateY(-10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInLeft {\\n  from {\\n    transform: translateX(-20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInRight {\\n  from {\\n    transform: translateX(20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes bounceIn {\\n  0% {\\n    transform: scale(0.3);\\n    opacity: 0;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  70% {\\n    transform: scale(0.9);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes scaleIn {\\n  from {\\n    transform: scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes rotateIn {\\n  from {\\n    transform: rotate(-10deg) scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n\\n@keyframes glowPulse {\\n  0% {\\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);\\n  }\\n}\\n\\n@keyframes glowPulseGreen {\\n  0% {\\n    box-shadow: 0 0 5px rgba(34, 197, 94, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 30px rgba(34, 197, 94, 0.2);\\n  }\\n}\\n\\n@keyframes glowPulseBlue {\\n  0% {\\n    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 30px rgba(59, 130, 246, 0.2);\\n  }\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  body {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n  }\\n}\\n\\\\n\\\\n\\n/* Performance optimizations */\\n* {\\n  /* Use hardware acceleration for transforms */\\n  transform: translateZ(0);\\n  backface-visibility: hidden;\\n  perspective: 1000px;\\n}\\n\\n/* Optimize animations */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Optimize repaints */\\n.card-premium,\\n.card-glow,\\n.magnetic {\\n  will-change: transform, box-shadow;\\n}\\n\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline,\\n.btn-danger {\\n  will-change: transform, box-shadow, background-color;\\n}\\n\\n/* Optimize scrolling */\\n.scrollable {\\n  -webkit-overflow-scrolling: touch;\\n  overflow-scrolling: touch;\\n}\\n\\n/* Optimize text rendering */\\nbody {\\n  text-rendering: optimizeSpeed;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Optimize images */\\nimg {\\n  content-visibility: auto;\\n  contain-intrinsic-size: 300px 200px;\\n}\\n\\n/* Critical CSS inlining */\\n.above-fold {\\n  contain: layout style paint;\\n}\\n.hover\\\\:themed-text-primary:hover {\\n    color: var(--text-primary);\\n    transition: var(--theme-transition);\\n  }\\n.after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\n.after\\\\:left-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 2px;\\n}\\n.after\\\\:top-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  top: 2px;\\n}\\n.after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\n.after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\n.after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\n.after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\n.after\\\\:border-gray-300::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\n.last\\\\:border-b-0:last-child {\\n  border-bottom-width: 0px;\\n}\\n.hover\\\\:border-slate-500:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:bg-blue-600\\\\/30:hover {\\n  background-color: rgb(37 99 235 / 0.3);\\n}\\n.hover\\\\:bg-green-600\\\\/30:hover {\\n  background-color: rgb(22 163 74 / 0.3);\\n}\\n.hover\\\\:bg-orange-600\\\\/30:hover {\\n  background-color: rgb(234 88 12 / 0.3);\\n}\\n.hover\\\\:bg-purple-600\\\\/30:hover {\\n  background-color: rgb(147 51 234 / 0.3);\\n}\\n.hover\\\\:bg-red-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-slate-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-slate-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-slate-700\\\\/50:hover {\\n  background-color: rgb(51 65 85 / 0.5);\\n}\\n.hover\\\\:bg-yellow-500\\\\/30:hover {\\n  background-color: rgb(234 179 8 / 0.3);\\n}\\n.hover\\\\:bg-blue-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-gray-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-green-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-opacity-80:hover {\\n  --tw-bg-opacity: 0.8;\\n}\\n.hover\\\\:text-blue-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-blue-400:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-green-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-red-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-yellow-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\\n}\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-green-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\n.peer:checked ~ .peer-checked\\\\:bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.peer:focus ~ .peer-focus\\\\:outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-4 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-blue-300 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));\\n}\\n.dark\\\\:border-gray-600:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.dark\\\\:bg-gray-700:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.peer:focus ~ .dark\\\\:peer-focus\\\\:ring-blue-800:is(.dark *) {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .sm\\\\:justify-between {\\n    justify-content: space-between;\\n  }\\n\\n  .sm\\\\:gap-x-6 {\\n    column-gap: 1.5rem;\\n  }\\n\\n  .sm\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:fixed {\\n    position: fixed;\\n  }\\n\\n  .lg\\\\:inset-y-0 {\\n    top: 0px;\\n    bottom: 0px;\\n  }\\n\\n  .lg\\\\:z-50 {\\n    z-index: 50;\\n  }\\n\\n  .lg\\\\:col-span-1 {\\n    grid-column: span 1 / span 1;\\n  }\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:col-span-3 {\\n    grid-column: span 3 / span 3;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:w-64 {\\n    width: 16rem;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-6 {\\n    grid-template-columns: repeat(6, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:flex-col {\\n    flex-direction: column;\\n  }\\n\\n  .lg\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .lg\\\\:justify-between {\\n    justify-content: space-between;\\n  }\\n\\n  .lg\\\\:gap-x-6 {\\n    column-gap: 1.5rem;\\n  }\\n\\n  .lg\\\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .lg\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n\\n  .lg\\\\:pl-64 {\\n    padding-left: 16rem;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .xl\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc,EAAd,MAAc;EAAd,WAAc,EAAd,MAAc;EAAd,yCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,qDAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,gBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,mBAAc;EAAd,sBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,mBAAc;EAAd,sBAAc;AAAA;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,iFAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,mPAAc;EAAd,wCAAc;EAAd,4BAAc;EAAd,4BAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,wBAAc;EAAd,sBAAc;EAAd;AAAc;;AAAd;EAAA,gBAAc;EAAd,UAAc;EAAd,yBAAc;EAAd,qBAAc;EAAd,sBAAc;EAAd,6BAAc;EAAd,yBAAc;UAAd,iBAAc;EAAd,cAAc;EAAd,YAAc;EAAd,WAAc;EAAd,cAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd;AAAc;;AAAd;EAAA,sQAAc;AAAA;;AAAd;;EAAA;IAAA;EAAc;AAAA;;AAAd;EAAA,oKAAc;AAAA;;AAAd;;EAAA;IAAA;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,uOAAc;EAAd,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,4BAAc;AAAA;;AAAd;;EAAA;IAAA;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,iBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,gBAAc;EAAd,UAAc;EAAd,gBAAc;EAAd;AAAc;;AAAd;EAAA,6BAAc;EAAd;AAAc;EAAd;IAAA,2CAAc;IAAd,uBAAc;EAAA;;EAAd;IAAA,mCAAc;IAAd,0BAAc;IAAd,qDAAc;IAAd,mCAAc;IAAd,mCAAc;IAAd,kCAAc;EAAA;;EAAd,qBAAc;EAAd;IAAA,UAAc;IAAd,WAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,qBAAc;EAAd,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd,sBAAc;EAAd;IAAA,qBAAc;IAAd,gCAAc;EAAA;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAoDlB,gEAAgE;AAChE;IACE,qCAAqC;IACrC,uCAAuC;IACvC,4BAA4B;IAC5B,qBAAiB;IACjB,mCAAmC;EACrC;AAGE;IAAA,qCAAW;IAAX,uCAAW;IAAX,4BAAW;IAAX,qBAAW;IACX,wBAAwB;IACxB,mCAAmC;EAFxB;AAKb;IACE,2BAA2B;IAC3B,iDAAiD;IACjD,kCAAkC;EACpC;AAEA,oCAAoC;AAElC;IAAA,qCAAiB;IAAjB,uCAAiB;IAAjB,4BAAiB;IAAjB,qBAAiB;IAAjB,wBAAiB;IAAjB,mCAAiB;EAAA;AAAjB;IAAA,2BAAiB;IAAjB,iDAAiB;IAAjB,kCAAiB;EAAA;AADnB;IAEE,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,oFAAoF;IACpF,qBAAqB;EACvB;AAEA;IACE,UAAU;EACZ;AAEA,mDAAmD;AAEjD;IAAA,qCAAgB;IAAhB,uCAAgB;IAAhB,4BAAgB;IAAhB,qBAAgB;IAAhB,wBAAgB;IAAhB,mCAAgB;EAAA;AAAhB;IAAA,2BAAgB;IAAhB,iDAAgB;IAAhB,kCAAgB;EAAA;AAAhB;IAAA,kBAAgB;IAAhB,gBAAgB;EAAA;AAAhB;IAAA,WAAgB;IAAhB,kBAAgB;IAAhB,MAAgB;IAAhB,WAAgB;IAAhB,WAAgB;IAAhB,YAAgB;IAAhB,oFAAgB;IAAhB,qBAAgB;EAAA;AAAhB;IAAA,UAAgB;EAAA;AAAhB;EAAA,kCAAgB;IAChB,kCAAkC;IAClC,2BAA2B;IAC3B,yCAAyC;IACzC,mCAAmC;AAJnB;AAOlB;IACE,kCAAkC;IAClC,iDAAiD;IACjD,kCAAkC;EACpC;AAEA,oDAAoD;AAElD;EAAA,qBAAkF;EAAlF,kBAAkF;EAAlF,0DAAkF;EAAlF,kBAAkF;EAAlF,mBAAkF;EAAlF,mBAAkF;EAAlF,sBAAkF;EAAlF,gBAAkF;EAAlF,oBAAkF;EAAlF;AAAkF;AAAlF;EAAA,8BAAkF;EAAlF;AAAkF;AADpF;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,iFAAiF;EACnF;AAGE;EAAA,kBAAmB;EAAnB,0DAAmB;IACnB,2BAA2B;IAC3B;;;AAFmB;AAOrB;IACE,wBAAwB;IACxB,6CAA6C;EAC/C;AAGE;EAAA,qBAAkF;EAAlF,kBAAkF;EAAlF,yDAAkF;EAAlF,kBAAkF;EAAlF,mBAAkF;EAAlF,mBAAkF;EAAlF,sBAAkF;EAAlF,gBAAkF;EAAlF,oBAAkF;EAAlF;AAAkF;AAAlF;EAAA,8BAAkF;EAAlF;AAAkF;AADpF;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,6CAA6C;EAC/C;AAGE;EAAA,kBAAmB;EAAnB,0DAAmB;IACnB,2BAA2B;IAC3B;;;AAFmB;AAQnB;EAAA,qBAAiG;EAAjG,iBAAiG;EAAjG,sBAAiG;EAAjG,0DAAiG;EAAjG,kBAAiG;EAAjG,mBAAiG;EAAjG,mBAAiG;EAAjG,sBAAiG;EAAjG,gBAAiG;EAAjG,oBAAiG;EAAjG;AAAiG;AAAjG;EAAA,8BAAiG;EAAjG;AAAiG;AADnG;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,uBAAuB;EACzB;AAGE;EAAA,sBAA+C;EAA/C,0DAA+C;EAA/C,kBAA+C;EAA/C,yDAA+C;EAA/C,oBAA+C;EAA/C,mDAA+C;IAC/C,2BAA2B;IAC3B;;AAF+C;AAO/C;EAAA,qBAAgF;EAAhF,kBAAgF;EAAhF,0DAAgF;EAAhF,kBAAgF;EAAhF,mBAAgF;EAAhF,mBAAgF;EAAhF,sBAAgF;EAAhF,gBAAgF;EAAhF,oBAAgF;EAAhF;AAAgF;AAAhF;EAAA,8BAAgF;EAAhF;AAAgF;AADlF;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,6CAA6C;EAC/C;AAGE;EAAA,kBAAiB;EAAjB,0DAAiB;IACjB,2BAA2B;IAC3B;;;AAFiB;AAOnB,yBAAyB;AACzB;IACE,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;IACT,kBAAkB;IAClB,oCAAoC;IACpC,gCAAgC;IAChC,mCAAmC;EACrC;AAEA;IACE,YAAY;IACZ,aAAa;EACf;AAEA,iDAAiD;AAE/C;EAAA,qBAAoH;EAApH,iBAAoH;EAApH,sBAAoH;EAApH,0DAAoH;EAApH,kBAAoH;EAApH,yDAAoH;EAApH,qBAAoH;EAApH,sBAAoH;EAApH,mBAAoH;EAApH,sBAAoH;EAApH,oBAAoH;EAApH;AAAoH;AAApH;EAAA,2BAAoH;EAApH;AAAoH;AAApH;EAAA,8BAAoH;EAApH;AAAoH;AADtH;IAEE,iDAAiD;IACjD,kBAAkB;EACpB;AAGE;EAAA,sBAAoC;EAApC,0DAAoC;EAApC,kBAAoC;EAApC,yDAAoC;IACpC;+CAC2C;IAC3C;AAHoC;AAOpC;EAAA,sBAAuB;EAAvB,4DAAuB;IACvB;AADuB;AAIzB,yBAAyB;AAwBzB,iDAAiD;AA0B/C;EAAA,oBAA2I;EAA3I,mBAA2I;EAA3I,qBAA2I;EAA3I,iBAA2I;EAA3I,kCAA2I;EAA3I,sCAA2I;EAA3I,sBAA2I;EAA3I,uBAA2I;EAA3I,qBAA2I;EAA3I,wBAA2I;EAA3I,kBAA2I;EAA3I,iBAA2I;EAA3I,gBAA2I;EAA3I,oBAA2I;EAA3I,kDAA2I;IAC3I,kBAAkB;IAClB;AAF2I;AAK7I,8BAA8B;AAE5B;EAAA,oBAAoB;EAApB,mBAAoB;EAApB,qBAAoB;EAApB,iBAAoB;EAApB,kCAAoB;EAApB,qCAAoB;EAApB,sBAAoB;EAApB,uBAAoB;EAApB,qBAAoB;EAApB,wBAAoB;EAApB,kBAAoB;EAApB,iBAAoB;EAApB,gBAAoB;EAApB,oBAAoB;EAApB,kDAAoB;IAApB,kBAAoB;IAApB;AAAoB;AAApB;IAAA,WAAoB;IAApB,kBAAoB;IAApB,MAAoB;IAApB,OAAoB;IAApB,QAAoB;IAApB,SAAoB;IAApB,kCAAoB;IAApB,sBAAoB;IAApB,2DAAoB;EAAA;AAGtB;IACE,WAAW;IACX,kBAAkB;IAClB,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,WAAW;IACX,4BAA4B;IAC5B,kBAAkB;IAClB,2BAA2B;IAC3B,0CAA0C;EAC5C;AAEA,6BAA6B;AAE3B;EAAA,oBAA0E;EAA1E,mBAA0E;EAA1E,qBAA0E;EAA1E,qBAA0E;EAA1E,sBAA0E;EAA1E,oBAA0E;EAA1E,uBAA0E;EAA1E,kBAA0E;EAA1E,iBAA0E;EAA1E,gBAA0E;IAC1E,oFAAoF;IACpF,yBAAyB;IACzB,yCAAyC;IACzC;AAJ0E;AAO5E,0BAA0B;AAa1B,mBAAmB;AAMjB;;EAAA;IAAA;EAA6E;AAAA;AAA7E;EAAA,kCAA6E;EAA7E,qBAA6E;EAA7E,iBAA6E;EAA7E,0DAA6E;EAA7E,sBAA6E;EAA7E;AAA6E;AAG/E,uBAAuB;AAKvB,eAAe;AAMb;EAAA,aAAyE;EAAzE,mBAAyE;EAAzE,qBAAyE;EAAzE,qBAAyE;EAAzE,sBAAyE;EAAzE,mBAAyE;EAAzE,sBAAyE;EAAzE,mBAAyE;EAAzE,oBAAyE;EAAzE,gBAAyE;EAAzE,+FAAyE;EAAzE,wDAAyE;EAAzE,0BAAyE;EAAzE,iBAAyE;EAAzE,kCAAyE;EAAzE,qCAAyE;EAAzE,oBAAyE;EAAzE;AAAyE;AAIzE;EAAA,aAAkE;EAAlE,mBAAkE;EAAlE,qBAAkE;EAAlE,qBAAkE;EAAlE,sBAAkE;EAAlE,mBAAkE;EAAlE,sBAAkE;EAAlE,mBAAkE;EAAlE,oBAAkE;EAAlE,gBAAkE;EAAlE,+FAAkE;EAAlE,wDAAkE;EAAlE,0BAAkE;EAAlE,oBAAkE;EAAlE;AAAkE;AAAlE;EAAA,kBAAkE;EAAlE,yDAAkE;EAAlE,oBAAkE;EAAlE;AAAkE;AAGpE,2CAA2C;AA+B3C,oCAAoC;AAepC,6BAA6B;AAC7B;IACE,0BAA0B;IAC1B,mCAAmC;EACrC;AAEA;IACE,4BAA4B;IAC5B,mCAAmC;EACrC;AAEA;IACE,2BAA2B;IAC3B,mCAAmC;EACrC;AAEA,+BAA+B;AAC/B;IACE,mCAAmC;IACnC,mCAAmC;EACrC;AAEA;IACE,8CAA8C;IAC9C,mCAAmC;EACrC;AAEA;IACE,2CAA2C;IAC3C,mCAAmC;EACrC;AAEA,6BAA6B;AAC7B;IACE,qCAAqC;IACrC,yCAAyC;IACzC,mCAAmC;EACrC;AAEA;IACE,oCAAoC;IACpC,mCAAmC;EACrC;AAnbF;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,oBAAmB;EAAnB,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6CAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,6CAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,wJAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAwbjB,0BAA0B;AAS1B,kBAAkB;AASlB,6CAA6C;AAC7C;IACE;+CAC2C;EAC7C;AAEA;IACE;gDAC4C;EAC9C;AAEA;IACE;gDAC4C;EAC9C;AAEA;IACE;+CAC2C;EAC7C;AAEA;IACE;gDAC4C;EAC9C;AAEA,wBAAwB;AAKxB;IACE,2DAA2D;EAC7D;AAEA;IACE,0DAA0D;EAC5D;AAEA,oBAAoB;AACpB;IACE,wCAAwC;EAC1C;AAEA;IACE,wCAAwC;IACxC,mBAAmB;EACrB;AAEA,mBAAmB;AACnB;IACE,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,sFAAsF;IACtF,8BAA8B;EAChC;AAEA,0BAA0B;AAC1B;IACE,uDAAuD;EACzD;AAEA;IACE,sBAAsB;EACxB;AAEA,gBAAgB;AAChB;IACE,uDAAuD;EACzD;AAEA;IACE,0DAA0D;EAC5D;AAEA,iCAAiC;AAajC;IACE,oCAAoC;EACtC;AAEA;IACE,qCAAqC;EACvC;AAcA,yBAAyB;AACzB,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;AAI5C,0CAA0C;AAU1C,wBAAwB;;AA9kB1B,wBAAwB;;AAGxB,sBAAsB;AACtB,mHAAmH;AACnH,wHAAwH;;AAExH,gBAAgB;;AAwChB,qBAAqB;;AAqYrB,mBAAmB;;AAwKnB,+BAA+B;AAC/B;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,UAAU;EACZ;EACA;IACE,sBAAsB;IACtB,YAAY;EACd;EACA;IACE,qBAAqB;IACrB,UAAU;EACZ;EACA;IACE,mBAAmB;IACnB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,UAAU;EACZ;EACA;IACE,mBAAmB;IACnB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,oCAAoC;IACpC,UAAU;EACZ;EACA;IACE,gCAAgC;IAChC,UAAU;EACZ;AACF;;AAEA;EACE;IACE,0BAA0B;EAC5B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,4CAA4C;EAC9C;EACA;IACE,gFAAgF;EAClF;AACF;;AAEA;EACE;IACE,0CAA0C;EAC5C;EACA;IACE,4EAA4E;EAC9E;AACF;;AAEA;EACE;IACE,2CAA2C;EAC7C;EACA;IACE,8EAA8E;EAChF;AACF;;AAEA;EACE;IACE,WAAW;EACb;EACA;IACE,UAAU;EACZ;AACF;;AAEA,iBAAiB;AACjB;EACE;IACE,wBAAwB;EAC1B;;EAGE;IAAA,kBAA0B;IAA1B,4DAA0B;IAA1B,oBAA0B;IAA1B;EAA0B;AAE9B;AACA;;;EAGE,6CAA6C;EAC7C,wBAAwB;EACxB,2BAA2B;EAC3B,mBAAmB;AACrB;;AAEA,wBAAwB;AACxB;EACE;;;IAGE,qCAAqC;IACrC,uCAAuC;IACvC,sCAAsC;IACtC,gCAAgC;EAClC;AACF;;AAEA,sBAAsB;AACtB;;;EAGE,kCAAkC;AACpC;;AAEA;;;;EAIE,oDAAoD;AACtD;;AAEA,uBAAuB;AACvB;EACE,iCAAiC;EACjC,yBAAyB;AAC3B;;AAEA,4BAA4B;AAC5B;EACE,6BAA6B;EAC7B,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,oBAAoB;AACpB;EACE,wBAAwB;EACxB,mCAAmC;AACrC;;AAEA,0BAA0B;AAC1B;EACE,2BAA2B;AAC7B;AAnaE;IACE,0BAA0B;IAC1B,mCAAmC;EACrC;AA/YF;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,wBAgzBA;EAhzBA,wDAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,gBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,8BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,2GAgzBA;EAhzBA,yGAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,8BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,2GAgzBA;EAhzBA,yGAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;;EAAA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA,uBAgzBA;IAhzBA,2DAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA,oBAgzBA;IAhzBA;EAgzBA;AAAA;AAhzBA;;EAAA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;AAAA;AAhzBA;;EAAA;IAAA;EAgzBA;;EAhzBA;IAAA,QAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA,uBAgzBA;IAhzBA,oDAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA,uBAgzBA;IAhzBA,2DAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA,kBAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA;EAgzBA;AAAA;AAhzBA;;EAAA;IAAA;EAgzBA;AAAA\",\"sourcesContent\":[\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* Import theme system */\\n@import './themes.css';\\n\\n/* Import Inter font */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');\\n\\n/* Base styles */\\n@layer base {\\n  html {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n    background-color: var(--bg-primary);\\n    color: var(--text-primary);\\n    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';\\n    transition: var(--theme-transition);\\n    @apply antialiased;\\n  }\\n\\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 6px;\\n    height: 6px;\\n  }\\n\\n  ::-webkit-scrollbar-track {\\n    @apply bg-slate-800;\\n  }\\n\\n  ::-webkit-scrollbar-thumb {\\n    @apply bg-slate-600 rounded-full;\\n  }\\n\\n  ::-webkit-scrollbar-thumb:hover {\\n    @apply bg-slate-500;\\n  }\\n\\n  /* Firefox scrollbar */\\n  * {\\n    scrollbar-width: thin;\\n    scrollbar-color: #475569 #1e293b;\\n  }\\n}\\n\\n/* Component styles */\\n@layer components {\\n  /* Enhanced Card components with theme-aware immersive effects */\\n  .card {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    @apply rounded-lg;\\n    transition: var(--theme-transition);\\n  }\\n\\n  .card-hover {\\n    @apply card;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .card-hover:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n\\n  /* Immersive card with glow effect */\\n  .card-glow {\\n    @apply card-hover;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .card-glow::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\\n    transition: left 0.5s;\\n  }\\n\\n  .card-glow:hover::before {\\n    left: 100%;\\n  }\\n\\n  /* Premium card with enhanced theme-aware effects */\\n  .card-premium {\\n    @apply card-glow;\\n    background: var(--bg-glass-strong);\\n    backdrop-filter: blur(15px);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .card-premium:hover {\\n    background: var(--bg-glass-strong);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n\\n  /* Enhanced Button variants with immersive effects */\\n  .btn-primary {\\n    @apply bg-green-600 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  }\\n\\n  .btn-primary:hover {\\n    @apply bg-green-700;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.3);\\n  }\\n\\n  .btn-primary:active {\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-secondary {\\n    @apply bg-slate-700 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-secondary:hover {\\n    @apply bg-slate-600;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 15px rgba(71, 85, 105, 0.3);\\n  }\\n\\n  .btn-outline {\\n    @apply border border-slate-600 text-slate-300 font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    background: transparent;\\n  }\\n\\n  .btn-outline:hover {\\n    @apply border-green-500 text-white bg-slate-800;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 0 15px rgba(34, 197, 94, 0.2);\\n  }\\n\\n  .btn-danger {\\n    @apply bg-red-600 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-danger:hover {\\n    @apply bg-red-700;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(239, 68, 68, 0.3);\\n  }\\n\\n  /* Button ripple effect */\\n  .btn-ripple {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .btn-ripple::before {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 50%;\\n    width: 0;\\n    height: 0;\\n    border-radius: 50%;\\n    background: rgba(255, 255, 255, 0.3);\\n    transform: translate(-50%, -50%);\\n    transition: width 0.3s, height 0.3s;\\n  }\\n\\n  .btn-ripple:active::before {\\n    width: 300px;\\n    height: 300px;\\n  }\\n\\n  /* Enhanced Input styles with immersive effects */\\n  .input-primary {\\n    @apply bg-slate-800 border border-slate-600 text-white placeholder-slate-400 rounded-lg px-3 py-2 focus:outline-none;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n  }\\n\\n  .input-primary:focus {\\n    @apply border-green-500 bg-slate-750;\\n    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.1);\\n    transform: translateY(-1px);\\n  }\\n\\n  .input-primary:hover:not(:focus) {\\n    @apply border-slate-500;\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  /* Floating label input */\\n  .input-floating {\\n    @apply input-primary;\\n    padding-top: 1.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .input-floating + label {\\n    position: absolute;\\n    left: 0.75rem;\\n    top: 0.75rem;\\n    color: rgb(148, 163, 184);\\n    font-size: 0.875rem;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    pointer-events: none;\\n    transform-origin: left top;\\n  }\\n\\n  .input-floating:focus + label,\\n  .input-floating:not(:placeholder-shown) + label {\\n    transform: translateY(-0.5rem) scale(0.75);\\n    color: rgb(34, 197, 94);\\n  }\\n\\n  /* Enhanced Status indicators with glow effects */\\n  .status-online {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/20 text-green-400 border border-green-500/20;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .status-online::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background: rgba(34, 197, 94, 0.1);\\n    border-radius: inherit;\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n\\n  .status-offline {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/20 text-red-400 border border-red-500/20;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .status-warning {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/20 text-yellow-400 border border-yellow-500/20;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  /* Live indicator with pulse */\\n  .status-live {\\n    @apply status-online;\\n  }\\n\\n  .status-live::after {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 0.5rem;\\n    width: 6px;\\n    height: 6px;\\n    background: rgb(34, 197, 94);\\n    border-radius: 50%;\\n    transform: translateY(-50%);\\n    animation: pulse 1.5s ease-in-out infinite;\\n  }\\n\\n  /* Premium status indicator */\\n  .status-premium {\\n    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;\\n    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(79, 70, 229, 0.2));\\n    color: rgb(196, 181, 253);\\n    border: 1px solid rgba(147, 51, 234, 0.3);\\n    box-shadow: 0 0 15px rgba(147, 51, 234, 0.2);\\n  }\\n\\n  /* Risk level indicators */\\n  .risk-low {\\n    @apply text-green-400 bg-green-900/20 border border-green-500/20;\\n  }\\n\\n  .risk-medium {\\n    @apply text-yellow-400 bg-yellow-900/20 border border-yellow-500/20;\\n  }\\n\\n  .risk-high {\\n    @apply text-red-400 bg-red-900/20 border border-red-500/20;\\n  }\\n\\n  /* Loading states */\\n  .loading-skeleton {\\n    @apply animate-pulse bg-slate-700 rounded;\\n  }\\n\\n  .loading-spinner {\\n    @apply animate-spin rounded-full border-2 border-slate-600 border-t-green-400;\\n  }\\n\\n  /* Data visualization */\\n  .chart-container {\\n    @apply bg-slate-800/50 rounded-lg p-4 border border-slate-700;\\n  }\\n\\n  /* Navigation */\\n  .nav-link {\\n    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;\\n  }\\n\\n  .nav-link-active {\\n    @apply nav-link bg-green-900/20 text-green-400 border border-green-500/20;\\n  }\\n\\n  .nav-link-inactive {\\n    @apply nav-link text-slate-400 hover:text-white hover:bg-slate-800;\\n  }\\n\\n  /* Enhanced Tables with immersive effects */\\n  .table-container {\\n    @apply overflow-hidden rounded-lg border border-slate-700;\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .table-header {\\n    @apply bg-slate-800 px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider border-b border-slate-700;\\n    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.9));\\n  }\\n\\n  .table-cell {\\n    @apply px-6 py-4 whitespace-nowrap text-sm text-slate-300 border-b border-slate-700/50;\\n    transition: all 0.2s ease-in-out;\\n  }\\n\\n  .table-row {\\n    @apply bg-slate-900 transition-all duration-300;\\n    position: relative;\\n  }\\n\\n  .table-row:hover {\\n    @apply bg-slate-800/70;\\n    transform: translateX(2px);\\n    box-shadow: 4px 0 8px rgba(34, 197, 94, 0.1);\\n  }\\n\\n  .table-row:hover .table-cell {\\n    @apply text-white;\\n  }\\n\\n  /* Interactive table row with glow */\\n  .table-row-interactive {\\n    @apply table-row cursor-pointer;\\n  }\\n\\n  .table-row-interactive:hover {\\n    background: linear-gradient(90deg, rgba(30, 41, 59, 0.8), rgba(34, 197, 94, 0.05), rgba(30, 41, 59, 0.8));\\n    border-left: 3px solid rgb(34, 197, 94);\\n  }\\n\\n  .table-row-interactive:active {\\n    transform: translateX(1px);\\n    box-shadow: 2px 0 4px rgba(34, 197, 94, 0.2);\\n  }\\n\\n  /* Theme-aware text classes */\\n  .themed-text-primary {\\n    color: var(--text-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-text-secondary {\\n    color: var(--text-secondary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-text-tertiary {\\n    color: var(--text-tertiary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  /* Theme-aware border classes */\\n  .themed-border {\\n    border-color: var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-border-b {\\n    border-bottom: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-border-t {\\n    border-top: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  /* Theme-aware card classes */\\n  .themed-card-secondary {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-card-secondary:hover {\\n    background-color: var(--bg-tertiary);\\n    border-color: var(--border-primary);\\n  }\\n}\\n\\n/* Utility styles */\\n@layer utilities {\\n  /* Glass morphism effect */\\n  .glass {\\n    @apply bg-white/5 backdrop-blur-md border border-white/10;\\n  }\\n\\n  .glass-dark {\\n    @apply bg-black/20 backdrop-blur-md border border-white/5;\\n  }\\n\\n  /* Gradient text */\\n  .gradient-text {\\n    @apply bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent;\\n  }\\n\\n  .gradient-text-warm {\\n    @apply bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent;\\n  }\\n\\n  /* Enhanced Custom shadows and glow effects */\\n  .shadow-glow-green {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3),\\n                0 0 40px rgba(34, 197, 94, 0.1);\\n  }\\n\\n  .shadow-glow-blue {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),\\n                0 0 40px rgba(59, 130, 246, 0.1);\\n  }\\n\\n  .shadow-glow-orange {\\n    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3),\\n                0 0 40px rgba(251, 191, 36, 0.1);\\n  }\\n\\n  .shadow-glow-red {\\n    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3),\\n                0 0 40px rgba(239, 68, 68, 0.1);\\n  }\\n\\n  .shadow-glow-purple {\\n    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3),\\n                0 0 40px rgba(147, 51, 234, 0.1);\\n  }\\n\\n  /* Pulsing glow effect */\\n  .glow-pulse {\\n    animation: glowPulse 2s ease-in-out infinite alternate;\\n  }\\n\\n  .glow-pulse-green {\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n\\n  .glow-pulse-blue {\\n    animation: glowPulseBlue 2s ease-in-out infinite alternate;\\n  }\\n\\n  /* Floating effect */\\n  .float {\\n    animation: float 3s ease-in-out infinite;\\n  }\\n\\n  .float-delayed {\\n    animation: float 3s ease-in-out infinite;\\n    animation-delay: 1s;\\n  }\\n\\n  /* Shimmer effect */\\n  .shimmer {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .shimmer::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\\n    animation: shimmer 2s infinite;\\n  }\\n\\n  /* Magnetic hover effect */\\n  .magnetic {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n\\n  .magnetic:hover {\\n    transform: scale(1.02);\\n  }\\n\\n  /* Tilt effect */\\n  .tilt {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n\\n  .tilt:hover {\\n    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);\\n  }\\n\\n  /* Enhanced Animation utilities */\\n  .animate-fade-in {\\n    animation: fadeIn 0.5s ease-in-out;\\n  }\\n\\n  .animate-slide-up {\\n    animation: slideUp 0.3s ease-out;\\n  }\\n\\n  .animate-slide-down {\\n    animation: slideDown 0.3s ease-out;\\n  }\\n\\n  .animate-slide-in-left {\\n    animation: slideInLeft 0.4s ease-out;\\n  }\\n\\n  .animate-slide-in-right {\\n    animation: slideInRight 0.4s ease-out;\\n  }\\n\\n  .animate-bounce-in {\\n    animation: bounceIn 0.6s ease-out;\\n  }\\n\\n  .animate-scale-in {\\n    animation: scaleIn 0.3s ease-out;\\n  }\\n\\n  .animate-rotate-in {\\n    animation: rotateIn 0.5s ease-out;\\n  }\\n\\n  /* Staggered animations */\\n  .animate-stagger-1 { animation-delay: 0.1s; }\\n  .animate-stagger-2 { animation-delay: 0.2s; }\\n  .animate-stagger-3 { animation-delay: 0.3s; }\\n  .animate-stagger-4 { animation-delay: 0.4s; }\\n  .animate-stagger-5 { animation-delay: 0.5s; }\\n\\n  /* Hide scrollbar but keep functionality */\\n  .scrollbar-hide {\\n    -ms-overflow-style: none;\\n    scrollbar-width: none;\\n  }\\n\\n  .scrollbar-hide::-webkit-scrollbar {\\n    display: none;\\n  }\\n\\n  /* Custom focus styles */\\n  .focus-ring {\\n    @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-slate-900;\\n  }\\n\\n  .focus-ring-blue {\\n    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900;\\n  }\\n\\n  .focus-ring-orange {\\n    @apply focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-slate-900;\\n  }\\n}\\n\\n/* Enhanced Custom animations */\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideDown {\\n  from {\\n    transform: translateY(-10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInLeft {\\n  from {\\n    transform: translateX(-20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInRight {\\n  from {\\n    transform: translateX(20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes bounceIn {\\n  0% {\\n    transform: scale(0.3);\\n    opacity: 0;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  70% {\\n    transform: scale(0.9);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes scaleIn {\\n  from {\\n    transform: scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes rotateIn {\\n  from {\\n    transform: rotate(-10deg) scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n\\n@keyframes glowPulse {\\n  0% {\\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);\\n  }\\n}\\n\\n@keyframes glowPulseGreen {\\n  0% {\\n    box-shadow: 0 0 5px rgba(34, 197, 94, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 30px rgba(34, 197, 94, 0.2);\\n  }\\n}\\n\\n@keyframes glowPulseBlue {\\n  0% {\\n    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 30px rgba(59, 130, 246, 0.2);\\n  }\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  body {\\n    @apply bg-white text-black;\\n  }\\n}\\n\\\\n\\\\n\\n/* Performance optimizations */\\n* {\\n  /* Use hardware acceleration for transforms */\\n  transform: translateZ(0);\\n  backface-visibility: hidden;\\n  perspective: 1000px;\\n}\\n\\n/* Optimize animations */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Optimize repaints */\\n.card-premium,\\n.card-glow,\\n.magnetic {\\n  will-change: transform, box-shadow;\\n}\\n\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline,\\n.btn-danger {\\n  will-change: transform, box-shadow, background-color;\\n}\\n\\n/* Optimize scrolling */\\n.scrollable {\\n  -webkit-overflow-scrolling: touch;\\n  overflow-scrolling: touch;\\n}\\n\\n/* Optimize text rendering */\\nbody {\\n  text-rendering: optimizeSpeed;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Optimize images */\\nimg {\\n  content-visibility: auto;\\n  contain-intrinsic-size: 300px 200px;\\n}\\n\\n/* Critical CSS inlining */\\n.above-fold {\\n  contain: layout style paint;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/globals.css\n"));

/***/ }),

/***/ "./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"./node_modules/next/node_modules/@swc/helpers/esm/_class_call_check.js\");\n/* harmony import */ var _swc_helpers_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/_/_inherits */ \"./node_modules/next/node_modules/@swc/helpers/esm/_inherits.js\");\n/* harmony import */ var _swc_helpers_create_super__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_create_super */ \"./node_modules/next/node_modules/@swc/helpers/esm/_create_super.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n\n\nvar ErrorBoundary = /*#__PURE__*/ function(Component) {\n    \"use strict\";\n    (0,_swc_helpers_inherits__WEBPACK_IMPORTED_MODULE_2__._)(ErrorBoundary, Component);\n    var _super = (0,_swc_helpers_create_super__WEBPACK_IMPORTED_MODULE_3__._)(ErrorBoundary);\n    function ErrorBoundary(props) {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_4__._)(this, ErrorBoundary);\n        var _this;\n        _this = _super.call(this, props);\n        _this.resetTimeoutId = null;\n        _this.resetErrorBoundary = function() {\n            if (_this.resetTimeoutId) {\n                clearTimeout(_this.resetTimeoutId);\n            }\n            _this.setState({\n                hasError: false,\n                error: undefined,\n                errorInfo: undefined,\n                eventId: undefined\n            });\n        };\n        _this.logErrorToService = function(error, errorInfo) {\n            // In a real app, you might send this to Sentry, LogRocket, etc.\n            try {\n                var errorData = {\n                    message: error.message,\n                    stack: error.stack,\n                    componentStack: errorInfo.componentStack,\n                    timestamp: new Date().toISOString(),\n                    userAgent:  true ? window.navigator.userAgent : 0,\n                    url:  true ? window.location.href : 0\n                };\n                console.warn(\"\\uD83D\\uDCCA Error logged:\", errorData);\n                // Store in localStorage for debugging\n                if (true) {\n                    var errors = JSON.parse(localStorage.getItem(\"fantasypro-errors\") || \"[]\");\n                    errors.push(errorData);\n                    // Keep only last 10 errors\n                    if (errors.length > 10) {\n                        errors.splice(0, errors.length - 10);\n                    }\n                    localStorage.setItem(\"fantasypro-errors\", JSON.stringify(errors));\n                }\n            } catch (loggingError) {\n                console.error(\"Failed to log error:\", loggingError);\n            }\n        };\n        _this.state = {\n            hasError: false\n        };\n        return _this;\n    }\n    var _proto = ErrorBoundary.prototype;\n    _proto.componentDidCatch = function componentDidCatch(error, errorInfo) {\n        console.error(\"\\uD83D\\uDEA8 ErrorBoundary caught an error:\", error, errorInfo);\n        // Update state with error info\n        this.setState({\n            error: error,\n            errorInfo: errorInfo\n        });\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // Log to external service (if needed)\n        this.logErrorToService(error, errorInfo);\n    };\n    _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n        var _this_props = this.props, resetKeys = _this_props.resetKeys, resetOnPropsChange = _this_props.resetOnPropsChange;\n        var hasError = this.state.hasError;\n        if (hasError && prevProps.resetKeys !== resetKeys) {\n            if (resetKeys) {\n                var hasResetKeyChanged = resetKeys.some(function(key, index) {\n                    var _prevProps_resetKeys;\n                    return ((_prevProps_resetKeys = prevProps.resetKeys) === null || _prevProps_resetKeys === void 0 ? void 0 : _prevProps_resetKeys[index]) !== key;\n                });\n                if (hasResetKeyChanged) {\n                    this.resetErrorBoundary();\n                }\n            }\n        }\n        if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {\n            this.resetErrorBoundary();\n        }\n    };\n    _proto.componentWillUnmount = function componentWillUnmount() {\n        if (this.resetTimeoutId) {\n            clearTimeout(this.resetTimeoutId);\n        }\n    };\n    _proto.render = function render() {\n        if (this.state.hasError) {\n            var _this_state_error, _this_state_error1, _this_state_errorInfo;\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-slate-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/20 border border-red-500/30 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold\",\n                                                children: \"!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold text-red-400\",\n                                                    children: \"Something went wrong\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this),\n                                                this.state.eventId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-red-300 mt-1\",\n                                                    children: [\n                                                        \"Error ID: \",\n                                                        this.state.eventId\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-300 mb-2\",\n                                                    children: \"Error Details:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 rounded p-3 text-sm text-red-200 font-mono\",\n                                                    children: ((_this_state_error = this.state.error) === null || _this_state_error === void 0 ? void 0 : _this_state_error.message) || \"Unknown error occurred\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                         true && ((_this_state_error1 = this.state.error) === null || _this_state_error1 === void 0 ? void 0 : _this_state_error1.stack) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-300 mb-2\",\n                                                    children: \"Stack Trace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto\",\n                                                    children: this.state.error.stack\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 19\n                                        }, this),\n                                         true && ((_this_state_errorInfo = this.state.errorInfo) === null || _this_state_errorInfo === void 0 ? void 0 : _this_state_errorInfo.componentStack) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-300 mb-2\",\n                                                    children: \"Component Stack:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto\",\n                                                    children: this.state.errorInfo.componentStack\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-3 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: this.resetErrorBoundary,\n                                                    className: \"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm font-medium transition-colors\",\n                                                    children: \"Try Again\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: function() {\n                                                        return window.location.reload();\n                                                    },\n                                                    className: \"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors\",\n                                                    children: \"Reload Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: function() {\n                                                        return window.history.back();\n                                                    },\n                                                    className: \"px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg text-sm font-medium transition-colors\",\n                                                    children: \"Go Back\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: function() {\n                                                        return window.location.href = \"/\";\n                                                    },\n                                                    className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors\",\n                                                    children: \"Go Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"If this error persists, please check the browser console for more details.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Development mode: Full error details are shown above.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    };\n    ErrorBoundary.getDerivedStateFromError = function getDerivedStateFromError(error) {\n        // Generate a unique event ID for this error\n        var eventId = \"error_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n        return {\n            hasError: true,\n            error: error,\n            eventId: eventId\n        };\n    };\n    return ErrorBoundary;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ErrorBoundary);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ErrorBoundary.tsx\n"));

/***/ })

});