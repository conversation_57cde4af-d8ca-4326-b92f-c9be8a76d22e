import { useState, useEffect, useCallback } from 'react';

interface Player {
  id: number;
  name: string;
  team: string;
  position: string;
  current_price?: number | null;
  current_breakeven?: number;
  season_points?: number;
}

interface UsePlayerSearchOptions {
  excludePlayerIds?: number[];
  filterByPosition?: string[];
  filterByTeam?: string[];
  maxResults?: number;
  minQueryLength?: number;
}

interface UsePlayerSearchReturn {
  players: Player[];
  searchPlayers: (query: string) => Player[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const usePlayerSearch = (options: UsePlayerSearchOptions = {}): UsePlayerSearchReturn => {
  const {
    excludePlayerIds = [],
    filterByPosition = [],
    filterByTeam = [],
    maxResults = 50,
    minQueryLength = 1
  } = options;

  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all players
  const fetchPlayers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('http://localhost:8001/players?limit=1000');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      setPlayers(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch players';
      setError(errorMessage);
      console.error('Error fetching players:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Search and filter players
  const searchPlayers = useCallback((query: string): Player[] => {
    if (!query || query.length < minQueryLength) {
      return [];
    }

    const searchTerm = query.toLowerCase().trim();
    
    let filtered = players.filter(player => {
      // Apply exclusion filters
      if (excludePlayerIds.includes(player.id)) return false;
      
      // Apply position filters
      if (filterByPosition.length > 0 && !filterByPosition.includes(player.position)) return false;
      
      // Apply team filters
      if (filterByTeam.length > 0 && !filterByTeam.includes(player.team)) return false;
      
      // Search in name and team
      const nameMatch = player.name.toLowerCase().includes(searchTerm);
      const teamMatch = player.team.toLowerCase().includes(searchTerm);
      
      return nameMatch || teamMatch;
    });

    // Sort by relevance
    filtered.sort((a, b) => {
      // Exact name matches first
      const aNameExact = a.name.toLowerCase().startsWith(searchTerm);
      const bNameExact = b.name.toLowerCase().startsWith(searchTerm);
      
      if (aNameExact && !bNameExact) return -1;
      if (!aNameExact && bNameExact) return 1;
      
      // Then by name length (shorter names first for partial matches)
      const aNameLength = a.name.length;
      const bNameLength = b.name.length;
      
      if (aNameLength !== bNameLength) {
        return aNameLength - bNameLength;
      }
      
      // Finally alphabetical
      return a.name.localeCompare(b.name);
    });

    return filtered.slice(0, maxResults);
  }, [players, excludePlayerIds, filterByPosition, filterByTeam, maxResults, minQueryLength]);

  // Initial fetch
  useEffect(() => {
    fetchPlayers();
  }, [fetchPlayers]);

  return {
    players,
    searchPlayers,
    loading,
    error,
    refetch: fetchPlayers
  };
};

// Hook for getting a specific player by ID
export const usePlayer = (playerId: number | null) => {
  const [player, setPlayer] = useState<Player | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!playerId) {
      setPlayer(null);
      return;
    }

    const fetchPlayer = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`http://localhost:8001/players/${playerId}`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        setPlayer(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch player';
        setError(errorMessage);
        console.error('Error fetching player:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPlayer();
  }, [playerId]);

  return { player, loading, error };
};

// Hook for player statistics and comparisons
export const usePlayerStats = () => {
  const getPlayersByPosition = useCallback((players: Player[], position: string) => {
    return players.filter(player => player.position === position);
  }, []);

  const getPlayersByTeam = useCallback((players: Player[], team: string) => {
    return players.filter(player => player.team === team);
  }, []);

  const getTopPlayersByPoints = useCallback((players: Player[], limit: number = 10) => {
    return [...players]
      .filter(player => player.season_points && player.season_points > 0)
      .sort((a, b) => (b.season_points || 0) - (a.season_points || 0))
      .slice(0, limit);
  }, []);

  const getPlayersByPriceRange = useCallback((players: Player[], minPrice: number, maxPrice: number) => {
    return players.filter(player => {
      const price = player.current_price || 0;
      return price >= minPrice && price <= maxPrice;
    });
  }, []);

  const formatPlayerPrice = useCallback((price: number | null | undefined) => {
    if (!price) return 'N/A';
    if (price >= 1000000) {
      return `$${(price / 1000000).toFixed(1)}M`;
    }
    return `$${(price / 1000).toFixed(0)}k`;
  }, []);

  return {
    getPlayersByPosition,
    getPlayersByTeam,
    getTopPlayersByPoints,
    getPlayersByPriceRange,
    formatPlayerPrice
  };
};

export default usePlayerSearch;
