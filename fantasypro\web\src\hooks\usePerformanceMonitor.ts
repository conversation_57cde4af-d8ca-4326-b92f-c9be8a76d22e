
import { useEffect } from 'react';

export const usePerformanceMonitor = () => {
  useEffect(() => {
    // Monitor Core Web Vitals
    if (typeof window !== 'undefined') {
      try {
        import('web-vitals').then((webVitals) => {
          // Use whatever functions are available in the web-vitals package
          if (webVitals.onCLS) webVitals.onCLS(console.log);
          if (webVitals.onFCP) webVitals.onFCP(console.log);
          if (webVitals.onLCP) webVitals.onLCP(console.log);
          if (webVitals.onTTFB) webVitals.onTTFB(console.log);
          if (webVitals.onINP) webVitals.onINP(console.log); // New metric replacing FID
        }).catch(() => {
          // Silently fail if web-vitals is not available
          console.log('Web Vitals monitoring not available');
        });
      } catch (error) {
        console.log('Web Vitals import failed:', error);
      }
    }
    
    // Monitor memory usage (Chrome only)
    if (typeof window !== 'undefined' && 'performance' in window) {
      const perf = performance as any;
      if (perf.memory) {
        const memoryInfo = perf.memory;
        console.log('Memory Usage:', {
          used: Math.round(memoryInfo.usedJSHeapSize / 1048576) + ' MB',
          total: Math.round(memoryInfo.totalJSHeapSize / 1048576) + ' MB',
          limit: Math.round(memoryInfo.jsHeapSizeLimit / 1048576) + ' MB'
        });
      }
    }
    
    // Monitor frame rate
    let frameCount = 0;
    let lastTime = performance.now();
    
    function countFrames() {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        console.log('FPS:', frameCount);
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(countFrames);
    }
    
    requestAnimationFrame(countFrames);
  }, []);
};

export default usePerformanceMonitor;
