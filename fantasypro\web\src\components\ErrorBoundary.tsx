import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  eventId?: string;
}

class ErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: number | null = null;

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Generate a unique event ID for this error
    const eventId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return { hasError: true, error, eventId };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 ErrorBoundary caught an error:', error, errorInfo);

    // Update state with error info
    this.setState({ error, errorInfo });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to external service (if needed)
    this.logErrorToService(error, errorInfo);
  }

  componentDidUpdate(prevProps: Props) {
    const { resetKeys, resetOnPropsChange } = this.props;
    const { hasError } = this.state;

    if (hasError && prevProps.resetKeys !== resetKeys) {
      if (resetKeys) {
        const hasResetKeyChanged = resetKeys.some((key, index) =>
          prevProps.resetKeys?.[index] !== key
        );
        if (hasResetKeyChanged) {
          this.resetErrorBoundary();
        }
      }
    }

    if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
      this.resetErrorBoundary();
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      eventId: undefined
    });
  };

  logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In a real app, you might send this to Sentry, LogRocket, etc.
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'SSR',
        url: typeof window !== 'undefined' ? window.location.href : 'SSR'
      };

      console.warn('📊 Error logged:', errorData);

      // Store in localStorage for debugging
      if (typeof window !== 'undefined') {
        const errors = JSON.parse(localStorage.getItem('fantasypro-errors') || '[]');
        errors.push(errorData);
        // Keep only last 10 errors
        if (errors.length > 10) {
          errors.splice(0, errors.length - 10);
        }
        localStorage.setItem('fantasypro-errors', JSON.stringify(errors));
      }
    } catch (loggingError) {
      console.error('Failed to log error:', loggingError);
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-slate-900">
          <div className="max-w-2xl mx-auto p-8">
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">!</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-red-400">Something went wrong</h1>
                  {this.state.eventId && (
                    <p className="text-xs text-red-300 mt-1">Error ID: {this.state.eventId}</p>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-red-300 mb-2">Error Details:</h3>
                  <div className="bg-slate-800 rounded p-3 text-sm text-red-200 font-mono">
                    {this.state.error?.message || 'Unknown error occurred'}
                  </div>
                </div>

                {process.env.NODE_ENV === 'development' && this.state.error?.stack && (
                  <div>
                    <h3 className="text-sm font-medium text-red-300 mb-2">Stack Trace:</h3>
                    <div className="bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto">
                      {this.state.error.stack}
                    </div>
                  </div>
                )}

                {process.env.NODE_ENV === 'development' && this.state.errorInfo?.componentStack && (
                  <div>
                    <h3 className="text-sm font-medium text-red-300 mb-2">Component Stack:</h3>
                    <div className="bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto">
                      {this.state.errorInfo.componentStack}
                    </div>
                  </div>
                )}

                <div className="flex flex-wrap gap-3 pt-4">
                  <button
                    type="button"
                    onClick={this.resetErrorBoundary}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm font-medium transition-colors"
                  >
                    Try Again
                  </button>
                  <button
                    type="button"
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors"
                  >
                    Reload Page
                  </button>
                  <button
                    type="button"
                    onClick={() => window.history.back()}
                    className="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg text-sm font-medium transition-colors"
                  >
                    Go Back
                  </button>
                  <button
                    type="button"
                    onClick={() => window.location.href = '/'}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
                  >
                    Go Home
                  </button>
                </div>
              </div>
            </div>

            <div className="mt-6 text-center space-y-2">
              <p className="text-sm text-gray-400">
                If this error persists, please check the browser console for more details.
              </p>
              {process.env.NODE_ENV === 'development' && (
                <p className="text-xs text-gray-500">
                  Development mode: Full error details are shown above.
                </p>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
