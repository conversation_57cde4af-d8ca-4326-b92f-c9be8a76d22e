@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import theme system */
@import './themes.css';

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

/* Base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }
  
  body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    transition: var(--theme-transition);
    @apply antialiased;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-slate-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-slate-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-slate-500;
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: #475569 #1e293b;
  }
}

/* Component styles */
@layer components {
  /* Enhanced Card components with theme-aware immersive effects */
  .card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    @apply rounded-lg;
    transition: var(--theme-transition);
  }

  .card-hover {
    @apply card;
    transform: translateY(0);
    transition: var(--theme-transition);
  }

  .card-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl), var(--glow-primary);
    border-color: var(--brand-primary);
  }

  /* Immersive card with glow effect */
  .card-glow {
    @apply card-hover;
    position: relative;
    overflow: hidden;
  }

  .card-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
    transition: left 0.5s;
  }

  .card-glow:hover::before {
    left: 100%;
  }

  /* Premium card with enhanced theme-aware effects */
  .card-premium {
    @apply card-glow;
    background: var(--bg-glass-strong);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-secondary);
    transition: var(--theme-transition);
  }

  .card-premium:hover {
    background: var(--bg-glass-strong);
    box-shadow: var(--shadow-xl), var(--glow-primary);
    border-color: var(--brand-primary);
  }

  /* Enhanced Button variants with immersive effects */
  .btn-primary {
    @apply bg-green-600 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .btn-primary:hover {
    @apply bg-green-700;
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),
                0 4px 6px -2px rgba(0, 0, 0, 0.1),
                0 0 20px rgba(34, 197, 94, 0.3);
  }

  .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .btn-secondary {
    @apply bg-slate-700 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .btn-secondary:hover {
    @apply bg-slate-600;
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),
                0 4px 6px -2px rgba(0, 0, 0, 0.1),
                0 0 15px rgba(71, 85, 105, 0.3);
  }

  .btn-outline {
    @apply border border-slate-600 text-slate-300 font-medium px-4 py-2 rounded-lg focus:outline-none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    background: transparent;
  }

  .btn-outline:hover {
    @apply border-green-500 text-white bg-slate-800;
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),
                0 0 15px rgba(34, 197, 94, 0.2);
  }

  .btn-danger {
    @apply bg-red-600 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .btn-danger:hover {
    @apply bg-red-700;
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),
                0 4px 6px -2px rgba(0, 0, 0, 0.1),
                0 0 20px rgba(239, 68, 68, 0.3);
  }

  /* Button ripple effect */
  .btn-ripple {
    position: relative;
    overflow: hidden;
  }

  .btn-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }

  .btn-ripple:active::before {
    width: 300px;
    height: 300px;
  }

  /* Enhanced Input styles with immersive effects */
  .input-primary {
    @apply bg-slate-800 border border-slate-600 text-white placeholder-slate-400 rounded-lg px-3 py-2 focus:outline-none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .input-primary:focus {
    @apply border-green-500 bg-slate-750;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1),
                0 0 20px rgba(34, 197, 94, 0.1);
    transform: translateY(-1px);
  }

  .input-primary:hover:not(:focus) {
    @apply border-slate-500;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Floating label input */
  .input-floating {
    @apply input-primary;
    padding-top: 1.5rem;
    padding-bottom: 0.5rem;
  }

  .input-floating + label {
    position: absolute;
    left: 0.75rem;
    top: 0.75rem;
    color: rgb(148, 163, 184);
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    transform-origin: left top;
  }

  .input-floating:focus + label,
  .input-floating:not(:placeholder-shown) + label {
    transform: translateY(-0.5rem) scale(0.75);
    color: rgb(34, 197, 94);
  }

  /* Enhanced Status indicators with glow effects */
  .status-online {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/20 text-green-400 border border-green-500/20;
    position: relative;
    overflow: hidden;
  }

  .status-online::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(34, 197, 94, 0.1);
    border-radius: inherit;
    animation: glowPulseGreen 2s ease-in-out infinite alternate;
  }

  .status-offline {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/20 text-red-400 border border-red-500/20;
    position: relative;
    overflow: hidden;
  }

  .status-warning {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/20 text-yellow-400 border border-yellow-500/20;
    position: relative;
    overflow: hidden;
  }

  /* Live indicator with pulse */
  .status-live {
    @apply status-online;
  }

  .status-live::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0.5rem;
    width: 6px;
    height: 6px;
    background: rgb(34, 197, 94);
    border-radius: 50%;
    transform: translateY(-50%);
    animation: pulse 1.5s ease-in-out infinite;
  }

  /* Premium status indicator */
  .status-premium {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(79, 70, 229, 0.2));
    color: rgb(196, 181, 253);
    border: 1px solid rgba(147, 51, 234, 0.3);
    box-shadow: 0 0 15px rgba(147, 51, 234, 0.2);
  }

  /* Risk level indicators */
  .risk-low {
    @apply text-green-400 bg-green-900/20 border border-green-500/20;
  }

  .risk-medium {
    @apply text-yellow-400 bg-yellow-900/20 border border-yellow-500/20;
  }

  .risk-high {
    @apply text-red-400 bg-red-900/20 border border-red-500/20;
  }

  /* Loading states */
  .loading-skeleton {
    @apply animate-pulse bg-slate-700 rounded;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-slate-600 border-t-green-400;
  }

  /* Data visualization */
  .chart-container {
    @apply bg-slate-800/50 rounded-lg p-4 border border-slate-700;
  }

  /* Navigation */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }

  .nav-link-active {
    @apply nav-link bg-green-900/20 text-green-400 border border-green-500/20;
  }

  .nav-link-inactive {
    @apply nav-link text-slate-400 hover:text-white hover:bg-slate-800;
  }

  /* Enhanced Tables with immersive effects */
  .table-container {
    @apply overflow-hidden rounded-lg border border-slate-700;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .table-header {
    @apply bg-slate-800 px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider border-b border-slate-700;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.9));
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-slate-300 border-b border-slate-700/50;
    transition: all 0.2s ease-in-out;
  }

  .table-row {
    @apply bg-slate-900 transition-all duration-300;
    position: relative;
  }

  .table-row:hover {
    @apply bg-slate-800/70;
    transform: translateX(2px);
    box-shadow: 4px 0 8px rgba(34, 197, 94, 0.1);
  }

  .table-row:hover .table-cell {
    @apply text-white;
  }

  /* Interactive table row with glow */
  .table-row-interactive {
    @apply table-row cursor-pointer;
  }

  .table-row-interactive:hover {
    background: linear-gradient(90deg, rgba(30, 41, 59, 0.8), rgba(34, 197, 94, 0.05), rgba(30, 41, 59, 0.8));
    border-left: 3px solid rgb(34, 197, 94);
  }

  .table-row-interactive:active {
    transform: translateX(1px);
    box-shadow: 2px 0 4px rgba(34, 197, 94, 0.2);
  }

  /* Theme-aware text classes */
  .themed-text-primary {
    color: var(--text-primary);
    transition: var(--theme-transition);
  }

  .themed-text-secondary {
    color: var(--text-secondary);
    transition: var(--theme-transition);
  }

  .themed-text-tertiary {
    color: var(--text-tertiary);
    transition: var(--theme-transition);
  }

  /* Theme-aware border classes */
  .themed-border {
    border-color: var(--border-primary);
    transition: var(--theme-transition);
  }

  .themed-border-b {
    border-bottom: 1px solid var(--border-primary);
    transition: var(--theme-transition);
  }

  .themed-border-t {
    border-top: 1px solid var(--border-primary);
    transition: var(--theme-transition);
  }

  /* Theme-aware card classes */
  .themed-card-secondary {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-secondary);
    transition: var(--theme-transition);
  }

  .themed-card-secondary:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
  }
}

/* Utility styles */
@layer utilities {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/5 backdrop-blur-md border border-white/10;
  }

  .glass-dark {
    @apply bg-black/20 backdrop-blur-md border border-white/5;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent;
  }

  .gradient-text-warm {
    @apply bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent;
  }

  /* Enhanced Custom shadows and glow effects */
  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3),
                0 0 40px rgba(34, 197, 94, 0.1);
  }

  .shadow-glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),
                0 0 40px rgba(59, 130, 246, 0.1);
  }

  .shadow-glow-orange {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3),
                0 0 40px rgba(251, 191, 36, 0.1);
  }

  .shadow-glow-red {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3),
                0 0 40px rgba(239, 68, 68, 0.1);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3),
                0 0 40px rgba(147, 51, 234, 0.1);
  }

  /* Pulsing glow effect */
  .glow-pulse {
    animation: glowPulse 2s ease-in-out infinite alternate;
  }

  .glow-pulse-green {
    animation: glowPulseGreen 2s ease-in-out infinite alternate;
  }

  .glow-pulse-blue {
    animation: glowPulseBlue 2s ease-in-out infinite alternate;
  }

  /* Floating effect */
  .float {
    animation: float 3s ease-in-out infinite;
  }

  .float-delayed {
    animation: float 3s ease-in-out infinite;
    animation-delay: 1s;
  }

  /* Shimmer effect */
  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
  }

  /* Magnetic hover effect */
  .magnetic {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .magnetic:hover {
    transform: scale(1.02);
  }

  /* Tilt effect */
  .tilt {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .tilt:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
  }

  /* Enhanced Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.4s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.4s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-rotate-in {
    animation: rotateIn 0.5s ease-out;
  }

  /* Staggered animations */
  .animate-stagger-1 { animation-delay: 0.1s; }
  .animate-stagger-2 { animation-delay: 0.2s; }
  .animate-stagger-3 { animation-delay: 0.3s; }
  .animate-stagger-4 { animation-delay: 0.4s; }
  .animate-stagger-5 { animation-delay: 0.5s; }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Custom focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-slate-900;
  }

  .focus-ring-blue {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900;
  }

  .focus-ring-orange {
    @apply focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-slate-900;
  }
}

/* Enhanced Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotateIn {
  from {
    transform: rotate(-10deg) scale(0.9);
    opacity: 0;
  }
  to {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glowPulse {
  0% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);
  }
}

@keyframes glowPulseGreen {
  0% {
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.2);
  }
  100% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 30px rgba(34, 197, 94, 0.2);
  }
}

@keyframes glowPulseBlue {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);
  }
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 30px rgba(59, 130, 246, 0.2);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply bg-white text-black;
  }
}
\n\n
/* Performance optimizations */
* {
  /* Use hardware acceleration for transforms */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Optimize repaints */
.card-premium,
.card-glow,
.magnetic {
  will-change: transform, box-shadow;
}

.btn-primary,
.btn-secondary,
.btn-outline,
.btn-danger {
  will-change: transform, box-shadow, background-color;
}

/* Optimize scrolling */
.scrollable {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Optimize text rendering */
body {
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Optimize images */
img {
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* Critical CSS inlining */
.above-fold {
  contain: layout style paint;
}
