/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/diagnostic";
exports.ids = ["pages/diagnostic"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdiagnostic&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdiagnostic.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdiagnostic&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdiagnostic.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\diagnostic.tsx */ \"./src/pages/diagnostic.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/diagnostic\",\n        pathname: \"/diagnostic\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_diagnostic_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdiagnostic&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdiagnostic.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-slate-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/20 border border-red-500/30 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold\",\n                                                children: \"!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-red-400\",\n                                            children: \"Something went wrong\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-300 mb-2\",\n                                                    children: \"Error Details:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 rounded p-3 text-sm text-red-200 font-mono\",\n                                                    children: this.state.error?.message || \"Unknown error occurred\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this),\n                                        this.state.error?.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-300 mb-2\",\n                                                    children: \"Stack Trace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto\",\n                                                    children: this.state.error.stack\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 19\n                                        }, this),\n                                        this.state.errorInfo?.componentStack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-300 mb-2\",\n                                                    children: \"Component Stack:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto\",\n                                                    children: this.state.errorInfo.componentStack\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>window.location.reload(),\n                                                    className: \"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors\",\n                                                    children: \"Reload Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>window.history.back(),\n                                                    className: \"px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg text-sm font-medium transition-colors\",\n                                                    children: \"Go Back\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"If this error persists, please check the browser console for more details.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   ThemeTransition: () => (/* binding */ ThemeTransition),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeTransition: () => (/* binding */ useThemeTransition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\nconst ThemeProvider = ({ children, defaultTheme = \"system\", storageKey = \"fantasypro-theme\" })=>{\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dark\");\n    // Get system preference\n    const getSystemTheme = ()=>{\n        if (false) {}\n        return \"dark\";\n    };\n    // Resolve theme based on current setting\n    const resolveTheme = (currentTheme)=>{\n        if (currentTheme === \"system\") {\n            return getSystemTheme();\n        }\n        return currentTheme;\n    };\n    // Apply theme to document\n    const applyTheme = (resolvedTheme)=>{\n        if (false) {}\n    };\n    // Set theme with persistence\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        if (false) {}\n        const resolved = resolveTheme(newTheme);\n        setResolvedTheme(resolved);\n        applyTheme(resolved);\n    };\n    // Toggle between dark and light (skips system)\n    const toggleTheme = ()=>{\n        const newTheme = resolvedTheme === \"dark\" ? \"light\" : \"dark\";\n        setTheme(newTheme);\n    };\n    // Initialize theme on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        defaultTheme,\n        storageKey\n    ]);\n    // Listen for system theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        theme\n    ]);\n    const value = {\n        theme,\n        resolvedTheme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n};\n// Theme transition component for smooth animations\nconst ThemeTransition = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"theme-transition\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook for theme-aware animations\nconst useThemeTransition = ()=>{\n    const { resolvedTheme } = useTheme();\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const triggerTransition = ()=>{\n        setIsTransitioning(true);\n        setTimeout(()=>setIsTransitioning(false), 300);\n    };\n    return {\n        isTransitioning,\n        triggerTransition,\n        resolvedTheme\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/dropdown-fix.css */ \"./src/styles/dropdown-fix.css\");\n/* harmony import */ var _styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ErrorBoundary */ \"./src/components/ErrorBoundary.tsx\");\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n            defaultTheme: \"dark\",\n            storageKey: \"fantasypro-theme\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUMrQjtBQUNLO0FBQ3FCO0FBQ0Q7QUFFekMsU0FBU0UsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWTtJQUM1RCxxQkFDRSw4REFBQ0gsaUVBQWFBO2tCQUNaLDRFQUFDRCxpRUFBYUE7WUFBQ0ssY0FBYTtZQUFPQyxZQUFXO3NCQUM1Qyw0RUFBQ0g7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhbnRhc3lwcm8td2ViLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcbmltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJztcbmltcG9ydCAnLi4vc3R5bGVzL2Ryb3Bkb3duLWZpeC5jc3MnO1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciB9IGZyb20gJy4uL2NvbnRleHRzL1RoZW1lQ29udGV4dCc7XG5pbXBvcnQgRXJyb3JCb3VuZGFyeSBmcm9tICcuLi9jb21wb25lbnRzL0Vycm9yQm91bmRhcnknO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxFcnJvckJvdW5kYXJ5PlxuICAgICAgPFRoZW1lUHJvdmlkZXIgZGVmYXVsdFRoZW1lPVwiZGFya1wiIHN0b3JhZ2VLZXk9XCJmYW50YXN5cHJvLXRoZW1lXCI+XG4gICAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgICA8L0Vycm9yQm91bmRhcnk+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiVGhlbWVQcm92aWRlciIsIkVycm9yQm91bmRhcnkiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJkZWZhdWx0VGhlbWUiLCJzdG9yYWdlS2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/diagnostic.tsx":
/*!**********************************!*\
  !*** ./src/pages/diagnostic.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst DiagnosticPage = ()=>{\n    const [diagnostics, setDiagnostics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        hydrationSafe: false,\n        errorBoundaryActive: false,\n        themeLoaded: false,\n        componentsLoaded: false,\n        timestamp: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Run diagnostics\n        const runDiagnostics = ()=>{\n            const results = {\n                hydrationSafe: \"undefined\" !== \"undefined\",\n                errorBoundaryActive: true,\n                themeLoaded: document.documentElement.classList.contains(\"dark\") || document.documentElement.classList.contains(\"light\"),\n                componentsLoaded: true,\n                timestamp: new Date().toISOString()\n            };\n            setDiagnostics(results);\n            console.log(\"\\uD83D\\uDD0D Diagnostics Results:\", results);\n        };\n        runDiagnostics();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Diagnostic - FantasyPro\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"System diagnostic page\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-slate-900 text-white p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold mb-8\",\n                            children: \"\\uD83D\\uDD0D System Diagnostics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"Hydration Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Client-side:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 49,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: diagnostics.hydrationSafe ? \"text-green-400\" : \"text-red-400\",\n                                                            children: diagnostics.hydrationSafe ? \"✅ Active\" : \"❌ Not Ready\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 50,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Timestamp:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 55,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400\",\n                                                            children: diagnostics.timestamp\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 56,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"Error Boundary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Status:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: diagnostics.errorBoundaryActive ? \"text-green-400\" : \"text-red-400\",\n                                                            children: diagnostics.errorBoundaryActive ? \"✅ Active\" : \"❌ Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Error boundary is wrapping the application\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"Theme System\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Theme Loaded:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: diagnostics.themeLoaded ? \"text-green-400\" : \"text-red-400\",\n                                                            children: diagnostics.themeLoaded ? \"✅ Loaded\" : \"❌ Not Loaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: [\n                                                        \"Current theme: \",\n                                                        document?.documentElement?.classList?.contains(\"dark\") ? \"Dark\" : \"Light\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"Components\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"React Components:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: diagnostics.componentsLoaded ? \"text-green-400\" : \"text-red-400\",\n                                                            children: diagnostics.componentsLoaded ? \"✅ Loaded\" : \"❌ Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"All components rendering successfully\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>window.location.reload(),\n                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg\",\n                                                    children: \"Reload Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>console.log(\"Current diagnostics:\", diagnostics),\n                                                    className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg\",\n                                                    children: \"Log to Console\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>window.history.back(),\n                                                    className: \"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg\",\n                                                    children: \"Go Back\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"Navigation Test\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/\",\n                                                    className: \"bg-slate-700 hover:bg-slate-600 text-center py-2 px-3 rounded\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/dashboard\",\n                                                    className: \"bg-slate-700 hover:bg-slate-600 text-center py-2 px-3 rounded\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/my-team\",\n                                                    className: \"bg-slate-700 hover:bg-slate-600 text-center py-2 px-3 rounded\",\n                                                    children: \"My Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/error-test\",\n                                                    className: \"bg-slate-700 hover:bg-slate-600 text-center py-2 px-3 rounded\",\n                                                    children: \"Error Test\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\diagnostic.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DiagnosticPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/diagnostic.tsx\n");

/***/ }),

/***/ "./src/styles/dropdown-fix.css":
/*!*************************************!*\
  !*** ./src/styles/dropdown-fix.css ***!
  \*************************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdiagnostic&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdiagnostic.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();