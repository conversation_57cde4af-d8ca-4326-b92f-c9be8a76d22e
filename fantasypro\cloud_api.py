#!/usr/bin/env python3
"""
Cloud API Server for FantasyPro
Uses Supabase as primary data source with local cache fallback
"""

import os
import logging
from datetime import datetime
from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from supabase_client import FantasyProSupabase<PERSON>lient
from data_cache import FantasyProDataCache
from injury_oracle import InjuryO<PERSON>le
from ml_engines import InjuryPredictionEngine, PerformancePredictionEngine, PlayerData
from injury_suspension_service import InjurySuspensionService
from intelligence_engine import FantasyProIntelligenceEngine
from comprehensive_data_service import ComprehensiveDataService
from nrl_news_scraper import NRLNewsScraper
from supercoach_scraper import Super<PERSON>oachScraper

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="FantasyPro Cloud API",
    description="NRL player data from Supabase cloud database with local fallback",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize clients
supabase_client = FantasyProSupabaseClient()
local_cache = FantasyProDataCache()
injury_oracle = None  # Will be initialized when needed
injury_suspension_service = InjurySuspensionService()
intelligence_engine = None  # Will be initialized when needed
comprehensive_data_service = None  # Will be initialized when needed

# Configuration
SUPABASE_SERVICE_KEY = os.getenv('SUPABASE_SERVICE_KEY')
SPORTRADAR_API_KEY = "aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN"

# Set up Supabase authentication if key is available
if SUPABASE_SERVICE_KEY:
    supabase_client.set_service_key(SUPABASE_SERVICE_KEY)
    print("✅ Supabase client authenticated")
else:
    print("⚠️  SUPABASE_SERVICE_KEY not found - using local cache only")

def get_data_source():
    """Determine which data source to use"""
    if SUPABASE_SERVICE_KEY:
        return 'supabase'
    else:
        return 'local'

@app.get("/")
async def root():
    return {
        "message": "FantasyPro Cloud API", 
        "status": "running",
        "data_source": get_data_source()
    }

@app.get("/health")
async def health_check():
    data_source = get_data_source()
    
    if data_source == 'supabase':
        stats = supabase_client.get_cache_stats()
    else:
        stats = local_cache.get_cache_stats()
    
    return {
        "status": "healthy",
        "data_source": data_source,
        "cache_stats": stats
    }

@app.get("/players")
async def get_all_players(limit: int = Query(default=100, le=1000)):
    """Get all NRL players"""
    try:
        data_source = get_data_source()
        
        if data_source == 'supabase':
            players = supabase_client.get_all_players(limit)
        else:
            players = local_cache.get_all_players()
            players = players[:limit] if limit else players
        
        return {
            "source": data_source,
            "count": len(players),
            "players": players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving players: {str(e)}")

@app.get("/teams")
async def get_teams():
    """Get all NRL teams"""
    try:
        data_source = get_data_source()
        
        if data_source == 'supabase':
            teams = supabase_client.get_all_teams()
        else:
            teams = local_cache.get_all_teams()
        
        return {
            "source": data_source,
            "count": len(teams),
            "teams": teams
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving teams: {str(e)}")

@app.get("/players/search")
async def search_players(
    q: str = Query(..., min_length=1, description="Search query"),
    limit: int = Query(default=20, le=100)
):
    """Search players by name or team"""
    try:
        data_source = get_data_source()
        
        if data_source == 'supabase':
            players = supabase_client.search_players(q, limit)
        else:
            players = local_cache.search_players(q, limit)
        
        return {
            "source": data_source,
            "query": q,
            "count": len(players),
            "players": players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching players: {str(e)}")

@app.get("/team/{team_id}/players")
async def get_team_players(team_id: str):
    """Get players from a specific team"""
    try:
        data_source = get_data_source()
        
        if data_source == 'supabase':
            players = supabase_client.get_team_players(team_id)
        else:
            players = local_cache.get_team_players(team_id)
        
        return {
            "source": data_source,
            "team_id": team_id,
            "count": len(players),
            "players": players
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving team players: {str(e)}")

@app.get("/search/predictive")
async def predictive_search(
    q: str = Query(..., min_length=1, description="Search query"),
    limit: int = Query(default=10, le=50),
    include_team: bool = Query(default=True, description="Include team in search")
):
    """
    Optimized predictive search for frontend typeahead components
    """
    try:
        data_source = get_data_source()
        
        if data_source == 'supabase':
            # Use Supabase search
            players = supabase_client.search_players(q, limit * 2)  # Get more for scoring
        else:
            # Use local cache search
            players = local_cache.search_players(q, limit * 2)
        
        # Apply relevance scoring
        query_lower = q.lower()
        scored_players = []
        
        for player in players:
            name = player.get('name', '').lower()
            team = player.get('team', '').lower()
            
            score = 0
            
            # Exact name start match (highest priority)
            if name.startswith(query_lower):
                score += 100
            # Name contains query
            elif query_lower in name:
                score += 50
            # Team contains query (if enabled)
            elif include_team and query_lower in team:
                score += 25
            
            if score > 0:
                player_copy = player.copy()
                player_copy['_search_score'] = score
                scored_players.append(player_copy)
        
        # Sort by score (descending) then by name
        scored_players.sort(key=lambda x: (-x['_search_score'], x.get('name', '')))
        
        # Remove score from results and limit
        results = []
        for player in scored_players[:limit]:
            player_result = {k: v for k, v in player.items() if k != '_search_score'}
            results.append(player_result)
        
        return {
            "source": data_source,
            "query": q,
            "count": len(results),
            "total_matches": len(scored_players),
            "players": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in predictive search: {str(e)}")

@app.get("/stats")
async def get_stats():
    """Get comprehensive statistics"""
    try:
        data_source = get_data_source()
        
        if data_source == 'supabase':
            cache_stats = supabase_client.get_cache_stats()
            all_players = supabase_client.get_all_players(1000)
            teams = supabase_client.get_all_teams()
        else:
            cache_stats = local_cache.get_cache_stats()
            all_players = local_cache.get_all_players()
            teams = local_cache.get_all_teams()
        
        # Count players by team
        team_counts = {}
        for player in all_players:
            team = player.get('team', 'Unknown')
            team_counts[team] = team_counts.get(team, 0) + 1
        
        # Count players by position
        position_counts = {}
        for player in all_players:
            position = player.get('position', 'Unknown')
            position_counts[position] = position_counts.get(position, 0) + 1
        
        return {
            "data_source": data_source,
            "cache_stats": cache_stats,
            "team_player_counts": team_counts,
            "position_counts": position_counts,
            "total_teams": len(teams),
            "total_players": len(all_players)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving stats: {str(e)}")

@app.post("/data/sync")
async def sync_data():
    """Sync data from SportRadar to both Supabase and local cache"""
    try:
        results = {}
        
        # Update Supabase if available
        if SUPABASE_SERVICE_KEY:
            supabase_result = supabase_client.update_from_sportradar(SPORTRADAR_API_KEY)
            results['supabase'] = supabase_result
        
        # Update local cache
        local_result = local_cache.update_from_sportradar(SPORTRADAR_API_KEY)
        results['local'] = local_result
        
        return {
            "status": "success",
            "message": "Data synchronized from SportRadar",
            "results": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error syncing data: {str(e)}")

@app.post("/data/migrate")
async def migrate_local_to_supabase():
    """Migrate data from local cache to Supabase"""
    if not SUPABASE_SERVICE_KEY:
        raise HTTPException(status_code=503, detail="Supabase not configured")
    
    try:
        # Get data from local cache
        local_players = local_cache.get_all_players()
        local_teams = local_cache.get_all_teams()
        
        # Store in Supabase
        teams_result = supabase_client.store_teams(local_teams)
        players_result = supabase_client.store_players(local_players)
        
        return {
            "status": "success",
            "message": "Data migrated from local cache to Supabase",
            "players_migrated": len(local_players),
            "teams_migrated": len(local_teams),
            "teams_result": teams_result is not None,
            "players_result": players_result is not None
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error migrating data: {str(e)}")

@app.get("/injuries")
async def get_injuries():
    """Get current injury reports"""
    try:
        data_source = get_data_source()

        if data_source == 'supabase':
            injuries = supabase_client.get_all_injuries(50)
        else:
            # For local cache, return empty for now
            injuries = []

        return {
            "source": data_source,
            "count": len(injuries),
            "injuries": injuries
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving injuries: {str(e)}")

@app.get("/injuries/oracle")
async def get_injury_oracle():
    """Get AI-powered injury intelligence"""
    global injury_oracle

    if not SUPABASE_SERVICE_KEY:
        raise HTTPException(status_code=503, detail="Injury Oracle requires Supabase configuration")

    try:
        # Initialize oracle if needed
        if not injury_oracle:
            injury_oracle = InjuryOracle(SPORTRADAR_API_KEY, SUPABASE_SERVICE_KEY)

        # Get live injury intelligence
        alerts = await injury_oracle.get_live_injury_intel()
        summary = injury_oracle.get_injury_summary()

        return {
            "source": "injury_oracle",
            "summary": summary,
            "alerts": [
                {
                    "player_name": alert.player_name,
                    "team": alert.team,
                    "injury_status": alert.injury_status,
                    "injury_type": alert.injury_type,
                    "impact_rating": alert.impact_rating,
                    "risk_score": alert.risk_score,
                    "ownership_percentage": alert.ownership_percentage,
                    "replacement_suggestions": alert.replacement_suggestions[:3],
                    "last_updated": alert.last_updated
                }
                for alert in alerts[:10]  # Top 10 alerts
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting injury oracle: {str(e)}")

@app.get("/ml/predictions")
async def get_ml_predictions(limit: int = Query(default=20, le=50)):
    """Get ML-powered player predictions"""
    try:
        data_source = get_data_source()

        if data_source == 'supabase':
            players = supabase_client.get_all_players(limit)
            injuries = supabase_client.get_all_injuries(100)
        else:
            players = local_cache.get_all_players()[:limit]
            injuries = []

        # Initialize ML engines
        injury_engine = InjuryPredictionEngine()
        performance_engine = PerformancePredictionEngine()

        predictions = []
        for player in players:
            try:
                # Convert to PlayerData
                ml_player = PlayerData(
                    player_id=str(player.get('id', '')),
                    name=player.get('name', ''),
                    team=player.get('team', ''),
                    position=player.get('position', 'Unknown'),
                    price=float(player.get('price', 500000)),
                    recent_scores=[],  # Would need historical data
                    season_points=int(player.get('season_points', 0)),
                    games_played=int(player.get('games_played', 15)),
                    injury_history=[],
                    ownership_percentage=20.0  # Default
                )

                # Get predictions
                injury_pred = injury_engine.predict_injury_risk(ml_player, injuries)
                perf_pred = performance_engine.predict_performance(ml_player, injury_pred)

                predictions.append({
                    "player_name": player.get('name'),
                    "team": player.get('team'),
                    "position": player.get('position'),
                    "injury_risk": injury_pred,
                    "performance_prediction": perf_pred
                })

            except Exception as e:
                logger.error(f"Error predicting for {player.get('name')}: {e}")
                continue

        return {
            "source": data_source,
            "count": len(predictions),
            "predictions": predictions
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting ML predictions: {str(e)}")

@app.get("/injuries-suspensions")
async def get_injuries_suspensions():
    """Get current injury and suspension data from ZeroTackle"""
    try:
        data = injury_suspension_service.get_injury_suspension_data()
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting injury/suspension data: {str(e)}")

@app.get("/injuries-suspensions/summary")
async def get_injury_suspension_summary():
    """Get injury and suspension summary for dashboard"""
    try:
        summary = injury_suspension_service.get_injury_summary()
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting injury/suspension summary: {str(e)}")

@app.get("/injuries-suspensions/alerts")
async def get_critical_alerts(limit: int = Query(default=5, le=20)):
    """Get critical injury/suspension alerts"""
    try:
        alerts = injury_suspension_service.get_critical_alerts(limit)
        return {
            "count": len(alerts),
            "alerts": alerts
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting critical alerts: {str(e)}")

@app.post("/injuries-suspensions/refresh")
async def refresh_injury_suspension_data():
    """Force refresh of injury/suspension data"""
    try:
        data = injury_suspension_service.force_refresh()
        return {
            "status": "success",
            "message": "Injury/suspension data refreshed",
            "data": data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error refreshing injury/suspension data: {str(e)}")

@app.get("/intelligence/captain-recommendations")
async def get_captain_recommendations(squad_players: str = Query(..., description="JSON string of squad players")):
    """Get AI-powered captain recommendations"""
    global intelligence_engine

    try:
        # Initialize intelligence engine if needed
        if not intelligence_engine:
            intelligence_engine = FantasyProIntelligenceEngine(SUPABASE_SERVICE_KEY)

        # Parse squad players
        import json
        players = json.loads(squad_players)

        # Get captain recommendations
        recommendations = intelligence_engine.get_captain_recommendations(players, limit=3)

        return {
            "source": "intelligence_engine",
            "count": len(recommendations),
            "recommendations": recommendations
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting captain recommendations: {str(e)}")

@app.get("/intelligence/trade-recommendations")
async def get_trade_recommendations(
    squad_players: str = Query(..., description="JSON string of current squad"),
    budget: float = Query(default=0, description="Available budget")
):
    """Get AI-powered trade recommendations"""
    global intelligence_engine

    try:
        # Initialize intelligence engine if needed
        if not intelligence_engine:
            intelligence_engine = FantasyProIntelligenceEngine(SUPABASE_SERVICE_KEY)

        # Parse squad players
        import json
        players = json.loads(squad_players)

        # Get trade recommendations
        recommendations = intelligence_engine.get_trade_recommendations(players, budget, limit=5)

        return {
            "source": "intelligence_engine",
            "count": len(recommendations),
            "budget": budget,
            "recommendations": recommendations
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting trade recommendations: {str(e)}")

@app.get("/intelligence/analytics")
async def get_analytics_predictions(squad_players: str = Query(..., description="JSON string of squad players")):
    """Get comprehensive analytics and predictions"""
    global intelligence_engine

    try:
        # Initialize intelligence engine if needed
        if not intelligence_engine:
            intelligence_engine = FantasyProIntelligenceEngine(SUPABASE_SERVICE_KEY)

        # Parse squad players
        import json
        players = json.loads(squad_players)

        # Get analytics
        analytics = intelligence_engine.get_analytics_predictions(players)

        return {
            "source": "intelligence_engine",
            "analytics": analytics
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting analytics: {str(e)}")

@app.get("/intelligence/injury-intelligence")
async def get_injury_intelligence(squad_players: str = Query(..., description="JSON string of squad players")):
    """Get comprehensive injury intelligence"""
    global intelligence_engine

    try:
        # Initialize intelligence engine if needed
        if not intelligence_engine:
            intelligence_engine = FantasyProIntelligenceEngine(SUPABASE_SERVICE_KEY)

        # Parse squad players
        import json
        players = json.loads(squad_players)

        # Get injury intelligence
        intelligence = intelligence_engine.get_injury_intelligence(players)

        return {
            "source": "intelligence_engine",
            "intelligence": intelligence
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting injury intelligence: {str(e)}")

@app.get("/intelligence/sample-squad")
async def get_sample_squad():
    """Get sample squad data for testing intelligence features"""
    try:
        # Create a hardcoded sample squad for testing
        sample_squad = [
            {
                'id': 1,
                'name': 'James Tedesco',
                'team': 'Sydney Roosters',
                'position': 'Fullback',
                'price': 817700,
                'recent_scores': [85.4, 72.1, 91.2],
                'season_points': 1200,
                'games_played': 15,
                'ownership_percentage': 45.2
            },
            {
                'id': 2,
                'name': 'Nathan Cleary',
                'team': 'Penrith Panthers',
                'position': 'Halfback',
                'price': 795600,
                'recent_scores': [78.2, 82.5, 69.8],
                'season_points': 1150,
                'games_played': 14,
                'ownership_percentage': 38.7
            },
            {
                'id': 3,
                'name': 'Herbie Farnworth',
                'team': 'Brisbane Broncos',
                'position': 'Centre',
                'price': 815400,
                'recent_scores': [82.1, 76.3, 88.9],
                'season_points': 1180,
                'games_played': 15,
                'ownership_percentage': 42.1
            }
        ]

        return {
            "source": "sample",
            "squad_size": len(sample_squad),
            "squad": sample_squad
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting sample squad: {str(e)}")

@app.post("/intelligence/captain-recommendations-simple")
async def get_captain_recommendations_simple():
    """Get captain recommendations for sample squad"""
    global intelligence_engine

    try:
        # Initialize intelligence engine if needed
        if not intelligence_engine:
            intelligence_engine = FantasyProIntelligenceEngine(SUPABASE_SERVICE_KEY)

        # Use sample squad
        sample_squad = [
            {
                'id': 1,
                'name': 'James Tedesco',
                'team': 'Sydney Roosters',
                'position': 'Fullback',
                'price': 817700,
                'recent_scores': [85.4, 72.1, 91.2],
                'season_points': 1200,
                'games_played': 15,
                'ownership_percentage': 45.2
            },
            {
                'id': 2,
                'name': 'Nathan Cleary',
                'team': 'Penrith Panthers',
                'position': 'Halfback',
                'price': 795600,
                'recent_scores': [78.2, 82.5, 69.8],
                'season_points': 1150,
                'games_played': 14,
                'ownership_percentage': 38.7
            },
            {
                'id': 3,
                'name': 'Herbie Farnworth',
                'team': 'Brisbane Broncos',
                'position': 'Centre',
                'price': 815400,
                'recent_scores': [82.1, 76.3, 88.9],
                'season_points': 1180,
                'games_played': 15,
                'ownership_percentage': 42.1
            }
        ]

        # Get captain recommendations
        recommendations = intelligence_engine.get_captain_recommendations(sample_squad, limit=3)

        return {
            "source": "intelligence_engine",
            "count": len(recommendations),
            "recommendations": recommendations
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting captain recommendations: {str(e)}")

@app.get("/news/trending")
async def get_trending_news(limit: int = Query(default=5, description="Number of news items to return")):
    """Get trending NRL news"""
    global comprehensive_data_service

    try:
        # Initialize service if needed
        if not comprehensive_data_service:
            comprehensive_data_service = ComprehensiveDataService(SUPABASE_SERVICE_KEY)

        # Get trending news
        news = comprehensive_data_service.get_trending_news()

        return {
            "source": "nrl.com",
            "count": len(news[:limit]),
            "last_updated": datetime.now().isoformat(),
            "articles": news[:limit]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting trending news: {str(e)}")

@app.get("/supercoach/ladder")
async def get_supercoach_ladder(limit: int = Query(default=10, description="Number of ladder entries to return")):
    """Get SuperCoach ladder/rankings"""
    global comprehensive_data_service

    try:
        # Initialize service if needed
        if not comprehensive_data_service:
            comprehensive_data_service = ComprehensiveDataService(SUPABASE_SERVICE_KEY)

        # Get ladder data
        ladder = comprehensive_data_service.get_supercoach_ladder()

        return {
            "source": "supercoach.com.au",
            "count": len(ladder[:limit]),
            "last_updated": datetime.now().isoformat(),
            "rankings": ladder[:limit]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting SuperCoach ladder: {str(e)}")

@app.post("/data/collect-all")
async def collect_all_data():
    """Trigger comprehensive data collection from all sources"""
    global comprehensive_data_service

    try:
        # Initialize service if needed
        if not comprehensive_data_service:
            comprehensive_data_service = ComprehensiveDataService(SUPABASE_SERVICE_KEY)

        # Collect all data
        data = comprehensive_data_service.collect_all_data()

        return {
            "status": "completed",
            "collection_timestamp": data.get('collection_timestamp'),
            "sources_status": data.get('status', {}),
            "summary": {
                "supercoach_pages": len(data.get('supercoach_data', {}).get('data', {})),
                "news_articles": len(data.get('news_data', {}).get('articles', [])),
                "total_data_size": len(str(data))
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error collecting data: {str(e)}")

if __name__ == "__main__":
    print("🏈 Starting FantasyPro Cloud API Server")
    print(f"📊 Data Source: {get_data_source()}")
    print("📊 Players: http://localhost:8004/players")
    print("🔍 Search: http://localhost:8004/players/search?q=player_name")
    print("🚀 Predictive: http://localhost:8004/search/predictive?q=ted")
    print("📈 Stats: http://localhost:8004/stats")
    print("🔄 Sync: POST http://localhost:8004/data/sync")
    print("🔍 Health: http://localhost:8004/health")
    print("📖 Docs: http://localhost:8004/docs")
    
    if not SUPABASE_SERVICE_KEY:
        print("\n⚠️  To enable Supabase:")
        print("   set SUPABASE_SERVICE_KEY=your_service_key")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8004,  # Different port for cloud API
        reload=False,
        log_level="info"
    )
