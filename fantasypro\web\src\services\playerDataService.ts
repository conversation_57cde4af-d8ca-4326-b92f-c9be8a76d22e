/**
 * Player Data Integration Service
 * Combines cached NRL data with live web scraping
 */

import fs from 'fs';
import path from 'path';

// FantasyPro Player interface
export interface Player {
  id: string;
  name: string;
  position: string;
  team: string;
  price: number;
  points: number;
  average: number;
  form: number;
  ownership?: number;
  breakeven?: number;
  injury_status?: string;
  games_played?: number;
  minutes_per_game?: number;
  source: string;
  last_updated: string;
}

// Cached data interfaces
interface CachedPlayerData {
  name: string;
  posn: string;
  played: string;
  price: string;
  be: string;
  avg: string;
  mins: string;
}

interface ConsolidatedPlayer {
  name: string;
  consolidated_stats: CachedPlayerData;
}

interface CachedData {
  collection_timestamp: string;
  consolidated_players: Record<string, ConsolidatedPlayer>;
}

// Team mapping for NRL players
const TEAM_MAPPING: Record<string, string> = {
  // Common team abbreviations to full names
  'BRO': 'Brisbane Broncos',
  'SYD': 'Sydney Roosters', 
  'MEL': 'Melbourne Storm',
  'PEN': 'Penrith Panthers',
  'CRO': 'Cronulla Sharks',
  'MAN': 'Manly Sea Eagles',
  'RAB': 'South Sydney Rabbitohs',
  'PAR': 'Parramatta Eels',
  'NEW': 'Newcastle Knights',
  'CAN': 'Canberra Raiders',
  'GLD': 'Gold Coast Titans',
  'STI': 'St George Illawarra Dragons',
  'CNT': 'Canterbury Bulldogs',
  'TIG': 'Wests Tigers',
  'COW': 'North Queensland Cowboys',
  'NZW': 'New Zealand Warriors',
  'DOL': 'Dolphins'
};

// Player to team mapping (this would ideally come from a more comprehensive source)
const PLAYER_TEAM_MAPPING: Record<string, string> = {
  'reece walsh': 'Brisbane Broncos',
  'josiah karapani': 'Brisbane Broncos',
  'kotoni staggs': 'Brisbane Broncos',
  'herbie farnworth': 'Dolphins',
  'hamiso tabuai-fidow': 'Dolphins',
  'jahrome hughes': 'Melbourne Storm',
  'ryan papenhuyzen': 'Melbourne Storm',
  'nathan cleary': 'Penrith Panthers',
  'jarome luai': 'Penrith Panthers',
  'james tedesco': 'Sydney Roosters',
  'joseph manu': 'Sydney Roosters',
  // Add more mappings as needed
};

export class PlayerDataService {
  private static cachedPlayers: Player[] | null = null;
  private static lastCacheUpdate: number = 0;
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get the path to the cached player data
   */
  private static getCachedDataPath(): string {
    // In production, this would be an environment variable
    const basePath = process.cwd();
    return path.join(basePath, '..', 'data', 'nrl_player_cache', 'nrl_player_latest.json');
  }

  /**
   * Load cached player data from JSON file
   */
  private static loadCachedData(): CachedData | null {
    try {
      const dataPath = this.getCachedDataPath();
      
      // Check if file exists
      if (!fs.existsSync(dataPath)) {
        console.warn('Cached player data file not found:', dataPath);
        return null;
      }

      const rawData = fs.readFileSync(dataPath, 'utf-8');
      const data: CachedData = JSON.parse(rawData);
      
      console.log(`✅ Loaded cached data from ${data.collection_timestamp}`);
      console.log(`📊 Found ${Object.keys(data.consolidated_players).length} players in cache`);
      
      return data;
    } catch (error) {
      console.error('❌ Error loading cached player data:', error);
      return null;
    }
  }

  /**
   * Convert cached player data to FantasyPro format
   */
  private static convertCachedPlayer(
    playerKey: string, 
    playerData: ConsolidatedPlayer,
    index: number
  ): Player {
    const stats = playerData.consolidated_stats;
    const playerName = playerData.name;
    
    // Generate unique ID
    const id = `player_${index + 1}`;
    
    // Get team from mapping or default
    const team = PLAYER_TEAM_MAPPING[playerKey.toLowerCase()] || 'Unknown Team';
    
    // Convert price from string to number (remove any formatting)
    const price = parseInt(stats.price.replace(/[^0-9]/g, '')) || 0;
    
    // Convert other numeric fields
    const average = parseFloat(stats.avg) || 0;
    const breakeven = parseInt(stats.be) || 0;
    const gamesPlayed = parseInt(stats.played) || 0;
    const minutesPerGame = parseInt(stats.mins) || 0;
    
    // Calculate total points (average * games played)
    const points = Math.round(average * gamesPlayed);
    
    // Calculate form rating (simplified - could be more sophisticated)
    const form = Math.min(10, Math.max(0, average / 10));
    
    return {
      id,
      name: playerName,
      position: stats.posn || 'Unknown',
      team,
      price,
      points,
      average,
      form,
      breakeven,
      games_played: gamesPlayed,
      minutes_per_game: minutesPerGame,
      source: 'cached_nrl_data',
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Get all players from cached data
   */
  static async getAllPlayers(): Promise<Player[]> {
    // Return cached data if still valid
    const now = Date.now();
    if (this.cachedPlayers && (now - this.lastCacheUpdate) < this.CACHE_DURATION) {
      console.log('📋 Returning cached players data');
      return this.cachedPlayers;
    }

    console.log('🔄 Loading fresh player data...');
    
    try {
      // Load cached data
      const cachedData = this.loadCachedData();
      
      if (!cachedData) {
        console.warn('⚠️ No cached data available, returning empty array');
        return [];
      }

      // Convert to FantasyPro format
      const players: Player[] = [];
      const consolidatedPlayers = cachedData.consolidated_players;
      
      Object.entries(consolidatedPlayers).forEach(([playerKey, playerData], index) => {
        try {
          const player = this.convertCachedPlayer(playerKey, playerData, index);
          players.push(player);
        } catch (error) {
          console.warn(`⚠️ Error converting player ${playerKey}:`, error);
        }
      });

      // Sort players by team and name
      players.sort((a, b) => {
        if (a.team !== b.team) {
          return a.team.localeCompare(b.team);
        }
        return a.name.localeCompare(b.name);
      });

      console.log(`✅ Converted ${players.length} players from cached data`);
      
      // Cache the results
      this.cachedPlayers = players;
      this.lastCacheUpdate = now;
      
      return players;
      
    } catch (error) {
      console.error('❌ Error getting players:', error);
      return [];
    }
  }

  /**
   * Get players by team
   */
  static async getPlayersByTeam(teamName: string): Promise<Player[]> {
    const allPlayers = await this.getAllPlayers();
    return allPlayers.filter(player => 
      player.team.toLowerCase().includes(teamName.toLowerCase())
    );
  }

  /**
   * Get players by position
   */
  static async getPlayersByPosition(position: string): Promise<Player[]> {
    const allPlayers = await this.getAllPlayers();
    return allPlayers.filter(player => 
      player.position.toLowerCase() === position.toLowerCase()
    );
  }

  /**
   * Search players by name
   */
  static async searchPlayers(query: string): Promise<Player[]> {
    const allPlayers = await this.getAllPlayers();
    const searchTerm = query.toLowerCase();
    
    return allPlayers.filter(player =>
      player.name.toLowerCase().includes(searchTerm) ||
      player.team.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Get player by ID
   */
  static async getPlayerById(id: string): Promise<Player | null> {
    const allPlayers = await this.getAllPlayers();
    return allPlayers.find(player => player.id === id) || null;
  }

  /**
   * Get top players by average
   */
  static async getTopPlayers(limit: number = 20): Promise<Player[]> {
    const allPlayers = await this.getAllPlayers();
    return allPlayers
      .filter(player => player.games_played && player.games_played > 3) // Only players with significant games
      .sort((a, b) => b.average - a.average)
      .slice(0, limit);
  }

  /**
   * Get cache statistics
   */
  static async getCacheStats(): Promise<{
    total_players: number;
    teams: string[];
    positions: string[];
    last_updated: string;
    cache_age_minutes: number;
  }> {
    const players = await this.getAllPlayers();
    const teams = [...new Set(players.map(p => p.team))].sort();
    const positions = [...new Set(players.map(p => p.position))].sort();
    
    return {
      total_players: players.length,
      teams,
      positions,
      last_updated: players[0]?.last_updated || 'Unknown',
      cache_age_minutes: Math.round((Date.now() - this.lastCacheUpdate) / 60000)
    };
  }

  /**
   * Refresh cache (force reload)
   */
  static refreshCache(): void {
    this.cachedPlayers = null;
    this.lastCacheUpdate = 0;
    console.log('🔄 Player cache cleared, will reload on next request');
  }
}

export default PlayerDataService;
