/* FantasyPro Theme System - Seamless Dark/Light Mode */

/* CSS Custom Properties for Theme Variables */
:root {
  /* Transition settings */
  --theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --theme-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Animation settings */
  --glow-intensity: 0.3;
  --shadow-intensity: 0.1;
  --border-opacity: 0.2;
}

/* Dark Theme (Default) */
:root,
:root.dark,
[data-theme="dark"] {
  /* Core Brand Colors - Consistent across themes */
  --brand-primary: #22c55e;
  --brand-primary-hover: #16a34a;
  --brand-primary-light: #4ade80;
  --brand-secondary: #3b82f6;
  --brand-accent: #f59e0b;
  --brand-danger: #ef4444;
  --brand-warning: #eab308;
  --brand-success: #10b981;
  
  /* Background Colors */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-elevated: #475569;
  --bg-overlay: rgba(15, 23, 42, 0.9);
  --bg-glass: rgba(255, 255, 255, 0.05);
  --bg-glass-strong: rgba(255, 255, 255, 0.1);
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #e2e8f0;
  --text-tertiary: #94a3b8;
  --text-quaternary: #64748b;
  --text-muted: #475569;
  --text-inverse: #0f172a;
  
  /* Border Colors */
  --border-primary: #334155;
  --border-secondary: #475569;
  --border-tertiary: #64748b;
  --border-focus: var(--brand-primary);
  --border-hover: #64748b;
  
  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Glow Effects */
  --glow-primary: 0 0 20px rgba(34, 197, 94, var(--glow-intensity));
  --glow-secondary: 0 0 20px rgba(59, 130, 246, var(--glow-intensity));
  --glow-accent: 0 0 20px rgba(245, 158, 11, var(--glow-intensity));
  --glow-danger: 0 0 20px rgba(239, 68, 68, var(--glow-intensity));
  
  /* Status Colors */
  --status-online-bg: rgba(34, 197, 94, 0.1);
  --status-online-border: rgba(34, 197, 94, 0.2);
  --status-offline-bg: rgba(239, 68, 68, 0.1);
  --status-offline-border: rgba(239, 68, 68, 0.2);
  --status-warning-bg: rgba(234, 179, 8, 0.1);
  --status-warning-border: rgba(234, 179, 8, 0.2);
}

/* Light Theme */
:root.light,
[data-theme="light"] {
  /* Background Colors - True Inversion */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;
  --bg-elevated: #cbd5e1;
  --bg-overlay: rgba(255, 255, 255, 0.9);
  --bg-glass: rgba(0, 0, 0, 0.05);
  --bg-glass-strong: rgba(0, 0, 0, 0.1);
  
  /* Text Colors - True Inversion */
  --text-primary: #0f172a;
  --text-secondary: #1e293b;
  --text-tertiary: #475569;
  --text-quaternary: #64748b;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;
  
  /* Border Colors - Adjusted for light theme */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-tertiary: #94a3b8;
  --border-focus: var(--brand-primary);
  --border-hover: #94a3b8;
  
  /* Shadow Colors - Adjusted for light theme */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Glow Effects - Reduced intensity for light theme */
  --glow-intensity: 0.15;
  --glow-primary: 0 0 15px rgba(34, 197, 94, var(--glow-intensity));
  --glow-secondary: 0 0 15px rgba(59, 130, 246, var(--glow-intensity));
  --glow-accent: 0 0 15px rgba(245, 158, 11, var(--glow-intensity));
  --glow-danger: 0 0 15px rgba(239, 68, 68, var(--glow-intensity));
  
  /* Status Colors - Adjusted for light theme */
  --status-online-bg: rgba(34, 197, 94, 0.08);
  --status-online-border: rgba(34, 197, 94, 0.15);
  --status-offline-bg: rgba(239, 68, 68, 0.08);
  --status-offline-border: rgba(239, 68, 68, 0.15);
  --status-warning-bg: rgba(234, 179, 8, 0.08);
  --status-warning-border: rgba(234, 179, 8, 0.15);
}

/* Theme Transition Animation */
.theme-transition * {
  transition: var(--theme-transition);
}

/* Prevent flash of unstyled content */
html {
  color-scheme: dark light;
}

html.dark {
  color-scheme: dark;
}

html.light {
  color-scheme: light;
}

/* Base body styling with theme variables */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: var(--theme-transition);
}

/* Theme-aware component classes */
.themed-bg-primary {
  background-color: var(--bg-primary);
}

.themed-bg-secondary {
  background-color: var(--bg-secondary);
}

.themed-bg-tertiary {
  background-color: var(--bg-tertiary);
}

.themed-text-primary {
  color: var(--text-primary);
}

.themed-text-secondary {
  color: var(--text-secondary);
}

.themed-text-tertiary {
  color: var(--text-tertiary);
}

.themed-border {
  border-color: var(--border-primary);
}

.themed-border-hover:hover {
  border-color: var(--border-hover);
}

/* Theme-aware shadows */
.themed-shadow-sm {
  box-shadow: var(--shadow-sm);
}

.themed-shadow-md {
  box-shadow: var(--shadow-md);
}

.themed-shadow-lg {
  box-shadow: var(--shadow-lg);
}

.themed-shadow-xl {
  box-shadow: var(--shadow-xl);
}

/* Theme-aware glow effects */
.themed-glow-primary {
  box-shadow: var(--glow-primary);
}

.themed-glow-secondary {
  box-shadow: var(--glow-secondary);
}

.themed-glow-accent {
  box-shadow: var(--glow-accent);
}

.themed-glow-danger {
  box-shadow: var(--glow-danger);
}

/* Theme-aware glass morphism */
.themed-glass {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-primary);
}

.themed-glass-strong {
  background: var(--bg-glass-strong);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-secondary);
}

/* Theme-aware status indicators */
.themed-status-online {
  background: var(--status-online-bg);
  border: 1px solid var(--status-online-border);
  color: var(--brand-primary);
}

.themed-status-offline {
  background: var(--status-offline-bg);
  border: 1px solid var(--status-offline-border);
  color: var(--brand-danger);
}

.themed-status-warning {
  background: var(--status-warning-bg);
  border: 1px solid var(--status-warning-border);
  color: var(--brand-warning);
}

/* Smooth transitions for all themed elements */
[class*="themed-"] {
  transition: var(--theme-transition);
}

/* Special handling for images and media in light mode */
:root.light img:not(.theme-ignore) {
  filter: brightness(0.9) contrast(1.1);
}

:root.light .logo-invert {
  filter: invert(1) brightness(0.9);
}

/* Print styles - always light */
@media print {
  :root {
    --bg-primary: #ffffff !important;
    --bg-secondary: #ffffff !important;
    --text-primary: #000000 !important;
    --text-secondary: #333333 !important;
    --border-primary: #cccccc !important;
  }
}
