import React, { useState } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import Layout from '../components/Layout';
import { testSportRadarAPI, testSpecificEndpoint, testCORS, testViaProxy } from '../utils/testSportRadar';

const APITest: NextPage = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [logs, setLogs] = useState<string[]>([]);

  // Capture console logs
  const originalLog = console.log;
  const captureLog = (...args: any[]) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    setLogs(prev => [...prev, message]);
    originalLog(...args);
  };

  const runFullTest = async () => {
    setTesting(true);
    setLogs([]);
    setResults(null);
    
    // Override console.log temporarily
    console.log = captureLog;
    
    try {
      const testResults = await testSportRadarAPI();
      setResults(testResults);
    } catch (error) {
      captureLog('💥 Test failed:', error);
    } finally {
      // Restore console.log
      console.log = originalLog;
      setTesting(false);
    }
  };

  const runQuickTest = async () => {
    setTesting(true);
    setLogs([]);
    
    console.log = captureLog;
    
    try {
      await testSpecificEndpoint();
    } catch (error) {
      captureLog('💥 Quick test failed:', error);
    } finally {
      console.log = originalLog;
      setTesting(false);
    }
  };

  const runCORSTest = async () => {
    setTesting(true);
    setLogs([]);

    console.log = captureLog;

    try {
      await testCORS();
    } catch (error) {
      captureLog('💥 CORS test failed:', error);
    } finally {
      console.log = originalLog;
      setTesting(false);
    }
  };

  const runProxyTest = async () => {
    setTesting(true);
    setLogs([]);

    console.log = captureLog;

    try {
      const proxyResults = await testViaProxy();
      setResults(proxyResults);
    } catch (error) {
      captureLog('💥 Proxy test failed:', error);
    } finally {
      console.log = originalLog;
      setTesting(false);
    }
  };

  return (
    <Layout>
      <Head>
        <title>SportRadar API Test - FantasyPro</title>
      </Head>

      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold themed-text-primary">SportRadar API Test</h1>
          <p className="themed-text-tertiary mt-1">Test real SportRadar NRL API connectivity</p>
        </div>

        {/* Test Controls */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold themed-text-primary mb-4">API Tests</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={runQuickTest}
              disabled={testing}
              className="btn-primary btn-ripple"
            >
              {testing ? 'Testing...' : 'Quick Test'}
            </button>
            <button
              onClick={runCORSTest}
              disabled={testing}
              className="btn-secondary btn-ripple"
            >
              {testing ? 'Testing...' : 'CORS Test'}
            </button>
            <button
              onClick={runProxyTest}
              disabled={testing}
              className="btn-accent btn-ripple"
            >
              {testing ? 'Testing...' : 'Proxy Test'}
            </button>
            <button
              onClick={runFullTest}
              disabled={testing}
              className="btn-outline btn-ripple"
            >
              {testing ? 'Testing...' : 'Full Test'}
            </button>
          </div>
        </div>

        {/* API Key Info */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold themed-text-primary mb-4">API Configuration</h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="themed-text-secondary">API Key:</span>
              <span className="themed-text-primary font-mono text-sm">aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN</span>
            </div>
            <div className="flex justify-between">
              <span className="themed-text-secondary">Base URL:</span>
              <span className="themed-text-primary font-mono text-sm">api.sportradar.com/rugby-league/trial/v3</span>
            </div>
            <div className="flex justify-between">
              <span className="themed-text-secondary">Status:</span>
              <span className="status-live">Trial Key Active</span>
            </div>
          </div>
        </div>

        {/* Live Logs */}
        {logs.length > 0 && (
          <div className="card p-6">
            <h2 className="text-xl font-semibold themed-text-primary mb-4">Live Test Logs</h2>
            <div className="bg-slate-900 rounded-lg p-4 max-h-96 overflow-y-auto">
              <pre className="text-sm text-green-400 font-mono whitespace-pre-wrap">
                {logs.join('\n')}
              </pre>
            </div>
          </div>
        )}

        {/* Results Summary */}
        {results && (
          <div className="card p-6">
            <h2 className="text-xl font-semibold themed-text-primary mb-4">Test Results</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center p-4 bg-green-500/10 rounded-lg">
                <div className="text-2xl font-bold text-green-400">
                  {results.filter((r: any) => r.success).length}
                </div>
                <div className="text-sm themed-text-secondary">Successful</div>
              </div>
              <div className="text-center p-4 bg-red-500/10 rounded-lg">
                <div className="text-2xl font-bold text-red-400">
                  {results.filter((r: any) => !r.success).length}
                </div>
                <div className="text-sm themed-text-secondary">Failed</div>
              </div>
              <div className="text-center p-4 bg-blue-500/10 rounded-lg">
                <div className="text-2xl font-bold text-blue-400">
                  {results.length}
                </div>
                <div className="text-sm themed-text-secondary">Total Tests</div>
              </div>
            </div>

            <div className="space-y-4">
              {results.map((result: any, index: number) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${
                    result.success
                      ? 'bg-green-500/10 border-green-500/20'
                      : 'bg-red-500/10 border-red-500/20'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-mono text-sm themed-text-primary">
                      {result.endpoint}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      result.success ? 'bg-green-600/20 text-green-400' : 'bg-red-600/20 text-red-400'
                    }`}>
                      {result.success ? 'SUCCESS' : 'FAILED'}
                    </span>
                  </div>
                  
                  {result.success && result.data && (
                    <div className="mt-2">
                      <div className="text-sm themed-text-secondary mb-1">Response Data Keys:</div>
                      <div className="text-xs font-mono themed-text-tertiary">
                        {Object.keys(result.data).join(', ')}
                      </div>
                    </div>
                  )}
                  
                  {!result.success && (
                    <div className="mt-2">
                      <div className="text-sm text-red-400">
                        {result.error || `HTTP ${result.status}`}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold themed-text-primary mb-4">What This Test Does</h2>
          <div className="space-y-3 themed-text-secondary">
            <p><strong>Quick Test:</strong> Tests a single endpoint to check basic connectivity</p>
            <p><strong>CORS Test:</strong> Checks if browser CORS policy blocks the requests</p>
            <p><strong>Full Test:</strong> Tests multiple common SportRadar endpoint patterns</p>
          </div>
          
          <div className="mt-6 p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
            <h3 className="font-semibold text-blue-400 mb-2">Expected Outcomes:</h3>
            <ul className="space-y-1 text-sm themed-text-tertiary">
              <li>✅ <strong>Success:</strong> API works, we can integrate real data immediately</li>
              <li>❌ <strong>CORS Error:</strong> Need to create server-side proxy</li>
              <li>❌ <strong>404 Error:</strong> Need to find correct endpoint structure</li>
              <li>❌ <strong>401/403 Error:</strong> API key or permissions issue</li>
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default APITest;
