#!/usr/bin/env python3
"""
SuperCoach API Endpoints

FastAPI endpoints for serving NRL SuperCoach data to the frontend.
Provides comprehensive player statistics, team data, and analysis.
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime
import logging
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.supercoach_db import supercoach_db, SuperCoachPlayer, NRLTeam
from scrapers.nrl_supercoach_scraper import NRLSuperCoachScraper
from agents.supercoach_agent import supercoach_agent
from analysis.supercoach_algorithms import supercoach_analyzer
from utils.logging_config import setup_logging

logger = setup_logging(__name__)

# Create router
router = APIRouter(prefix="/supercoach", tags=["SuperCoach"])

# Pydantic models for API responses
class PlayerResponse(BaseModel):
    id: int
    name: str
    team: str
    position: str
    current_price: Optional[float] = None
    current_breakeven: Optional[int] = None
    season_points: Optional[int] = None
    season_average: Optional[float] = None
    games_played: Optional[int] = None
    form_rating: Optional[float] = None
    ownership_percentage: Optional[float] = None
    minutes_per_game: Optional[float] = None
    consistency_rating: Optional[float] = None
    value_score: Optional[float] = None
    season_high: Optional[int] = None
    season_low: Optional[int] = None
    recent_scores: Optional[List[int]] = None
    injury_status: Optional[str] = None
    last_updated: Optional[datetime] = None

    @classmethod
    def from_player(cls, player):
        """Create PlayerResponse from SuperCoachPlayer model"""
        return cls(
            id=player.id,
            name=player.name,
            team=player.team.name if player.team else "Unknown",
            position=player.position or "Unknown",
            current_price=player.current_price,
            current_breakeven=player.current_breakeven,
            season_points=player.season_points,
            season_average=player.season_average,
            games_played=player.games_played,
            form_rating=player.form_rating,
            ownership_percentage=player.ownership_percentage,
            minutes_per_game=player.minutes_per_game,
            consistency_rating=player.consistency_rating,
            value_score=player.value_score,
            season_high=player.season_high,
            season_low=player.season_low,
            recent_scores=player.recent_scores,
            injury_status=player.injury_status,
            last_updated=player.last_updated
        )

    class Config:
        from_attributes = True

class TeamResponse(BaseModel):
    id: int
    name: str
    abbreviation: str
    city: Optional[str] = None
    player_count: Optional[int] = None
    total_value: Optional[float] = None
    average_breakeven: Optional[float] = None
    total_points: Optional[int] = None

    class Config:
        from_attributes = True

class PriceChangeResponse(BaseModel):
    player_name: str
    team: str
    position: str
    price_before: float
    price_after: float
    price_change: float
    breakeven_before: Optional[int] = None
    breakeven_after: Optional[int] = None
    round_number: int

class DashboardResponse(BaseModel):
    total_players: int
    active_players: int
    total_teams: int
    last_updated: datetime
    top_scorers: List[PlayerResponse]
    price_risers: List[PlayerResponse]
    price_fallers: List[PlayerResponse]
    best_value: List[PlayerResponse]
    injury_watch: List[PlayerResponse]

class TradeRecommendationResponse(BaseModel):
    player_out: PlayerResponse
    player_in: PlayerResponse
    price_difference: float
    expected_points_gain: float
    risk_rating: str
    reasoning: str

# Dependency to get database session
def get_db():
    return supercoach_db

@router.get("/dashboard", response_model=DashboardResponse)
async def get_dashboard_data(db = Depends(get_db)):
    """Get comprehensive dashboard data"""
    try:
        # Get basic stats
        all_players = db.get_all_players()
        active_players = [p for p in all_players if p.is_active]
        
        # Get top performers
        top_scorers = db.get_top_players_by_metric('points', limit=5)
        best_value = db.get_top_players_by_metric('value', limit=5)
        
        # Get price changes
        price_risers, price_fallers = db.get_price_risers_fallers(limit=5)
        
        # Get injury watch (players with injury status)
        injury_watch = [p for p in active_players if p.injury_status and p.injury_status != 'Fit'][:5]
        
        return DashboardResponse(
            total_players=len(all_players),
            active_players=len(active_players),
            total_teams=17,  # NRL teams
            last_updated=datetime.utcnow(),
            top_scorers=[PlayerResponse.from_player(p) for p in top_scorers],
            price_risers=[PlayerResponse.from_player(p) for p in price_risers],
            price_fallers=[PlayerResponse.from_player(p) for p in price_fallers],
            best_value=[PlayerResponse.from_player(p) for p in best_value],
            injury_watch=[PlayerResponse.from_player(p) for p in injury_watch]
        )
        
    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving dashboard data")

@router.get("/players", response_model=List[PlayerResponse])
async def get_players(
    team_id: Optional[int] = Query(None, description="Filter by team ID"),
    position: Optional[str] = Query(None, description="Filter by position"),
    min_price: Optional[float] = Query(None, description="Minimum price filter"),
    max_price: Optional[float] = Query(None, description="Maximum price filter"),
    sort_by: str = Query("name", description="Sort by field"),
    limit: int = Query(50, le=200, description="Limit results"),
    offset: int = Query(0, description="Offset for pagination"),
    db = Depends(get_db)
):
    """Get players with filtering and pagination"""
    try:
        players = db.get_all_players(team_id=team_id, position=position)
        
        # Apply price filters
        if min_price is not None:
            players = [p for p in players if p.current_price and p.current_price >= min_price]
        if max_price is not None:
            players = [p for p in players if p.current_price and p.current_price <= max_price]
        
        # Sort players
        if sort_by == "price":
            players.sort(key=lambda x: x.current_price or 0, reverse=True)
        elif sort_by == "points":
            players.sort(key=lambda x: x.season_points or 0, reverse=True)
        elif sort_by == "average":
            players.sort(key=lambda x: x.season_average or 0, reverse=True)
        elif sort_by == "form":
            players.sort(key=lambda x: x.form_rating or 0, reverse=True)
        else:  # default to name
            players.sort(key=lambda x: x.name)
        
        # Apply pagination
        paginated_players = players[offset:offset + limit]
        
        return [PlayerResponse.from_player(p) for p in paginated_players]
        
    except Exception as e:
        logger.error(f"Error getting players: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving players")

@router.get("/players/{player_id}", response_model=PlayerResponse)
async def get_player(player_id: int, db = Depends(get_db)):
    """Get specific player by ID"""
    try:
        with db.get_session() as session:
            player = session.query(SuperCoachPlayer).filter_by(id=player_id).first()
            if not player:
                raise HTTPException(status_code=404, detail="Player not found")
            
            return PlayerResponse.from_player(player)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting player {player_id}: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving player")

@router.get("/players/search/{name}", response_model=List[PlayerResponse])
async def search_players(name: str, db = Depends(get_db)):
    """Search players by name"""
    try:
        with db.get_session() as session:
            players = session.query(SuperCoachPlayer).filter(
                SuperCoachPlayer.name.ilike(f"%{name}%"),
                SuperCoachPlayer.is_active == True
            ).limit(20).all()
            
            return [PlayerResponse.from_player(p) for p in players]
            
    except Exception as e:
        logger.error(f"Error searching players: {e}")
        raise HTTPException(status_code=500, detail="Error searching players")

@router.get("/teams", response_model=List[TeamResponse])
async def get_teams(db = Depends(get_db)):
    """Get all NRL teams with statistics"""
    try:
        with db.get_session() as session:
            teams = session.query(NRLTeam).all()
            team_responses = []
            
            for team in teams:
                team_stats = db.get_team_stats(team.id)
                team_response = TeamResponse(
                    id=team.id,
                    name=team.name,
                    abbreviation=team.abbreviation,
                    city=team.city,
                    player_count=team_stats.get('player_count', 0),
                    total_value=team_stats.get('total_value', 0),
                    average_breakeven=team_stats.get('average_breakeven', 0),
                    total_points=team_stats.get('total_points', 0)
                )
                team_responses.append(team_response)
            
            return team_responses
            
    except Exception as e:
        logger.error(f"Error getting teams: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving teams")

@router.get("/teams/{team_id}/players", response_model=List[PlayerResponse])
async def get_team_players(team_id: int, db = Depends(get_db)):
    """Get all players for a specific team"""
    try:
        players = db.get_all_players(team_id=team_id)
        return [PlayerResponse.from_player(p) for p in players]
        
    except Exception as e:
        logger.error(f"Error getting team players: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving team players")

@router.get("/price-changes", response_model=List[PriceChangeResponse])
async def get_price_changes(
    limit: int = Query(20, le=100, description="Limit results"),
    db = Depends(get_db)
):
    """Get recent price changes"""
    try:
        price_risers, price_fallers = db.get_price_risers_fallers(limit=limit//2)
        
        changes = []
        for player in price_risers:
            if player.price_history:
                latest_change = player.price_history[-1]
                changes.append(PriceChangeResponse(
                    player_name=player.name,
                    team=player.team.name,
                    position=player.position,
                    price_before=latest_change.price_before,
                    price_after=latest_change.price_after,
                    price_change=latest_change.price_change,
                    breakeven_before=latest_change.breakeven_before,
                    breakeven_after=latest_change.breakeven_after,
                    round_number=latest_change.round_number
                ))
        
        for player in price_fallers:
            if player.price_history:
                latest_change = player.price_history[-1]
                changes.append(PriceChangeResponse(
                    player_name=player.name,
                    team=player.team.name,
                    position=player.position,
                    price_before=latest_change.price_before,
                    price_after=latest_change.price_after,
                    price_change=latest_change.price_change,
                    breakeven_before=latest_change.breakeven_before,
                    breakeven_after=latest_change.breakeven_after,
                    round_number=latest_change.round_number
                ))
        
        return changes
        
    except Exception as e:
        logger.error(f"Error getting price changes: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving price changes")

@router.get("/positions/{position}/top", response_model=List[PlayerResponse])
async def get_top_by_position(
    position: str,
    metric: str = Query("points", description="Metric to sort by"),
    limit: int = Query(10, le=50, description="Limit results"),
    db = Depends(get_db)
):
    """Get top players by position and metric"""
    try:
        players = db.get_top_players_by_metric(metric, limit=limit, position=position)
        return [PlayerResponse.from_player(p) for p in players]
        
    except Exception as e:
        logger.error(f"Error getting top players by position: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving top players")

@router.post("/scrape/update")
async def trigger_data_update(db = Depends(get_db)):
    """Trigger a data scraping update"""
    try:
        async with NRLSuperCoachScraper() as scraper:
            scraped_data = await scraper.scrape_all_data()
            
            if scraped_data:
                success = db.store_scraped_data(scraped_data)
                if success:
                    return {
                        "status": "success",
                        "message": "Data updated successfully",
                        "players_updated": len(scraped_data.get('combined_players', [])),
                        "timestamp": datetime.utcnow()
                    }
                else:
                    raise HTTPException(status_code=500, detail="Failed to store scraped data")
            else:
                raise HTTPException(status_code=500, detail="Failed to scrape data")
                
    except Exception as e:
        logger.error(f"Error updating data: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating data: {str(e)}")

@router.get("/stats/summary")
async def get_stats_summary(db = Depends(get_db)):
    """Get summary statistics"""
    try:
        all_players = db.get_all_players()
        active_players = [p for p in all_players if p.is_active]
        
        total_value = sum(p.current_price or 0 for p in active_players)
        avg_price = total_value / len(active_players) if active_players else 0
        
        total_points = sum(p.season_points or 0 for p in active_players)
        avg_points = total_points / len(active_players) if active_players else 0
        
        return {
            "total_players": len(all_players),
            "active_players": len(active_players),
            "total_value": total_value,
            "average_price": avg_price,
            "total_points": total_points,
            "average_points": avg_points,
            "last_updated": datetime.utcnow()
        }

    except Exception as e:
        logger.error(f"Error getting stats summary: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving stats summary")

# =============================================================================
# AI AGENT ENDPOINTS
# =============================================================================

@router.get("/ai/recommendations")
async def get_ai_recommendations(
    user_team: Optional[str] = Query(None, description="Comma-separated player IDs"),
    limit: int = Query(10, le=50, description="Limit results")
):
    """Get AI-powered recommendations"""
    try:
        # Parse user team if provided
        team_ids = []
        if user_team:
            try:
                team_ids = [int(id.strip()) for id in user_team.split(',')]
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid user team format")

        recommendations = supercoach_agent.generate_weekly_recommendations(team_ids if team_ids else None)

        # Convert to API response format
        api_recommendations = []
        for rec in recommendations[:limit]:
            api_recommendations.append({
                "type": rec.type,
                "title": rec.title,
                "description": rec.description,
                "confidence": rec.confidence,
                "priority": rec.priority,
                "data": rec.data,
                "reasoning": rec.reasoning,
                "created_at": rec.created_at.isoformat()
            })

        return {
            "recommendations": api_recommendations,
            "total_count": len(recommendations),
            "generated_at": datetime.utcnow().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting AI recommendations: {e}")
        raise HTTPException(status_code=500, detail="Error generating recommendations")

@router.get("/ai/player-insights/{player_id}")
async def get_player_insights(player_id: int):
    """Get comprehensive AI insights for a specific player"""
    try:
        insights = supercoach_agent.get_player_insights(player_id)

        if not insights:
            raise HTTPException(status_code=404, detail="Player not found or no insights available")

        return insights

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting player insights: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving player insights")

@router.get("/ai/price-predictions")
async def get_price_predictions(
    limit: int = Query(20, le=100, description="Limit results"),
    min_change: float = Query(10000, description="Minimum price change to include")
):
    """Get AI price predictions"""
    try:
        predictions = supercoach_analyzer.predict_price_changes()

        # Filter by minimum change
        filtered_predictions = [p for p in predictions if abs(p.price_change) >= min_change]

        # Convert to API response
        api_predictions = []
        for pred in filtered_predictions[:limit]:
            with supercoach_db.get_session() as session:
                player = session.query(SuperCoachPlayer).filter_by(id=pred.player_id).first()
                if player:
                    api_predictions.append({
                        "player_id": pred.player_id,
                        "player_name": player.name,
                        "team": player.team.name,
                        "position": player.position,
                        "current_price": pred.current_price,
                        "predicted_price": pred.predicted_price,
                        "price_change": pred.price_change,
                        "confidence": pred.confidence,
                        "breakeven_required": pred.breakeven_required,
                        "probability_increase": pred.probability_increase,
                        "probability_decrease": pred.probability_decrease
                    })

        return {
            "predictions": api_predictions,
            "total_count": len(filtered_predictions),
            "generated_at": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting price predictions: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving price predictions")

@router.get("/ai/form-analysis")
async def get_form_analysis(
    position: Optional[str] = Query(None, description="Filter by position"),
    trend: Optional[str] = Query(None, description="Filter by form trend"),
    limit: int = Query(20, le=100, description="Limit results")
):
    """Get form analysis for players"""
    try:
        players = supercoach_db.get_all_players(position=position)

        form_analyses = []
        for player in players:
            form_analysis = supercoach_analyzer.analyze_player_form(player.id)
            if form_analysis:
                # Filter by trend if specified
                if trend and form_analysis.trend.value != trend:
                    continue

                form_analyses.append({
                    "player_id": player.id,
                    "player_name": player.name,
                    "team": player.team.name,
                    "position": player.position,
                    "current_form": form_analysis.current_form,
                    "trend": form_analysis.trend.value,
                    "consistency": form_analysis.consistency,
                    "recent_average": form_analysis.recent_average,
                    "season_average": form_analysis.season_average,
                    "momentum_score": form_analysis.momentum_score,
                    "risk_level": form_analysis.risk_level.value
                })

        # Sort by momentum score
        form_analyses.sort(key=lambda x: x["momentum_score"], reverse=True)

        return {
            "form_analysis": form_analyses[:limit],
            "total_count": len(form_analyses),
            "generated_at": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting form analysis: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving form analysis")

@router.post("/ai/update-data")
async def trigger_ai_data_update():
    """Trigger AI agent to update data from NRL SuperCoach Stats"""
    try:
        success = await supercoach_agent.update_data()

        if success:
            return {
                "status": "success",
                "message": "AI agent data updated successfully",
                "last_update": supercoach_agent.last_update.isoformat() if supercoach_agent.last_update else None,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to update AI agent data")

    except Exception as e:
        logger.error(f"Error updating AI data: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating AI data: {str(e)}")
