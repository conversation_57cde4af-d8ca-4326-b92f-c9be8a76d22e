#!/usr/bin/env python3
"""
Debug the exact table structure to understand player data layout
"""

import asyncio
import aiohttp
from bs4 import BeautifulSoup
import re

async def debug_table_structure():
    """Debug the table structure in detail"""
    url = "https://www.nrlsupercoachstats.com/TeamBEs.php"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    content = await response.text()
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    # Find the table with actual player data
                    tables = soup.find_all('table')
                    print(f"Found {len(tables)} tables")
                    
                    # Look for the table that contains player data
                    for i, table in enumerate(tables):
                        rows = table.find_all('tr')
                        print(f"\n📋 Table {i+1}: {len(rows)} rows")
                        
                        # Check if this table has team images
                        team_images = table.find_all('img', src=re.compile(r'TeamPictures/\w+\.png'))
                        if team_images:
                            print(f"   🖼️ Found {len(team_images)} team images")
                            
                            # Look at the structure around the first team
                            first_team_img = team_images[0]
                            team_match = re.search(r'TeamPictures/(\w+)\.png', first_team_img['src'])
                            if team_match:
                                team_abbr = team_match.group(1)
                                print(f"   🏈 First team: {team_abbr}")
                                
                                # Find the row containing this image
                                team_row = first_team_img.find_parent('tr')
                                if team_row:
                                    cells = team_row.find_all(['td', 'th'])
                                    print(f"   📊 Team row has {len(cells)} cells")
                                    
                                    for j, cell in enumerate(cells):
                                        cell_text = cell.get_text(strip=True)
                                        print(f"      Cell {j+1}: '{cell_text[:100]}{'...' if len(cell_text) > 100 else ''}'")
                                        
                                        # Look for player patterns
                                        if ',' in cell_text and len(cell_text) < 50:
                                            print(f"         🎯 Potential player data: '{cell_text}'")
                                
                                # Also check the next few rows
                                next_rows = team_row.find_next_siblings('tr')[:3]
                                for k, next_row in enumerate(next_rows):
                                    next_cells = next_row.find_all(['td', 'th'])
                                    print(f"   📊 Next row {k+1} has {len(next_cells)} cells")
                                    
                                    for j, cell in enumerate(next_cells):
                                        cell_text = cell.get_text(strip=True)
                                        if cell_text and len(cell_text) < 100:
                                            print(f"      Cell {j+1}: '{cell_text}'")
                                            
                                            # Look for player patterns
                                            if ',' in cell_text and len(cell_text) < 50:
                                                print(f"         🎯 Potential player data: '{cell_text}'")
                        
                        # Only show details for first few tables with team images
                        if team_images and i >= 2:
                            break
                    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_table_structure())
