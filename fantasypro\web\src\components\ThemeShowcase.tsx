import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';
import ThemeToggle from './ThemeToggle';
import {
  SparklesIcon,
  SunIcon,
  MoonIcon,
  SwatchIcon,
  EyeIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const ThemeShowcase: React.FC = () => {
  const { resolvedTheme } = useTheme();

  const features = [
    {
      icon: SparklesIcon,
      title: 'Seamless Transitions',
      description: 'Smooth animations between themes with no jarring changes'
    },
    {
      icon: SwatchIcon,
      title: 'True Inversion',
      description: 'Light mode is a complete inversion, not an afterthought'
    },
    {
      icon: EyeIcon,
      title: 'Preserved Identity',
      description: 'Core FantasyPro branding remains consistent across themes'
    },
    {
      icon: CheckCircleIcon,
      title: 'System Aware',
      description: 'Automatically respects your system preference'
    }
  ];

  const colorPalette = [
    { name: 'Primary', class: 'bg-green-500', description: 'Brand Green' },
    { name: 'Secondary', class: 'bg-blue-500', description: 'Accent Blue' },
    { name: 'Warning', class: 'bg-yellow-500', description: 'Alert Yellow' },
    { name: 'Danger', class: 'bg-red-500', description: 'Error Red' },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-8"
    >
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.div
          className="inline-flex items-center space-x-2 px-4 py-2 rounded-full themed-bg-secondary themed-border"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          {resolvedTheme === 'dark' ? (
            <MoonIcon className="w-5 h-5 text-blue-400" />
          ) : (
            <SunIcon className="w-5 h-5 text-yellow-500" />
          )}
          <span className="text-sm font-medium themed-text-primary">
            {resolvedTheme === 'dark' ? 'Dark Mode Active' : 'Light Mode Active'}
          </span>
        </motion.div>
        
        <h2 className="text-2xl font-bold themed-text-primary">
          FantasyPro Theme System
        </h2>
        <p className="themed-text-tertiary max-w-2xl mx-auto">
          Experience seamless dark and light modes that preserve the core FantasyPro identity 
          while providing a true inversion experience, not an afterthought.
        </p>
      </div>

      {/* Theme Controls */}
      <div className="flex justify-center space-x-4">
        <ThemeToggle variant="button" size="lg" showLabel />
        <ThemeToggle variant="dropdown" size="lg" showLabel />
        <ThemeToggle variant="switch" size="lg" showLabel />
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {features.map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="card-premium magnetic p-6 text-center space-y-4"
          >
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-green-500/10 themed-border">
              <feature.icon className="w-6 h-6 text-green-500" />
            </div>
            <h3 className="font-semibold themed-text-primary">{feature.title}</h3>
            <p className="text-sm themed-text-tertiary">{feature.description}</p>
          </motion.div>
        ))}
      </div>

      {/* Color Palette */}
      <div className="card-premium p-6">
        <h3 className="text-lg font-semibold themed-text-primary mb-4">
          Consistent Brand Colors
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {colorPalette.map((color, index) => (
            <motion.div
              key={color.name}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="text-center space-y-2"
            >
              <div className={`w-16 h-16 rounded-lg ${color.class} mx-auto shadow-lg`} />
              <div>
                <div className="font-medium themed-text-primary text-sm">{color.name}</div>
                <div className="text-xs themed-text-tertiary">{color.description}</div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Component Examples */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Buttons */}
        <div className="card-premium p-6 space-y-4">
          <h3 className="text-lg font-semibold themed-text-primary">Button Variants</h3>
          <div className="space-y-3">
            <button className="btn-primary btn-ripple w-full">Primary Button</button>
            <button className="btn-secondary w-full">Secondary Button</button>
            <button className="btn-outline w-full">Outline Button</button>
            <button className="btn-danger w-full">Danger Button</button>
          </div>
        </div>

        {/* Status Indicators */}
        <div className="card-premium p-6 space-y-4">
          <h3 className="text-lg font-semibold themed-text-primary">Status Indicators</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="themed-text-secondary">System Status</span>
              <span className="themed-status-online px-3 py-1 rounded-full text-xs font-medium">
                Online
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="themed-text-secondary">Data Pipeline</span>
              <span className="themed-status-warning px-3 py-1 rounded-full text-xs font-medium">
                Warning
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="themed-text-secondary">API Connection</span>
              <span className="themed-status-offline px-3 py-1 rounded-full text-xs font-medium">
                Offline
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Interactive Elements */}
      <div className="card-premium p-6 space-y-4">
        <h3 className="text-lg font-semibold themed-text-primary">Interactive Elements</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium themed-text-secondary">Search Players</label>
            <input
              type="text"
              placeholder="Enter player name..."
              className="input-primary w-full"
            />
          </div>

          {/* Select */}
          <div className="space-y-2">
            <label className="text-sm font-medium themed-text-secondary">Position</label>
            <select className="input-primary w-full">
              <option>All Positions</option>
              <option>Fullback</option>
              <option>Winger</option>
              <option>Centre</option>
            </select>
          </div>

          {/* Toggle */}
          <div className="space-y-2">
            <label className="text-sm font-medium themed-text-secondary">Live Updates</label>
            <div className="flex items-center space-x-2">
              <input type="checkbox" className="rounded" defaultChecked />
              <span className="text-sm themed-text-tertiary">Enable notifications</span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Note */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="text-center p-4 themed-bg-secondary rounded-lg themed-border"
      >
        <p className="text-sm themed-text-tertiary">
          <SparklesIcon className="w-4 h-4 inline mr-1" />
          Theme transitions use CSS custom properties and hardware acceleration for optimal performance
        </p>
      </motion.div>
    </motion.div>
  );
};

export default ThemeShowcase;
