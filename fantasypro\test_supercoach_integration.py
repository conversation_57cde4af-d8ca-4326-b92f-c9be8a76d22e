#!/usr/bin/env python3
"""
SuperCoach Integration Test Script

Test script to verify the complete SuperCoach integration including:
- Data scraping from NRL SuperCoach Stats
- Database storage and retrieval
- Analysis algorithms
- AI agent recommendations
- API endpoints
"""

import asyncio
import sys
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logging_config import setup_logging

logger = setup_logging(__name__)

async def test_scraper():
    """Test the NRL SuperCoach Stats scraper"""
    print("\n🔍 Testing NRL SuperCoach Stats Scraper...")
    
    try:
        from scrapers.nrl_supercoach_scraper import NRLSuperCoachScraper
        
        async with NRLSuperCoachScraper() as scraper:
            print("  ✅ Scraper initialized successfully")
            
            # Test individual methods
            print("  📊 Testing team breakevens scraping...")
            breakevens = await scraper.scrape_team_breakevens()
            print(f"  ✅ Scraped breakevens for {len(breakevens)} teams")
            
            print("  💰 Testing team prices scraping...")
            prices = await scraper.scrape_team_prices()
            print(f"  ✅ Scraped prices for {len(prices)} teams")
            
            # Test comprehensive scrape (limited for testing)
            print("  🔄 Testing comprehensive data scrape...")
            all_data = await scraper.scrape_all_data()
            
            if all_data:
                players_count = len(all_data.get('combined_players', []))
                fixtures_count = len(all_data.get('fixtures', []))
                print(f"  ✅ Comprehensive scrape successful:")
                print(f"    - Players: {players_count}")
                print(f"    - Fixtures: {fixtures_count}")
                return all_data
            else:
                print("  ❌ Comprehensive scrape failed")
                return None
                
    except Exception as e:
        print(f"  ❌ Scraper test failed: {e}")
        return None

def test_database():
    """Test database operations"""
    print("\n🗄️ Testing Database Operations...")
    
    try:
        from database.supercoach_db import supercoach_db
        
        print("  ✅ Database connection established")
        
        # Test basic queries
        all_players = supercoach_db.get_all_players()
        print(f"  📊 Found {len(all_players)} players in database")
        
        if all_players:
            # Test specific player query
            first_player = all_players[0]
            player_by_name = supercoach_db.get_player_by_name(first_player.name)
            if player_by_name:
                print(f"  ✅ Successfully retrieved player: {player_by_name.name}")
            
            # Test top players query
            top_scorers = supercoach_db.get_top_players_by_metric('points', limit=5)
            print(f"  🏆 Top 5 scorers retrieved: {len(top_scorers)} players")
            
            # Test team stats
            if first_player.team_id:
                team_stats = supercoach_db.get_team_stats(first_player.team_id)
                print(f"  🏈 Team stats retrieved for team: {team_stats.get('team', {}).name if team_stats.get('team') else 'Unknown'}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Database test failed: {e}")
        return False

def test_analysis_algorithms():
    """Test analysis algorithms"""
    print("\n🧠 Testing Analysis Algorithms...")
    
    try:
        from analysis.supercoach_algorithms import supercoach_analyzer
        
        print("  ✅ Analysis engine initialized")
        
        # Test price predictions
        print("  💹 Testing price predictions...")
        price_predictions = supercoach_analyzer.predict_price_changes()
        print(f"  ✅ Generated {len(price_predictions)} price predictions")
        
        if price_predictions:
            top_prediction = price_predictions[0]
            print(f"    - Top prediction: Player {top_prediction.player_id}, "
                  f"Change: ${top_prediction.price_change:,.0f}, "
                  f"Confidence: {top_prediction.confidence:.1%}")
        
        # Test trade recommendations
        print("  🔄 Testing trade recommendations...")
        trade_recs = supercoach_analyzer.generate_trade_recommendations(limit=5)
        print(f"  ✅ Generated {len(trade_recs)} trade recommendations")
        
        if trade_recs:
            top_trade = trade_recs[0]
            print(f"    - Top trade: Player {top_trade.player_out_id} → {top_trade.player_in_id}, "
                  f"Expected gain: +{top_trade.expected_points_gain:.1f} pts")
        
        # Test form analysis
        print("  📈 Testing form analysis...")
        from database.supercoach_db import supercoach_db
        all_players = supercoach_db.get_all_players()
        
        if all_players:
            form_analysis = supercoach_analyzer.analyze_player_form(all_players[0].id)
            if form_analysis:
                print(f"  ✅ Form analysis completed for player {all_players[0].name}")
                print(f"    - Form: {form_analysis.current_form:.1f}/10")
                print(f"    - Trend: {form_analysis.trend.value}")
                print(f"    - Risk: {form_analysis.risk_level.value}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Analysis algorithms test failed: {e}")
        return False

async def test_ai_agent():
    """Test AI agent functionality"""
    print("\n🤖 Testing AI Agent...")
    
    try:
        from agents.supercoach_agent import supercoach_agent
        
        print("  ✅ AI agent initialized")
        
        # Test recommendations generation
        print("  💡 Testing recommendation generation...")
        recommendations = supercoach_agent.generate_weekly_recommendations()
        print(f"  ✅ Generated {len(recommendations)} recommendations")
        
        if recommendations:
            for i, rec in enumerate(recommendations[:3]):
                print(f"    {i+1}. {rec.type.upper()}: {rec.title} (Confidence: {rec.confidence:.1%})")
        
        # Test player insights
        print("  🔍 Testing player insights...")
        from database.supercoach_db import supercoach_db
        all_players = supercoach_db.get_all_players()
        
        if all_players:
            insights = supercoach_agent.get_player_insights(all_players[0].id)
            if insights:
                print(f"  ✅ Player insights generated for {insights.get('player', {}).get('name', 'Unknown')}")
                print(f"    - Recommendations: {len(insights.get('recommendations', []))}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ AI agent test failed: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints (basic import test)"""
    print("\n🌐 Testing API Endpoints...")
    
    try:
        from api.supercoach_endpoints import router
        
        print("  ✅ SuperCoach API router imported successfully")
        
        # Count endpoints
        endpoint_count = len([route for route in router.routes])
        print(f"  📡 Found {endpoint_count} API endpoints")
        
        # List some key endpoints
        key_endpoints = [
            "/supercoach/dashboard",
            "/supercoach/players",
            "/supercoach/ai/recommendations",
            "/supercoach/ai/price-predictions"
        ]
        
        for endpoint in key_endpoints:
            print(f"    - {endpoint}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ API endpoints test failed: {e}")
        return False

async def run_integration_test():
    """Run complete integration test"""
    print("🚀 Starting SuperCoach Integration Test")
    print("=" * 50)
    
    results = {}
    
    # Test scraper (optional - may fail if website is down)
    try:
        scraped_data = await test_scraper()
        results['scraper'] = scraped_data is not None
        
        # If scraping successful, store data in database
        if scraped_data:
            print("\n💾 Storing scraped data in database...")
            from database.supercoach_db import supercoach_db
            success = supercoach_db.store_scraped_data(scraped_data)
            if success:
                print("  ✅ Data stored successfully")
            else:
                print("  ⚠️ Data storage failed")
    except Exception as e:
        print(f"  ⚠️ Scraper test skipped: {e}")
        results['scraper'] = False
    
    # Test database
    results['database'] = test_database()
    
    # Test analysis algorithms
    results['analysis'] = test_analysis_algorithms()
    
    # Test AI agent
    results['ai_agent'] = await test_ai_agent()
    
    # Test API endpoints
    results['api'] = test_api_endpoints()
    
    # Summary
    print("\n📊 Integration Test Summary")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for component, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {component.replace('_', ' ').title()}: {'PASS' if status else 'FAIL'}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} components passed")
    
    if passed == total:
        print("🎉 All tests passed! SuperCoach integration is working correctly.")
    elif passed >= total * 0.8:
        print("⚠️ Most tests passed. Some components may need attention.")
    else:
        print("❌ Multiple components failed. Integration needs debugging.")
    
    return results

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing required dependencies...")
    
    import subprocess
    
    required_packages = [
        'aiohttp',
        'beautifulsoup4', 
        'pandas',
        'numpy',
        'sqlalchemy',
        'fastapi',
        'uvicorn',
        'pydantic'
    ]
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package} already installed")
        except ImportError:
            print(f"  📦 Installing {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"  ✅ {package} installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"  ❌ Failed to install {package}: {e}")

if __name__ == "__main__":
    print("🏈 FantasyPro SuperCoach Integration Test")
    print("=" * 50)
    
    # Install dependencies first
    install_dependencies()
    
    # Run the integration test
    asyncio.run(run_integration_test())
