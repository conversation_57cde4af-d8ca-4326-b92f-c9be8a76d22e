#!/usr/bin/env python3
"""
Debug scraper to understand the HTML structure
"""

import asyncio
import aiohttp
from bs4 import BeautifulSoup
import re

async def debug_team_breakevens():
    """Debug the team breakevens page structure"""
    url = "https://www.nrlsupercoachstats.com/TeamBEs.php"
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"🔍 Debugging {url}")
            
            async with session.get(url) as response:
                if response.status == 200:
                    content = await response.text()
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    print(f"📄 Content length: {len(content)} characters")
                    
                    # Find team images
                    team_images = soup.find_all('img', src=re.compile(r'TeamPictures/\w+\.png'))
                    print(f"🖼️ Found {len(team_images)} team images:")
                    
                    for i, img in enumerate(team_images[:5]):  # Show first 5
                        src = img.get('src', '')
                        print(f"   {i+1}. {src}")
                        
                        # Find the parent container
                        parent = img.find_parent()
                        if parent:
                            print(f"      Parent tag: {parent.name}")
                            print(f"      Parent text (first 100 chars): {parent.get_text()[:100]}")
                            
                            # Look for player links in this section
                            player_links = parent.find_all('a', href=re.compile(r'index\.php\?player='))
                            print(f"      Found {len(player_links)} player links")
                            
                            for j, link in enumerate(player_links[:3]):  # Show first 3
                                player_name = link.get_text(strip=True)
                                href = link.get('href', '')
                                print(f"         {j+1}. {player_name} ({href})")
                                
                                # Look for breakeven value near this link
                                next_sibling = link.next_sibling
                                if next_sibling:
                                    if hasattr(next_sibling, 'strip'):
                                        text = next_sibling.strip()
                                        if text and text.isdigit():
                                            print(f"            Breakeven: {text}")
                        print()
                    
                    # Also check tables
                    tables = soup.find_all('table')
                    print(f"📋 Found {len(tables)} tables")
                    
                    for i, table in enumerate(tables[:3]):  # Check first 3 tables
                        rows = table.find_all('tr')
                        print(f"   Table {i+1}: {len(rows)} rows")
                        
                        if rows:
                            # Check first few rows
                            for j, row in enumerate(rows[:3]):
                                cells = row.find_all(['td', 'th'])
                                cell_texts = [cell.get_text(strip=True) for cell in cells]
                                print(f"      Row {j+1}: {cell_texts}")
                        print()
                    
                else:
                    print(f"❌ Failed to access: HTTP {response.status}")
                    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_team_breakevens())
