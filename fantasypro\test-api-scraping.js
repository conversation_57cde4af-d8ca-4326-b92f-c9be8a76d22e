/**
 * Test the NRL scraping API directly
 * This simulates what our Next.js API route will do
 */

const https = require('https');
const cheerio = require('cheerio');

// Rate limiting
let lastRequestTime = 0;
const REQUEST_DELAY = 2000;

async function rateLimitedFetch(url) {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < REQUEST_DELAY) {
    await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY - timeSinceLastRequest));
  }
  
  lastRequestTime = Date.now();

  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

function extractNumber(text) {
  if (!text) return 0;
  const cleaned = text.replace(/[,$%]/g, '').trim();
  const match = cleaned.match(/[\d.]+/);
  return match ? parseFloat(match[0]) : 0;
}

function generatePlayerId(name, team) {
  return `${name.toLowerCase().replace(/\s+/g, '_')}_${team.toLowerCase().replace(/\s+/g, '_')}`;
}

function normalizeTeamName(team) {
  const teamMappings = {
    'broncos': 'Brisbane Broncos',
    'roosters': 'Sydney Roosters',
    'storm': 'Melbourne Storm',
    'panthers': 'Penrith Panthers',
    'sharks': 'Cronulla Sharks',
    'sea eagles': 'Manly Sea Eagles',
    'rabbitohs': 'South Sydney Rabbitohs',
    'eels': 'Parramatta Eels',
    'knights': 'Newcastle Knights',
    'raiders': 'Canberra Raiders',
    'titans': 'Gold Coast Titans',
    'dragons': 'St George Illawarra Dragons',
    'bulldogs': 'Canterbury Bulldogs',
    'tigers': 'Wests Tigers',
    'cowboys': 'North Queensland Cowboys',
    'warriors': 'New Zealand Warriors',
    'dolphins': 'Dolphins'
  };

  const normalized = team.toLowerCase().trim();
  for (const [key, value] of Object.entries(teamMappings)) {
    if (normalized.includes(key)) {
      return value;
    }
  }
  return team;
}

async function scrapeNRLSuperCoachStats() {
  console.log('🕷️ Scraping nrlsupercoachstats.com for player stats...');
  
  try {
    const url = 'https://www.nrlsupercoachstats.com/stats.php?year=2025';
    console.log(`📡 Fetching: ${url}`);
    
    const response = await rateLimitedFetch(url);
    console.log(`📊 Status: ${response.status}`);
    
    if (response.status !== 200) {
      throw new Error(`HTTP ${response.status}`);
    }

    const $ = cheerio.load(response.data);
    console.log('✅ HTML parsed successfully');

    const players = [];
    const timestamp = new Date().toISOString();

    // Log page structure for debugging
    console.log(`📋 Page title: ${$('title').text()}`);
    console.log(`📊 Tables found: ${$('table').length}`);
    console.log(`📋 Total elements: ${$('*').length}`);

    // Try to find data in various ways
    const tables = $('table');
    
    if (tables.length > 0) {
      console.log('📊 Analyzing table structure...');
      
      tables.each((tableIndex, table) => {
        const $table = $(table);
        console.log(`\n📊 Table ${tableIndex + 1}:`);
        
        // Check headers
        const headers = $table.find('th, thead td');
        console.log(`   Headers (${headers.length}):`, headers.map((i, el) => $(el).text().trim()).get().slice(0, 10));
        
        // Check data rows
        const dataRows = $table.find('tbody tr, tr').not(':first');
        console.log(`   Data rows: ${dataRows.length}`);
        
        if (dataRows.length > 0) {
          console.log('   Sample rows:');
          dataRows.slice(0, 3).each((i, row) => {
            const cells = $(row).find('td, th');
            const cellData = cells.map((j, cell) => $(cell).text().trim()).get();
            console.log(`     Row ${i + 1}:`, cellData.slice(0, 8));
            
            // Try to extract player data if this looks like a player row
            if (cellData.length >= 4 && cellData[0] && cellData[0].length > 2) {
              const name = cellData[0];
              const team = cellData[1] || '';
              const position = cellData[2] || '';
              const price = cellData[3] || '';
              
              if (name && team && position) {
                const player = {
                  id: generatePlayerId(name, team),
                  name: name,
                  team: normalizeTeamName(team),
                  position: position,
                  price: extractNumber(price),
                  points: extractNumber(cellData[4] || ''),
                  average: extractNumber(cellData[5] || ''),
                  form: 0,
                  source: 'nrlsupercoachstats.com',
                  scraped_at: timestamp
                };
                
                players.push(player);
              }
            }
          });
        }
      });
    }

    // If no tables, try other approaches
    if (players.length === 0) {
      console.log('📄 No data in tables, trying alternative extraction...');
      
      // Look for any structured data
      const allText = $('body').text();
      const lines = allText.split('\n').filter(line => line.trim().length > 0);
      
      console.log(`📄 Text lines: ${lines.length}`);
      console.log('📄 Sample lines:', lines.slice(0, 10));
      
      // Look for player name patterns
      const playerNamePattern = /[A-Z][a-z]+ [A-Z][a-z]+/g;
      const potentialNames = allText.match(playerNamePattern) || [];
      const uniqueNames = [...new Set(potentialNames)].slice(0, 20);
      console.log(`🔍 Potential player names: ${uniqueNames.join(', ')}`);
    }

    console.log(`✅ Extracted ${players.length} players from nrlsupercoachstats.com`);
    return players;

  } catch (error) {
    console.error('❌ Error scraping nrlsupercoachstats.com:', error.message);
    throw error;
  }
}

async function scrapeFootyStatistics() {
  console.log('\n🕷️ Scraping footystatistics.com for ownership data...');
  
  try {
    const url = 'https://www.footystatistics.com/ownership.php';
    console.log(`📡 Fetching: ${url}`);
    
    const response = await rateLimitedFetch(url);
    console.log(`📊 Status: ${response.status}`);
    
    if (response.status !== 200) {
      throw new Error(`HTTP ${response.status}`);
    }

    const $ = cheerio.load(response.data);
    console.log('✅ HTML parsed successfully');

    const ownershipData = [];
    const timestamp = new Date().toISOString();

    // Log page structure
    console.log(`📋 Page title: ${$('title').text()}`);
    console.log(`📊 Tables found: ${$('table').length}`);

    // Analyze table structure
    const tables = $('table');
    
    if (tables.length > 0) {
      console.log('📊 Analyzing ownership table structure...');
      
      tables.each((tableIndex, table) => {
        const $table = $(table);
        console.log(`\n📊 Table ${tableIndex + 1}:`);
        
        const headers = $table.find('th, thead td');
        console.log(`   Headers (${headers.length}):`, headers.map((i, el) => $(el).text().trim()).get());
        
        const dataRows = $table.find('tbody tr, tr').not(':first');
        console.log(`   Data rows: ${dataRows.length}`);
        
        if (dataRows.length > 0) {
          console.log('   Sample ownership rows:');
          dataRows.each((i, row) => {
            const cells = $(row).find('td, th');
            const cellData = cells.map((j, cell) => $(cell).text().trim()).get();
            if (i < 5) console.log(`     Row ${i + 1}:`, cellData.slice(0, 6));
            
            // Try to extract ownership data
            if (cellData.length >= 2 && cellData[0] && cellData[0].length > 2) {
              const name = cellData[0];
              const count = cellData[1] || '';

              // Extract team from player name if it contains team info
              let team = '';
              let cleanName = name;

              // Check if name contains team abbreviations
              const teamPatterns = [
                'BRO', 'SYD', 'MEL', 'PEN', 'CRO', 'MAN', 'RAB', 'PAR', 'NEW', 'CAN',
                'GLD', 'STI', 'CNT', 'TIG', 'COW', 'NZW', 'DOL'
              ];

              for (const pattern of teamPatterns) {
                if (name.includes(pattern)) {
                  team = pattern;
                  cleanName = name.replace(pattern, '').trim();
                  break;
                }
              }

              if (cleanName && count && !isNaN(count)) {
                const ownershipInfo = {
                  id: generatePlayerId(cleanName, team),
                  name: cleanName,
                  team: team,
                  ownership_count: parseInt(count),
                  ownership: 0, // Will calculate percentage later if we know total teams
                  scraped_at: timestamp
                };

                ownershipData.push(ownershipInfo);
              }
            }
          });
        }
      });
    }

    console.log(`✅ Extracted ownership data for ${ownershipData.length} players from footystatistics.com`);
    return ownershipData;

  } catch (error) {
    console.error('❌ Error scraping footystatistics.com:', error.message);
    throw error;
  }
}

async function runFullTest() {
  console.log('🚀 TESTING NRL DATA SCRAPING API SIMULATION');
  console.log('============================================');
  
  try {
    // Test both sources
    const [playersResult, ownershipResult] = await Promise.allSettled([
      scrapeNRLSuperCoachStats(),
      scrapeFootyStatistics()
    ]);

    const players = playersResult.status === 'fulfilled' ? playersResult.value : [];
    const ownership = ownershipResult.status === 'fulfilled' ? ownershipResult.value : [];
    
    console.log('\n📊 FINAL RESULTS:');
    console.log('==================');
    console.log(`✅ Players scraped: ${players.length}`);
    console.log(`✅ Ownership records: ${ownership.length}`);
    
    if (players.length > 0) {
      console.log('\n🎯 Sample players:');
      players.slice(0, 3).forEach((player, i) => {
        console.log(`   ${i + 1}. ${player.name} (${player.team}) - ${player.position} - $${player.price}`);
      });
    }
    
    if (ownership.length > 0) {
      console.log('\n📈 Sample ownership:');
      ownership.slice(0, 3).forEach((owner, i) => {
        console.log(`   ${i + 1}. ${owner.name} (${owner.team}) - ${owner.ownership}% owned`);
      });
    }

    // Merge data
    const ownershipMap = new Map(ownership.map(o => [o.id, o]));
    let mergedCount = 0;
    
    players.forEach(player => {
      const ownershipInfo = ownershipMap.get(player.id);
      if (ownershipInfo) {
        player.ownership = ownershipInfo.ownership;
        mergedCount++;
      }
    });

    console.log(`🔄 Merged records: ${mergedCount}`);
    
    const errors = [
      ...(playersResult.status === 'rejected' ? [`nrlsupercoachstats.com: ${playersResult.reason}`] : []),
      ...(ownershipResult.status === 'rejected' ? [`footystatistics.com: ${ownershipResult.reason}`] : [])
    ];

    console.log('\n🎉 SCRAPING TEST COMPLETE!');
    console.log(`📊 Total players: ${players.length}`);
    console.log(`📈 Total ownership: ${ownership.length}`);
    console.log(`🔄 Merged: ${mergedCount}`);
    console.log(`❌ Errors: ${errors.length}`);
    
    if (errors.length > 0) {
      console.log('⚠️ Errors:', errors);
    }

    if (players.length > 0 || ownership.length > 0) {
      console.log('\n🚀 SUCCESS! Web scraping is working!');
      console.log('✅ Ready to integrate into FantasyPro');
    } else {
      console.log('\n⚠️ No data extracted - need to analyze HTML structure');
    }

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  runFullTest().catch(console.error);
}

module.exports = { scrapeNRLSuperCoachStats, scrapeFootyStatistics };
