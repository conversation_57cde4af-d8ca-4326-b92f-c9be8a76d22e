#!/usr/bin/env python3
"""
FantasyPro Demo Server
Simple demo server for client presentation.
"""

import os
import sys
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime

# Set environment variables
os.environ["OPENAI_API_KEY"] = "not-needed"
os.environ["OPENAI_API_BASE"] = "http://localhost:1234/v1"

app = FastAPI(title="FantasyPro Demo", version="1.0.0-demo")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "FantasyPro Demo API", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/players")
async def players():
    return {
        "players": [
            {"id": 1, "name": "<PERSON>", "team": "Panthers", "price": 750000},
            {"id": 2, "name": "<PERSON>", "team": "Roosters", "price": 820000},
            {"id": 3, "name": "Daly Cherry-Evans", "team": "Sea Eagles", "price": 680000}
        ]
    }

@app.get("/demo")
async def demo():
    return {
        "message": "FantasyPro AI Integration Demo",
        "features": [
            "Player Performance Analysis",
            "AI-Driven Recommendations", 
            "Real-time Data Processing",
            "Multi-Agent Collaboration",
            "Advanced Analytics Dashboard"
        ],
        "tech_stack": [
            "Python FastAPI",
            "PraisonAI Agents",
            "React/Next.js Frontend",
            "PostgreSQL Database",
            "Redis Caching",
            "Docker Deployment"
        ],
        "status": "ready_for_demo"
    }

if __name__ == "__main__":
    print("FantasyPro Demo Server Starting...")
    print("API: http://localhost:8000")
    print("Docs: http://localhost:8000/docs")
    print("Demo: http://localhost:8000/demo")
    print("Players: http://localhost:8000/players")
    uvicorn.run(app, host="0.0.0.0", port=8000)
