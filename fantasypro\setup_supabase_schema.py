#!/usr/bin/env python3
"""
Setup Supabase Schema
Creates the necessary tables for FantasyPro NRL data
"""

import requests
import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SupabaseSchemaSetup:
    def __init__(self):
        self.project_id = "fuxpdgsixnbbsdspusmp"
        self.supabase_url = f"https://{self.project_id}.supabase.co"
        
        # Correct service role key from Supabase
        self.service_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg"
        
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.service_key}',
            'apikey': self.service_key,
            'Prefer': 'return=minimal'
        }
    
    def test_connection(self):
        """Test connection to Supabase"""
        try:
            # Try to access a simple endpoint
            url = f"{self.supabase_url}/rest/v1/"
            response = requests.get(url, headers=self.headers)
            
            print(f"🔗 Testing Supabase connection...")
            print(f"   URL: {url}")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            
            if response.status_code == 200:
                print("✅ Supabase connection successful!")
                return True
            else:
                print(f"❌ Connection failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    def check_existing_tables(self):
        """Check what tables already exist"""
        try:
            # Try to list tables by querying the information schema
            url = f"{self.supabase_url}/rest/v1/information_schema.tables?select=table_name&table_schema=eq.public"
            response = requests.get(url, headers=self.headers)
            
            print(f"📋 Checking existing tables...")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                tables = response.json()
                print(f"   Found {len(tables)} tables:")
                for table in tables:
                    print(f"     - {table.get('table_name')}")
                return tables
            else:
                print(f"   Error: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error checking tables: {e}")
            return []
    
    def create_nrl_player_stats_table(self):
        """Create the NRL player stats table using SQL"""
        try:
            # SQL to create the table
            sql = """
            CREATE TABLE IF NOT EXISTS nrl_player_stats (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                position VARCHAR(50),
                team VARCHAR(100),
                price INTEGER DEFAULT 0,
                breakeven INTEGER DEFAULT 0,
                average_points DECIMAL(5,2) DEFAULT 0,
                total_points INTEGER DEFAULT 0,
                games_played INTEGER DEFAULT 0,
                minutes_played INTEGER DEFAULT 0,
                
                -- Attacking stats
                tries INTEGER DEFAULT 0,
                try_assists INTEGER DEFAULT 0,
                linebreaks INTEGER DEFAULT 0,
                linebreak_assists INTEGER DEFAULT 0,
                offloads INTEGER DEFAULT 0,
                runs INTEGER DEFAULT 0,
                metres INTEGER DEFAULT 0,
                post_contact_metres INTEGER DEFAULT 0,
                
                -- Defensive stats
                tackles INTEGER DEFAULT 0,
                missed_tackles INTEGER DEFAULT 0,
                tackle_efficiency DECIMAL(5,2) DEFAULT 0,
                intercepts INTEGER DEFAULT 0,
                
                -- Kicking stats
                kicks INTEGER DEFAULT 0,
                kick_metres INTEGER DEFAULT 0,
                forced_dropouts INTEGER DEFAULT 0,
                forty_twenties INTEGER DEFAULT 0,
                
                -- Discipline
                errors INTEGER DEFAULT 0,
                penalties INTEGER DEFAULT 0,
                sin_bins INTEGER DEFAULT 0,
                
                -- Form indicators
                last_3_avg DECIMAL(5,2) DEFAULT 0,
                last_5_avg DECIMAL(5,2) DEFAULT 0,
                home_avg DECIMAL(5,2) DEFAULT 0,
                away_avg DECIMAL(5,2) DEFAULT 0,
                
                -- Metadata
                data_source TEXT,
                last_updated TIMESTAMP DEFAULT NOW(),
                
                -- Unique constraint on name
                UNIQUE(name)
            );
            
            -- Create index for faster queries
            CREATE INDEX IF NOT EXISTS idx_nrl_player_stats_name ON nrl_player_stats(name);
            CREATE INDEX IF NOT EXISTS idx_nrl_player_stats_position ON nrl_player_stats(position);
            CREATE INDEX IF NOT EXISTS idx_nrl_player_stats_team ON nrl_player_stats(team);
            """
            
            # Execute SQL via Supabase RPC
            url = f"{self.supabase_url}/rest/v1/rpc/exec_sql"
            data = {"sql": sql}
            
            response = requests.post(url, headers=self.headers, json=data)
            
            print(f"🏗️ Creating nrl_player_stats table...")
            print(f"   Status: {response.status_code}")
            
            if response.status_code in [200, 201]:
                print("✅ Table created successfully!")
                return True
            else:
                print(f"❌ Table creation failed: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error creating table: {e}")
            return False
    
    def test_table_access(self):
        """Test if we can access the nrl_player_stats table"""
        try:
            url = f"{self.supabase_url}/rest/v1/nrl_player_stats?limit=1"
            response = requests.get(url, headers=self.headers)
            
            print(f"🧪 Testing table access...")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Table access successful! Found {len(data)} records")
                return True
            else:
                print(f"❌ Table access failed: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing table access: {e}")
            return False
    
    def setup_complete_schema(self):
        """Set up the complete schema for FantasyPro"""
        print("🚀 Setting up FantasyPro Supabase Schema")
        print("=" * 50)
        
        # Test connection
        if not self.test_connection():
            print("❌ Cannot proceed - connection failed")
            return False
        
        # Check existing tables
        existing_tables = self.check_existing_tables()
        
        # Create nrl_player_stats table
        if not self.create_nrl_player_stats_table():
            print("❌ Failed to create main table")
            return False
        
        # Test table access
        if not self.test_table_access():
            print("❌ Cannot access created table")
            return False
        
        print("\n🎉 Schema setup completed successfully!")
        print("✅ Ready for NRL player data storage")
        return True

def main():
    """Main setup function"""
    setup = SupabaseSchemaSetup()
    success = setup.setup_complete_schema()
    
    if success:
        print("\n🎯 Next steps:")
        print("   1. Run the NRL player stats scraper")
        print("   2. Run the Supabase integration test")
        print("   3. Verify data is stored correctly")
    else:
        print("\n❌ Setup failed - check credentials and permissions")

if __name__ == "__main__":
    main()
