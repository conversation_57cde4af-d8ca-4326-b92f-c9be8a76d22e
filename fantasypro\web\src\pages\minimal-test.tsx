import React from 'react';
import { NextPage } from 'next';
import Head from 'next/head';

const MinimalTestPage: NextPage = () => {
  return (
    <>
      <Head>
        <title>Minimal Test - FantasyPro</title>
        <meta name="description" content="Minimal test page" />
      </Head>

      <div className="min-h-screen bg-slate-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">✅ Minimal Test Page</h1>
          <p className="text-lg text-gray-300 mb-8">
            If you can see this, React is working correctly.
          </p>
          
          <div className="space-y-4">
            <div className="bg-slate-800 p-4 rounded-lg">
              <h2 className="text-xl font-semibold mb-2">Component Status</h2>
              <p className="text-green-400">✅ React rendering successfully</p>
              <p className="text-green-400">✅ Next.js routing working</p>
              <p className="text-green-400">✅ CSS styles loading</p>
            </div>
            
            <div className="flex space-x-4 justify-center">
              <button
                type="button"
                onClick={() => console.log('Button clicked!')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
              >
                Test Button
              </button>
              <a
                href="/"
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg"
              >
                Go Home
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MinimalTestPage;
