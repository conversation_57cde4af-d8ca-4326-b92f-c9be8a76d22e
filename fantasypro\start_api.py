#!/usr/bin/env python3
"""
API Startup Script

Ensures proper virtual environment and starts the API server with real SuperCoach data.
"""

import sys
import os
from pathlib import Path

# Ensure we're using the virtual environment
venv_python = Path(__file__).parent / "venv" / "Scripts" / "python.exe"
if venv_python.exists() and sys.executable != str(venv_python):
    print(f"🔄 Switching to virtual environment Python: {venv_python}")
    os.execv(str(venv_python), [str(venv_python)] + sys.argv)

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🏈 FantasyPro API Server with Real NRL SuperCoach Data")
print("=" * 60)

# Test imports first
try:
    print("🔍 Testing critical imports...")
    
    import sqlalchemy
    print(f"  ✅ SQLAlchemy: {sqlalchemy.__version__}")
    
    import fastapi
    print(f"  ✅ FastAPI: {fastapi.__version__}")
    
    from database.supercoach_db import supercoach_db
    print("  ✅ SuperCoach database connection")
    
    from api.supercoach_endpoints import router as supercoach_router
    print("  ✅ SuperCoach API endpoints")
    
    print("🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import error: {e}")
    print("🔧 Please check your virtual environment and dependencies")
    sys.exit(1)

# Import and start the API
try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn
    
    # Create FastAPI app
    app = FastAPI(
        title="FantasyPro API - Real NRL SuperCoach Data",
        description="AI-Integrated Fantasy Sports Platform with Real NRL SuperCoach Stats",
        version="1.0.0-production",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include SuperCoach router
    app.include_router(supercoach_router)
    
    # Add health check
    @app.get("/health")
    async def health_check():
        return {
            "status": "healthy",
            "mode": "production",
            "supercoach_data": "real",
            "database": "populated"
        }
    
    # Verify database has data
    with supercoach_db.get_session() as session:
        from database.supercoach_models import SuperCoachPlayer
        all_players = session.query(SuperCoachPlayer).all()
        player_count = len(all_players)

        print(f"\n📊 Database Status:")
        print(f"  👥 Players in database: {player_count}")

        if player_count > 0:
            # Show sample data
            sample_player = all_players[0]
            print(f"  🏈 Sample player: {sample_player.name} ({sample_player.team.name})")
            print(f"  📊 Breakeven: {sample_player.current_breakeven}")
            print(f"  💰 Price: ${sample_player.current_price:,.0f}" if sample_player.current_price else "  💰 Price: Not set")
    
    print(f"\n🚀 Starting API Server...")
    print(f"📊 API Documentation: http://localhost:8000/docs")
    print(f"🏈 SuperCoach Dashboard: http://localhost:8000/supercoach/dashboard")
    print(f"👥 All Players: http://localhost:8000/supercoach/players")
    print(f"🤖 AI Recommendations: http://localhost:8000/supercoach/ai/recommendations")
    print(f"🔍 Health Check: http://localhost:8000/health")
    print("=" * 60)
    
    # Start the server
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
    
except Exception as e:
    print(f"❌ Error starting server: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
