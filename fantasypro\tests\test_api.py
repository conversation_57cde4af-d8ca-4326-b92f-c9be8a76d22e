#!/usr/bin/env python3
"""
FantasyPro API Test Suite

Comprehensive tests for API endpoints, authentication,
and data validation.
"""

import unittest
import sys
import os
import json
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import requests
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestAPIEndpoints(unittest.TestCase):
    """Test cases for API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.base_url = "http://localhost:8000"
        self.test_headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    def test_health_endpoint(self):
        """Test health check endpoint."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            
            # Check status code
            self.assertEqual(response.status_code, 200)
            
            # Check response structure
            data = response.json()
            self.assertIn('status', data)
            self.assertIn('timestamp', data)
            self.assertIn('version', data)
            self.assertEqual(data['status'], 'healthy')
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_detailed_health_endpoint(self):
        """Test detailed health check endpoint."""
        try:
            response = requests.get(f"{self.base_url}/health/detailed", timeout=5)
            
            self.assertEqual(response.status_code, 200)
            
            data = response.json()
            self.assertIn('status', data)
            self.assertIn('services', data)
            self.assertIn('timestamp', data)
            self.assertIn('environment', data)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_players_endpoint(self):
        """Test players listing endpoint."""
        try:
            response = requests.get(f"{self.base_url}/players", timeout=5)
            
            self.assertEqual(response.status_code, 200)
            
            data = response.json()
            self.assertIsInstance(data, list)
            
            if data:
                player = data[0]
                required_fields = ['id', 'name', 'position', 'team', 'price', 'points']
                for field in required_fields:
                    self.assertIn(field, player)
                
                # Validate data types
                self.assertIsInstance(player['id'], int)
                self.assertIsInstance(player['name'], str)
                self.assertIsInstance(player['price'], (int, float))
                self.assertIsInstance(player['points'], int)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_players_filtering(self):
        """Test players endpoint with filters."""
        try:
            # Test position filter
            response = requests.get(
                f"{self.base_url}/players",
                params={"position": "Halfback"},
                timeout=5
            )
            
            self.assertEqual(response.status_code, 200)
            
            data = response.json()
            if data:
                for player in data:
                    self.assertEqual(player['position'], 'Halfback')
            
            # Test price filter
            response = requests.get(
                f"{self.base_url}/players",
                params={"min_price": 700000, "max_price": 800000},
                timeout=5
            )
            
            self.assertEqual(response.status_code, 200)
            
            data = response.json()
            if data:
                for player in data:
                    self.assertGreaterEqual(player['price'], 700000)
                    self.assertLessEqual(player['price'], 800000)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_player_detail_endpoint(self):
        """Test individual player detail endpoint."""
        try:
            # First get a player ID
            response = requests.get(f"{self.base_url}/players", timeout=5)
            self.assertEqual(response.status_code, 200)
            
            players = response.json()
            if not players:
                self.skipTest("No players available for testing")
            
            player_id = players[0]['id']
            
            # Test player detail
            response = requests.get(f"{self.base_url}/players/{player_id}", timeout=5)
            self.assertEqual(response.status_code, 200)
            
            player = response.json()
            self.assertEqual(player['id'], player_id)
            
            # Test non-existent player
            response = requests.get(f"{self.base_url}/players/99999", timeout=5)
            self.assertEqual(response.status_code, 404)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_recommendations_endpoint(self):
        """Test recommendations endpoint."""
        try:
            response = requests.get(f"{self.base_url}/recommendations", timeout=5)
            
            self.assertEqual(response.status_code, 200)
            
            data = response.json()
            self.assertIsInstance(data, list)
            
            if data:
                recommendation = data[0]
                required_fields = ['id', 'type', 'title', 'description', 'confidence']
                for field in required_fields:
                    self.assertIn(field, recommendation)
                
                # Validate confidence range
                self.assertGreaterEqual(recommendation['confidence'], 0)
                self.assertLessEqual(recommendation['confidence'], 1)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_analytics_dashboard_endpoint(self):
        """Test analytics dashboard endpoint."""
        try:
            response = requests.get(f"{self.base_url}/analytics/dashboard", timeout=5)
            
            self.assertEqual(response.status_code, 200)
            
            data = response.json()
            self.assertIn('user_stats', data)
            self.assertIn('recent_performance', data)
            self.assertIn('league_position', data)
            
            # Validate user stats
            user_stats = data['user_stats']
            self.assertIn('total_points', user_stats)
            self.assertIn('rank', user_stats)
            self.assertIn('team_value', user_stats)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")

class TestAuthentication(unittest.TestCase):
    """Test cases for authentication system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.base_url = "http://localhost:8000"
        self.test_credentials = {
            "email": "<EMAIL>",
            "password": "demo123"
        }
        self.invalid_credentials = {
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
    
    def test_valid_login(self):
        """Test login with valid credentials."""
        try:
            response = requests.post(
                f"{self.base_url}/auth/login",
                json=self.test_credentials,
                timeout=5
            )
            
            self.assertEqual(response.status_code, 200)
            
            data = response.json()
            self.assertIn('access_token', data)
            self.assertIn('token_type', data)
            self.assertIn('user', data)
            
            # Validate token type
            self.assertEqual(data['token_type'], 'bearer')
            
            # Validate user data
            user = data['user']
            self.assertIn('id', user)
            self.assertIn('email', user)
            self.assertEqual(user['email'], self.test_credentials['email'])
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_invalid_login(self):
        """Test login with invalid credentials."""
        try:
            response = requests.post(
                f"{self.base_url}/auth/login",
                json=self.invalid_credentials,
                timeout=5
            )
            
            self.assertEqual(response.status_code, 401)
            
            data = response.json()
            self.assertIn('detail', data)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_missing_credentials(self):
        """Test login with missing credentials."""
        try:
            # Test missing email
            response = requests.post(
                f"{self.base_url}/auth/login",
                json={"password": "test123"},
                timeout=5
            )
            
            # Should handle gracefully (might be 422 or 401)
            self.assertIn(response.status_code, [401, 422])
            
            # Test missing password
            response = requests.post(
                f"{self.base_url}/auth/login",
                json={"email": "<EMAIL>"},
                timeout=5
            )
            
            self.assertIn(response.status_code, [401, 422])
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")

class TestDataValidation(unittest.TestCase):
    """Test cases for data validation and error handling."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.base_url = "http://localhost:8000"
    
    def test_invalid_player_id(self):
        """Test handling of invalid player IDs."""
        try:
            # Test non-numeric ID
            response = requests.get(f"{self.base_url}/players/invalid", timeout=5)
            self.assertEqual(response.status_code, 422)  # Validation error
            
            # Test negative ID
            response = requests.get(f"{self.base_url}/players/-1", timeout=5)
            self.assertEqual(response.status_code, 404)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_invalid_query_parameters(self):
        """Test handling of invalid query parameters."""
        try:
            # Test invalid price range
            response = requests.get(
                f"{self.base_url}/players",
                params={"min_price": "invalid"},
                timeout=5
            )
            
            # Should handle gracefully
            self.assertIn(response.status_code, [200, 422])
            
            # Test negative limit
            response = requests.get(
                f"{self.base_url}/players",
                params={"limit": -1},
                timeout=5
            )
            
            self.assertIn(response.status_code, [200, 422])
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_cors_headers(self):
        """Test CORS headers are present."""
        try:
            response = requests.options(f"{self.base_url}/players", timeout=5)
            
            # Check for CORS headers
            headers = response.headers
            self.assertIn('Access-Control-Allow-Origin', headers)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")

class TestPerformance(unittest.TestCase):
    """Test cases for API performance."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.base_url = "http://localhost:8000"
    
    def test_response_times(self):
        """Test API response times."""
        try:
            import time
            
            # Test health endpoint
            start_time = time.time()
            response = requests.get(f"{self.base_url}/health", timeout=5)
            health_time = time.time() - start_time
            
            self.assertEqual(response.status_code, 200)
            self.assertLess(health_time, 1.0)  # Should respond within 1 second
            
            # Test players endpoint
            start_time = time.time()
            response = requests.get(f"{self.base_url}/players", timeout=5)
            players_time = time.time() - start_time
            
            self.assertEqual(response.status_code, 200)
            self.assertLess(players_time, 2.0)  # Should respond within 2 seconds
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")
    
    def test_concurrent_requests(self):
        """Test handling of concurrent requests."""
        try:
            import threading
            import time
            
            results = []
            
            def make_request():
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    results.append(response.status_code)
                except Exception as e:
                    results.append(str(e))
            
            # Create multiple threads
            threads = []
            for _ in range(5):
                thread = threading.Thread(target=make_request)
                threads.append(thread)
            
            # Start all threads
            start_time = time.time()
            for thread in threads:
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            total_time = time.time() - start_time
            
            # All requests should succeed
            self.assertEqual(len(results), 5)
            for result in results:
                self.assertEqual(result, 200)
            
            # Should handle concurrent requests efficiently
            self.assertLess(total_time, 5.0)
            
        except requests.exceptions.ConnectionError:
            self.skipTest("API server not running")

def run_api_tests():
    """Run all API tests and return results."""
    print("🌐 Running FantasyPro API Test Suite...")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestAPIEndpoints,
        TestAuthentication,
        TestDataValidation,
        TestPerformance
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
    print(f"Success rate: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_api_tests()
    sys.exit(0 if success else 1)
