import React from 'react';
import Head from 'next/head';
import Link from 'next/link';

export default function Home() {
  return (
    <>
      <Head>
        <title>FantasyPro - AI-Powered NRL SuperCoach Analytics</title>
        <meta name="description" content="Professional-level fantasy sports platform for NRL SuperCoach with AI-powered insights" />
      </Head>
      
      <div style={{ minHeight: '100vh', backgroundColor: '#0f172a', color: 'white', padding: '2rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', textAlign: 'center' }}>
          <h1 style={{ fontSize: '3rem', marginBottom: '1rem' }}>FantasyPro</h1>
          <p style={{ fontSize: '1.2rem', marginBottom: '2rem', color: '#94a3b8' }}>
            AI-Powered Fantasy Sports Intelligence Platform
          </p>
          
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Link href="/dashboard" style={{ 
              backgroundColor: '#16a34a', 
              color: 'white', 
              padding: '0.75rem 1.5rem', 
              borderRadius: '0.5rem', 
              textDecoration: 'none',
              display: 'inline-block'
            }}>
              Dashboard
            </Link>
            <Link href="/my-team" style={{
              backgroundColor: '#1e293b',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.5rem',
              textDecoration: 'none',
              display: 'inline-block',
              border: '1px solid #475569'
            }}>
              My Team
            </Link>
          </div>
          
          <div style={{ marginTop: '3rem' }}>
            <h2 style={{ fontSize: '1.5rem', marginBottom: '1rem' }}>Features</h2>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginTop: '2rem' }}>
              <div style={{ backgroundColor: '#1e293b', padding: '1.5rem', borderRadius: '0.5rem', border: '1px solid #475569' }}>
                <h3 style={{ marginBottom: '0.5rem' }}>Manual Squad Management</h3>
                <p style={{ color: '#94a3b8' }}>Add, edit, and manage your SuperCoach players manually with comprehensive stats tracking.</p>
              </div>
              <div style={{ backgroundColor: '#1e293b', padding: '1.5rem', borderRadius: '0.5rem', border: '1px solid #475569' }}>
                <h3 style={{ marginBottom: '0.5rem' }}>AI Analytics</h3>
                <p style={{ color: '#94a3b8' }}>Get AI-powered insights and recommendations for optimal team performance.</p>
              </div>
              <div style={{ backgroundColor: '#1e293b', padding: '1.5rem', borderRadius: '0.5rem', border: '1px solid #475569' }}>
                <h3 style={{ marginBottom: '0.5rem' }}>Real-time Data</h3>
                <p style={{ color: '#94a3b8' }}>Stay updated with live player stats, prices, and form ratings.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
