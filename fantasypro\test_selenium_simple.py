#!/usr/bin/env python3
"""
Simple Selenium test for SuperCoach access
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_supercoach_access():
    """Simple test to access SuperCoach site"""
    
    print("🔍 Testing SuperCoach Access with Selenium")
    print("=" * 50)
    
    driver = None
    try:
        # Setup Chrome options
        chrome_options = Options()
        # Don't run headless so we can see what happens
        # chrome_options.add_argument('--headless')
        
        # Anti-detection settings
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # User agent
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Setup driver with webdriver-manager
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Hide automation indicators
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Chrome driver setup successful")
        
        # Test 1: Access main SuperCoach site
        print("\n🌐 Testing main site access...")
        driver.get('https://www.supercoach.com.au')
        time.sleep(5)
        
        print(f"Current URL: {driver.current_url}")
        print(f"Page title: {driver.title}")
        
        # Check if we can find any login elements
        print("\n🔍 Looking for login elements...")
        
        # Save page source for analysis
        with open('supercoach_main_page.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("💾 Saved page source to supercoach_main_page.html")
        
        # Look for login button
        login_selectors = [
            'a[href*="login"]',
            'button[data-testid="login"]',
            '.login-button',
            '#login-btn'
        ]
        
        login_found = False
        for selector in login_selectors:
            try:
                elements = driver.find_elements("css selector", selector)
                if elements:
                    print(f"✅ Found login element with selector: {selector}")
                    print(f"   Element text: {elements[0].text}")
                    login_found = True
                    break
            except Exception as e:
                print(f"❌ Error with selector {selector}: {e}")
        
        if not login_found:
            print("⚠️ No login elements found with standard selectors")
            
            # Try to find any links that might be login
            all_links = driver.find_elements("tag name", "a")
            login_links = []
            for link in all_links:
                href = link.get_attribute('href')
                if href and 'login' in href.lower():
                    login_links.append(link)
            
            if login_links:
                print(f"🔗 Found {len(login_links)} potential login links:")
                for i, link in enumerate(login_links[:3]):
                    print(f"   {i+1}. {link.get_attribute('href')} - '{link.text}'")
            else:
                print("❌ No login links found")
        
        # Test 2: Try direct login page access
        print("\n🔐 Testing direct login page access...")
        driver.get('https://www.supercoach.com.au/login')
        time.sleep(5)
        
        print(f"Login page URL: {driver.current_url}")
        print(f"Login page title: {driver.title}")
        
        # Save login page
        with open('supercoach_login_page.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("💾 Saved login page source to supercoach_login_page.html")
        
        # Look for email and password fields
        email_found = False
        password_found = False
        
        try:
            email_field = driver.find_element("css selector", 'input[type="email"]')
            if email_field:
                print("✅ Found email field")
                email_found = True
        except:
            try:
                email_field = driver.find_element("css selector", 'input[name="email"]')
                if email_field:
                    print("✅ Found email field (by name)")
                    email_found = True
            except:
                print("❌ No email field found")
        
        try:
            password_field = driver.find_element("css selector", 'input[type="password"]')
            if password_field:
                print("✅ Found password field")
                password_found = True
        except:
            print("❌ No password field found")
        
        if email_found and password_found:
            print("🎯 Login form detected! Ready for authentication")
            
            # Test 3: Try to fill in credentials (but don't submit)
            print("\n📝 Testing credential input...")
            
            try:
                email_field.clear()
                email_field.send_keys("<EMAIL>")
                print("✅ Email entered successfully")
                
                password_field.clear()
                password_field.send_keys("SuperKenny123!")
                print("✅ Password entered successfully")
                
                print("⚠️ Credentials entered but NOT submitting (manual test)")
                print("🔍 You can manually submit to test authentication")
                
                # Wait for manual inspection
                print("\n⏳ Waiting 30 seconds for manual inspection...")
                time.sleep(30)
                
            except Exception as e:
                print(f"❌ Error entering credentials: {e}")
        else:
            print("❌ Login form not complete")
        
        print("\n✅ Test completed successfully")
        
    except Exception as e:
        print(f"💥 Error during test: {e}")
        
    finally:
        if driver:
            print("\n🧹 Closing browser...")
            driver.quit()

if __name__ == "__main__":
    test_supercoach_access()
