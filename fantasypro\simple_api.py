#!/usr/bin/env python3
"""
FantasyPro Simple API Server

A simplified version of the FantasyPro API for development and testing.
This version uses mock data and doesn't require database connections.
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import SuperCoach endpoints
try:
    from api.supercoach_endpoints import router as supercoach_router
    SUPERCOACH_AVAILABLE = True
except ImportError as e:
    print(f"Warning: SuperCoach endpoints not available: {e}")
    SUPERCOACH_AVAILABLE = False

# Initialize FastAPI app
app = FastAPI(
    title="FantasyPro API (Development)",
    description="AI-Integrated Fantasy Sports Platform API - Development Version",
    version="1.0.0-dev",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include SuperCoach router if available
if SUPERCOACH_AVAILABLE:
    app.include_router(supercoach_router)
    print("✅ SuperCoach endpoints loaded successfully")

# =============================================================================
# PYDANTIC MODELS
# =============================================================================

class PlayerResponse(BaseModel):
    id: int
    name: str
    position: str
    team: str
    price: float
    points: int
    form: float
    ownership: float
    value_score: float
    recommendation: str

class TeamResponse(BaseModel):
    id: int
    user_id: int
    name: str
    players: List[PlayerResponse]
    total_value: float
    projected_points: float
    risk_score: float

class RecommendationResponse(BaseModel):
    id: int
    type: str
    title: str
    description: str
    confidence: float
    impact: str
    created_at: str

class DashboardResponse(BaseModel):
    user_stats: Dict[str, Any]
    recent_performance: List[Dict[str, Any]]
    league_position: Dict[str, Any]
    upcoming_fixtures: List[Dict[str, Any]]

# =============================================================================
# MOCK DATA
# =============================================================================

MOCK_PLAYERS = [
    {
        "id": 1,
        "name": "Nathan Cleary",
        "position": "Halfback",
        "team": "Penrith Panthers",
        "price": 750000,
        "points": 1250,
        "form": 8.5,
        "ownership": 45.2,
        "value_score": 9.1,
        "recommendation": "Strong Buy"
    },
    {
        "id": 2,
        "name": "James Tedesco",
        "position": "Fullback",
        "team": "Sydney Roosters",
        "price": 820000,
        "points": 1380,
        "form": 9.2,
        "ownership": 52.8,
        "value_score": 8.8,
        "recommendation": "Hold"
    },
    {
        "id": 3,
        "name": "Daly Cherry-Evans",
        "position": "Halfback",
        "team": "Manly Sea Eagles",
        "price": 680000,
        "points": 1150,
        "form": 7.8,
        "ownership": 38.5,
        "value_score": 8.5,
        "recommendation": "Buy"
    },
    {
        "id": 4,
        "name": "Kalyn Ponga",
        "position": "Fullback",
        "team": "Newcastle Knights",
        "price": 780000,
        "points": 1200,
        "form": 8.0,
        "ownership": 41.2,
        "value_score": 7.9,
        "recommendation": "Hold"
    },
    {
        "id": 5,
        "name": "Cameron Munster",
        "position": "Five-eighth",
        "team": "Melbourne Storm",
        "price": 720000,
        "points": 1180,
        "form": 8.3,
        "ownership": 39.8,
        "value_score": 8.7,
        "recommendation": "Buy"
    }
]

MOCK_RECOMMENDATIONS = [
    {
        "id": 1,
        "type": "transfer",
        "title": "Consider transferring Nathan Cleary",
        "description": "Cleary has a tough fixture run coming up. Consider downgrading to fund other positions.",
        "confidence": 0.85,
        "impact": "high",
        "created_at": datetime.now().isoformat()
    },
    {
        "id": 2,
        "type": "captain",
        "title": "Captain James Tedesco this week",
        "description": "Tedesco has an excellent matchup against a weak defensive team.",
        "confidence": 0.92,
        "impact": "medium",
        "created_at": datetime.now().isoformat()
    },
    {
        "id": 3,
        "type": "buy",
        "title": "Target Cameron Munster",
        "description": "Munster has favorable fixtures and is in excellent form.",
        "confidence": 0.78,
        "impact": "medium",
        "created_at": datetime.now().isoformat()
    }
]

# =============================================================================
# HEALTH CHECK ENDPOINTS
# =============================================================================

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0-dev",
        "mode": "development"
    }

@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with service status."""
    return {
        "status": "healthy",
        "services": {
            "api": "healthy",
            "database": "mock",
            "redis": "mock",
            "agents": "mock"
        },
        "timestamp": datetime.now().isoformat()
    }

# =============================================================================
# AUTHENTICATION ENDPOINTS
# =============================================================================

@app.post("/auth/login")
async def login(credentials: dict):
    """User login endpoint."""
    email = credentials.get("email")
    password = credentials.get("password")
    
    # Mock authentication
    if email == "<EMAIL>" and password == "demo123":
        return {
            "access_token": "mock_jwt_token_12345",
            "token_type": "bearer",
            "user": {
                "id": 1,
                "email": email,
                "username": "demo_user"
            }
        }
    else:
        raise HTTPException(status_code=401, detail="Invalid credentials")

# =============================================================================
# PLAYER ENDPOINTS
# =============================================================================

@app.get("/players", response_model=List[PlayerResponse])
async def get_players(
    position: Optional[str] = None,
    team: Optional[str] = None,
    min_price: Optional[float] = None,
    max_price: Optional[float] = None,
    limit: int = 100
):
    """Get players with optional filtering."""
    players = MOCK_PLAYERS.copy()
    
    # Apply filters
    if position:
        players = [p for p in players if p["position"] == position]
    if team:
        players = [p for p in players if p["team"] == team]
    if min_price:
        players = [p for p in players if p["price"] >= min_price]
    if max_price:
        players = [p for p in players if p["price"] <= max_price]
    
    return players[:limit]

@app.get("/players/{player_id}", response_model=PlayerResponse)
async def get_player(player_id: int):
    """Get detailed player information."""
    player = next((p for p in MOCK_PLAYERS if p["id"] == player_id), None)
    if not player:
        raise HTTPException(status_code=404, detail="Player not found")
    return player

@app.get("/players/{player_id}/stats")
async def get_player_stats(player_id: int, weeks: int = 5):
    """Get player performance statistics."""
    return {
        "player_id": player_id,
        "weeks": weeks,
        "avg_points": 62.5,
        "total_points": 312,
        "consistency": 0.82,
        "recent_scores": [72, 58, 69, 71, 63],
        "form_trend": "improving"
    }

# =============================================================================
# TEAM MANAGEMENT ENDPOINTS
# =============================================================================

@app.get("/teams/my-team", response_model=TeamResponse)
async def get_my_team():
    """Get current user's team."""
    return {
        "id": 1,
        "user_id": 1,
        "name": "My Fantasy Team",
        "players": MOCK_PLAYERS[:3],  # First 3 players
        "total_value": 2250000,
        "projected_points": 3780,
        "risk_score": 0.25
    }

@app.post("/teams/optimize")
async def optimize_team(constraints: dict):
    """Get optimized team suggestions."""
    return [
        {
            "strategy": "Balanced",
            "projected_points": 1350,
            "risk_score": 0.3,
            "changes_required": 2,
            "total_cost": 9800000,
            "suggested_players": MOCK_PLAYERS[:4]
        },
        {
            "strategy": "High Risk/High Reward",
            "projected_points": 1450,
            "risk_score": 0.6,
            "changes_required": 4,
            "total_cost": 9950000,
            "suggested_players": MOCK_PLAYERS[1:5]
        }
    ]

# =============================================================================
# RECOMMENDATION ENDPOINTS
# =============================================================================

@app.get("/recommendations", response_model=List[RecommendationResponse])
async def get_recommendations(
    type: Optional[str] = None,
    limit: int = 20
):
    """Get personalized recommendations."""
    recommendations = MOCK_RECOMMENDATIONS.copy()
    
    if type:
        recommendations = [r for r in recommendations if r["type"] == type]
    
    return recommendations[:limit]

@app.get("/recommendations/weekly-insights")
async def get_weekly_insights():
    """Get weekly insights and analysis."""
    return {
        "week": 23,
        "key_insights": [
            "Origin players may be rested this week",
            "Weather conditions favor running games",
            "Several key players returning from injury"
        ],
        "top_picks": [
            {"player": "James Tedesco", "reason": "Excellent matchup"},
            {"player": "Daly Cherry-Evans", "reason": "Home advantage"}
        ],
        "avoid_list": [
            {"player": "Nathan Cleary", "reason": "Tough fixture"}
        ],
        "captain_recommendations": [
            {"player": "James Tedesco", "confidence": 0.92},
            {"player": "Kalyn Ponga", "confidence": 0.88}
        ]
    }

# =============================================================================
# ANALYTICS ENDPOINTS
# =============================================================================

@app.get("/analytics/dashboard", response_model=DashboardResponse)
async def get_dashboard_data():
    """Get enhanced dashboard analytics data inspired by beta design."""
    return {
        "user_stats": {
            "total_points": 31280,
            "rank": 1250,
            "weekly_rank": 890,
            "team_value": 11800000,
            "transfers_remaining": 8
        },
        "recent_performance": [
            {"week": 20, "points": 1250, "rank": 1200},
            {"week": 21, "points": 1180, "rank": 1350},
            {"week": 22, "points": 1320, "rank": 1100}
        ],
        "league_position": {
            "overall_rank": 1250,
            "total_players": 500000,
            "percentile": 99.75,
            "points_to_top_1000": 150
        },
        "upcoming_fixtures": [
            {
                "player": "Nathan Cleary",
                "opponent": "Sydney Roosters",
                "difficulty": 3.5,
                "date": "2024-08-15"
            },
            {
                "player": "James Tedesco",
                "opponent": "Penrith Panthers",
                "difficulty": 4.2,
                "date": "2024-08-15"
            }
        ]
    }

@app.get("/analytics/dashboard/enhanced")
async def get_enhanced_dashboard():
    """Get comprehensive dashboard data matching beta design."""
    return {
        "round_status": {
            "current_round": 16,
            "confidence": 94,
            "status": "Active"
        },
        "live_round_score": {
            "current_score": 2280,
            "projected_final": 2450,
            "change": "+150"
        },
        "trades_available": {
            "remaining": 8,
            "used": 2,
            "recommended_this_week": 1
        },
        "salary_cap": {
            "total_budget": 11800000,
            "remaining": 1305000,
            "percentage_used": 88.9,
            "optimization_score": 8.4
        },
        "injury_oracle": [
            {
                "player_name": "Latrell Mitchell",
                "team": "SOU",
                "status": "Doubtful",
                "injury_type": "Calf tightness - Available",
                "risk_level": "medium",
                "updated": "2 hours ago"
            },
            {
                "player_name": "Tom Trbojevic",
                "team": "MAN",
                "status": "Out",
                "injury_type": "43.1% owned, High Impact",
                "risk_level": "high",
                "updated": "1 hour ago"
            },
            {
                "player_name": "Cameron Munster",
                "team": "MEL",
                "status": "Monitor",
                "injury_type": "Hip flexor, minor impact",
                "risk_level": "low",
                "updated": "3 hours ago"
            }
        ],
        "trade_recommendations": [
            {
                "player_out": {
                    "name": "Nathan Cleary",
                    "team": "PEN",
                    "position": "HLF",
                    "price": 850000,
                    "avg_points": 65.2,
                    "form": "Declining"
                },
                "player_in": {
                    "name": "Daly Cherry-Evans",
                    "team": "MAN",
                    "position": "HLF",
                    "price": 720000,
                    "avg_points": 58.2,
                    "form": "Rising"
                },
                "confidence": 87,
                "projected_gain": "+180 pts",
                "action": "Execute"
            },
            {
                "player_out": {
                    "name": "Ryan Papenhuyzen",
                    "team": "MEL",
                    "position": "FLB",
                    "price": 650000,
                    "avg_points": 48.7,
                    "form": "Avg"
                },
                "player_in": {
                    "name": "James Tedesco",
                    "team": "SYD",
                    "position": "FLB",
                    "price": 680000,
                    "avg_points": 48.9,
                    "form": "Rising"
                },
                "confidence": 79,
                "projected_gain": "+145 pts",
                "action": "Execute"
            },
            {
                "player_out": {
                    "name": "Payne Haas",
                    "team": "BRI",
                    "position": "FRF",
                    "price": 740000,
                    "avg_points": 52.1,
                    "form": "Avg"
                },
                "player_in": {
                    "name": "James Fisher-Harris",
                    "team": "PEN",
                    "position": "FRF",
                    "price": 650000,
                    "avg_points": 48.7,
                    "form": "Rising"
                },
                "confidence": 52,
                "projected_gain": "+145 pts",
                "action": "Execute"
            }
        ],
        "captain_optimizer": {
            "recommended_captain": "Daly Cherry-Evans",
            "confidence": 79.5,
            "projected_score": "vs BYE (Storm)",
            "ownership": "32.5% owned, vs avg",
            "form_rating": 8.5,
            "recent_scores": [82, 77, 84, 45, 91],
            "weather_impact": "No significant impact"
        }
    }

@app.get("/analytics/live-scoring")
async def get_live_scoring():
    """Get real-time scoring updates."""
    return {
        "round_number": 16,
        "live_scores": [
            {
                "player_name": "Nathan Cleary",
                "team": "PEN",
                "current_score": 72,
                "projected_final": 78,
                "minutes_played": 65,
                "status": "playing"
            },
            {
                "player_name": "Daly Cherry-Evans",
                "team": "MAN",
                "current_score": 45,
                "projected_final": 62,
                "minutes_played": 45,
                "status": "playing"
            }
        ],
        "team_total": {
            "current": 2280,
            "projected": 2450,
            "players_playing": 13,
            "players_yet_to_play": 5
        }
    }

@app.get("/players/intelligence/{player_id}")
async def get_player_intelligence(player_id: int):
    """Get comprehensive player intelligence data."""
    # Mock data based on beta design
    player_data = {
        1: {
            "player_profile": {
                "name": "Nathan Cleary",
                "team": "Penrith Panthers",
                "position": "Halfback",
                "jersey_number": 7,
                "current_price": 785000,
                "ownership": 68.4,
                "form_rating": 92,
                "breakeven": 78.3
            },
            "performance_metrics": {
                "avg_score": 68.4,
                "season_high": 92,
                "season_low": 34,
                "consistency": 85,
                "recent_form": [72, 68, 84, 45, 78]
            },
            "projections": {
                "next_week": 72,
                "next_4_weeks": [72, 68, 75, 71],
                "season_projection": 1650,
                "confidence": 87
            },
            "analytics": {
                "scoring_pattern": "Consistent high scorer",
                "price_trend": "Stable with slight upward trend",
                "injury_risk": "Low - No current concerns",
                "weather_impact": "Minimal impact in wet conditions"
            }
        }
    }

    if player_id not in player_data:
        raise HTTPException(status_code=404, detail="Player not found")

    return player_data[player_id]

@app.get("/trade-analyzer/compare")
async def compare_players(player_out_id: int, player_in_id: int):
    """Advanced player comparison for trade analysis."""
    return {
        "comparison": {
            "player_out": {
                "name": "Nathan Cleary",
                "current_price": 850000,
                "avg_points": 68.5,
                "form_rating": 72.3,
                "ownership": 78.2,
                "injury_risk": "Low",
                "breakeven": 45
            },
            "player_in": {
                "name": "Daly Cherry-Evans",
                "current_price": 720000,
                "avg_points": 58.2,
                "form_rating": 61.9,
                "ownership": 45.6,
                "injury_risk": "Medium",
                "breakeven": 38
            }
        },
        "trade_impact": {
            "price_difference": -130000,
            "points_difference": "+12.5 pts over 4 weeks",
            "risk_assessment": "Medium risk due to injury history",
            "confidence": 89,
            "recommendation": "Execute"
        },
        "head_to_head": {
            "better_value": "Daly Cherry-Evans",
            "higher_ceiling": "Nathan Cleary",
            "better_form": "Nathan Cleary",
            "overall_winner": "Daly Cherry-Evans"
        }
    }

# =============================================================================
# MAIN
# =============================================================================

if __name__ == "__main__":
    print("🚀 Starting FantasyPro Development API Server...")
    print("📊 Dashboard: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("👤 Demo Login: <EMAIL> / demo123")

    if SUPERCOACH_AVAILABLE:
        print("🏈 SuperCoach endpoints: LOADED with real NRL data!")
        print("📊 SuperCoach dashboard: http://localhost:8000/supercoach/dashboard")
        print("👥 All players: http://localhost:8000/supercoach/players")
        print("🤖 AI recommendations: http://localhost:8000/supercoach/ai/recommendations")
    else:
        print("⚠️ SuperCoach endpoints: NOT AVAILABLE (using mock data only)")
    
    uvicorn.run(
        app,  # Pass the app object directly instead of string
        host="0.0.0.0",
        port=8000,
        reload=False,  # Disable reload to avoid virtual environment issues
        log_level="info"
    )
