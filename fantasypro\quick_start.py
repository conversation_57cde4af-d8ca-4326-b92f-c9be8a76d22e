#!/usr/bin/env python3
"""
FantasyPro Quick Start Script
Sets up environment and tests basic functionality for client demo.
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def setup_environment():
    """Set up environment variables for testing."""
    print("🔧 Setting up environment variables...")
    
    # Set required environment variables
    os.environ["OPENAI_API_KEY"] = "not-needed"
    os.environ["OPENAI_API_BASE"] = "http://localhost:1234/v1"
    os.environ["NODE_ENV"] = "development"
    os.environ["FLASK_ENV"] = "development"
    
    print("✅ Environment variables set")

def test_imports():
    """Test critical imports."""
    print("🔍 Testing critical imports...")
    
    try:
        import fastapi
        print(f"  ✅ FastAPI: {fastapi.__version__}")
    except ImportError as e:
        print(f"  ❌ FastAPI import failed: {e}")
        return False
    
    try:
        import uvicorn
        print(f"  ✅ Uvicorn: {uvicorn.__version__}")
    except ImportError as e:
        print(f"  ❌ Uvicorn import failed: {e}")
        return False
    
    try:
        import praisonai
        print("  ✅ PraisonAI imported successfully")
    except ImportError as e:
        print(f"  ❌ PraisonAI import failed: {e}")
        return False
    
    try:
        import praisonaiagents
        print("  ✅ PraisonAI Agents imported successfully")
    except ImportError as e:
        print(f"  ❌ PraisonAI Agents import failed: {e}")
        return False
    
    print("✅ All imports successful")
    return True

def create_demo_server():
    """Create a simple demo server."""
    print("🚀 Creating demo server...")
    
    server_code = '''
import os
import sys
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime

# Set environment variables
os.environ["OPENAI_API_KEY"] = "not-needed"
os.environ["OPENAI_API_BASE"] = "http://localhost:1234/v1"

app = FastAPI(title="FantasyPro Demo", version="1.0.0-demo")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "FantasyPro Demo API", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/players")
async def players():
    return {
        "players": [
            {"id": 1, "name": "Nathan Cleary", "team": "Panthers", "price": 750000},
            {"id": 2, "name": "James Tedesco", "team": "Roosters", "price": 820000}
        ]
    }

if __name__ == "__main__":
    print("🏈 FantasyPro Demo Server Starting...")
    print("📊 API: http://localhost:8000")
    print("📚 Docs: http://localhost:8000/docs")
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    with open("demo_server.py", "w") as f:
        f.write(server_code)
    
    print("✅ Demo server created")

def start_server():
    """Start the demo server."""
    print("🚀 Starting demo server...")
    
    try:
        # Start server in background
        process = subprocess.Popen(
            [sys.executable, "demo_server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Test if server is running
        import requests
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("✅ Server is running successfully!")
                print("📊 API: http://localhost:8000")
                print("📚 Docs: http://localhost:8000/docs")
                print("👥 Players: http://localhost:8000/players")
                return True
            else:
                print(f"❌ Server responded with status: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Server test failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return False

def main():
    """Main function."""
    print("🏈 FantasyPro Quick Start")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    
    # Test imports
    if not test_imports():
        print("❌ Import tests failed. Please check your installation.")
        return False
    
    # Create demo server
    create_demo_server()
    
    # Start server
    if start_server():
        print("\n🎉 FantasyPro is ready for client demo!")
        print("=" * 50)
        return True
    else:
        print("\n❌ Failed to start server")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1) 