/**
 * Server-side NRL Data Scraping API
 * Scrapes NRL SuperCoach data from multiple sources
 */

import { NextApiRequest, NextApiResponse } from 'next';
import * as cheerio from 'cheerio';

// Types
interface ScrapedPlayer {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  points: number;
  average: number;
  form: number;
  ownership?: number;
  breakeven?: number;
  price_change?: number;
  games_played?: number;
  source: string;
  scraped_at: string;
}

// Rate limiting
let lastRequestTime = 0;
const REQUEST_DELAY = 2000; // 2 seconds between requests

async function rateLimitedFetch(url: string): Promise<Response> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < REQUEST_DELAY) {
    await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY - timeSinceLastRequest));
  }
  
  lastRequestTime = Date.now();

  const response = await fetch(url, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response;
}

function extractNumber(text: string): number {
  if (!text) return 0;
  const cleaned = text.replace(/[,$%]/g, '').trim();
  const match = cleaned.match(/[\d.]+/);
  return match ? parseFloat(match[0]) : 0;
}

function generatePlayerId(name: string, team: string): string {
  return `${name.toLowerCase().replace(/\s+/g, '_')}_${team.toLowerCase().replace(/\s+/g, '_')}`;
}

function normalizeTeamName(team: string): string {
  const teamMappings: { [key: string]: string } = {
    'broncos': 'Brisbane Broncos',
    'roosters': 'Sydney Roosters',
    'storm': 'Melbourne Storm',
    'panthers': 'Penrith Panthers',
    'sharks': 'Cronulla Sharks',
    'sea eagles': 'Manly Sea Eagles',
    'rabbitohs': 'South Sydney Rabbitohs',
    'eels': 'Parramatta Eels',
    'knights': 'Newcastle Knights',
    'raiders': 'Canberra Raiders',
    'titans': 'Gold Coast Titans',
    'dragons': 'St George Illawarra Dragons',
    'bulldogs': 'Canterbury Bulldogs',
    'tigers': 'Wests Tigers',
    'cowboys': 'North Queensland Cowboys',
    'warriors': 'New Zealand Warriors',
    'dolphins': 'Dolphins'
  };

  const normalized = team.toLowerCase().trim();
  for (const [key, value] of Object.entries(teamMappings)) {
    if (normalized.includes(key)) {
      return value;
    }
  }
  return team;
}

function normalizePosition(position: string): string {
  const positionMappings: { [key: string]: string } = {
    'fullback': 'FLB',
    'wing': 'CTW',
    'winger': 'CTW',
    'centre': 'CTW',
    'center': 'CTW',
    'five-eighth': '5/8',
    'five eighth': '5/8',
    'halfback': 'HFB',
    'half': 'HFB',
    'hooker': 'HOK',
    'prop': 'FRF',
    'front row': 'FRF',
    'second row': '2RF',
    'second-row': '2RF',
    'back row': '2RF',
    'lock': 'LCK',
    'loose forward': 'LCK'
  };

  const normalized = position.toLowerCase().trim();
  return positionMappings[normalized] || position.toUpperCase();
}

async function scrapeNRLSuperCoachStats(): Promise<ScrapedPlayer[]> {
  console.log('🕷️ Scraping nrlsupercoachstats.com...');

  try {
    // Use the correct stats table URL
    const url = 'https://www.nrlsupercoachstats.com/stats.php?year=2025';
    const response = await rateLimitedFetch(url);
    const html = await response.text();
    const $ = cheerio.load(html);

    const players: ScrapedPlayer[] = [];
    const timestamp = new Date().toISOString();

    // Try multiple selectors to find player data
    const selectors = [
      'table tbody tr',
      '.player-row',
      '.player-item',
      '[data-player]',
      '.stats-row'
    ];

    let foundRows = false;
    
    for (const selector of selectors) {
      const rows = $(selector);
      if (rows.length > 0) {
        console.log(`📊 Found ${rows.length} rows with selector: ${selector}`);
        foundRows = true;

        rows.each((index, element) => {
          try {
            const $row = $(element);
            
            // Try different ways to extract player data
            const name = $row.find('.player-name, .name, td:nth-child(1), td:first-child').text().trim() ||
                         $row.find('a').first().text().trim() ||
                         $row.children().first().text().trim();
            
            const team = $row.find('.team, .player-team, td:nth-child(2)').text().trim() ||
                         $row.children().eq(1).text().trim();
            
            const position = $row.find('.position, .pos, td:nth-child(3)').text().trim() ||
                            $row.children().eq(2).text().trim();
            
            const priceText = $row.find('.price, .salary, td:nth-child(4)').text().trim() ||
                             $row.children().eq(3).text().trim();
            
            const pointsText = $row.find('.points, .total-points, td:nth-child(5)').text().trim() ||
                              $row.children().eq(4).text().trim();
            
            const averageText = $row.find('.average, .avg, td:nth-child(6)').text().trim() ||
                               $row.children().eq(5).text().trim();

            if (name && name.length > 2 && team && position) {
              const player: ScrapedPlayer = {
                id: generatePlayerId(name, team),
                name: name,
                team: normalizeTeamName(team),
                position: normalizePosition(position),
                price: extractNumber(priceText),
                points: extractNumber(pointsText),
                average: extractNumber(averageText),
                form: 0, // Will be calculated later
                source: 'nrlsupercoachstats.com',
                scraped_at: timestamp
              };

              players.push(player);
            }
          } catch (error) {
            console.warn(`⚠️ Error parsing row ${index}:`, error);
          }
        });

        if (players.length > 0) break; // Found data, stop trying other selectors
      }
    }

    if (!foundRows) {
      console.log('📄 No standard table rows found, trying alternative extraction...');
      
      // Try to extract any text that looks like player data
      const allText = $('body').text();
      console.log('📄 Page text sample:', allText.substring(0, 500));
    }

    console.log(`✅ Scraped ${players.length} players from nrlsupercoachstats.com`);
    return players;

  } catch (error) {
    console.error('❌ Error scraping nrlsupercoachstats.com:', error);
    throw error;
  }
}

async function scrapeNRLSuperCoachLive(): Promise<any[]> {
  console.log('🕷️ Scraping footystatistics.com for ownership data...');

  try {
    // Use footystatistics.com for ownership data
    const url = 'https://www.footystatistics.com/ownership.php';
    const response = await rateLimitedFetch(url);
    const html = await response.text();
    const $ = cheerio.load(html);

    const ownershipData: any[] = [];
    const timestamp = new Date().toISOString();

    // Try multiple selectors for ownership data
    const selectors = [
      'table tbody tr',
      '.ownership-row',
      '.player-ownership',
      '[data-ownership]'
    ];

    for (const selector of selectors) {
      const rows = $(selector);
      if (rows.length > 0) {
        console.log(`📊 Found ${rows.length} ownership rows with selector: ${selector}`);

        rows.each((index, element) => {
          try {
            const $row = $(element);
            
            const name = $row.find('.player-name, .name, td:nth-child(1)').text().trim() ||
                         $row.children().first().text().trim();
            
            const team = $row.find('.team, .player-team, td:nth-child(2)').text().trim() ||
                         $row.children().eq(1).text().trim();
            
            const ownershipText = $row.find('.ownership, .owned, td:nth-child(3)').text().trim() ||
                                 $row.children().eq(2).text().trim();
            
            const priceChangeText = $row.find('.price-change, .change, td:nth-child(4)').text().trim() ||
                                   $row.children().eq(3).text().trim();

            if (name && name.length > 2 && team) {
              const ownershipInfo = {
                id: generatePlayerId(name, team),
                name: name,
                team: normalizeTeamName(team),
                ownership: extractNumber(ownershipText),
                price_change: extractNumber(priceChangeText),
                scraped_at: timestamp
              };

              ownershipData.push(ownershipInfo);
            }
          } catch (error) {
            console.warn(`⚠️ Error parsing ownership row ${index}:`, error);
          }
        });

        if (ownershipData.length > 0) break;
      }
    }

    console.log(`✅ Scraped ownership data for ${ownershipData.length} players from nrlsupercoachlive.com`);
    return ownershipData;

  } catch (error) {
    console.error('❌ Error scraping nrlsupercoachlive.com:', error);
    throw error;
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { source } = req.query;

  try {
    console.log('🚀 Starting NRL data scraping...');
    
    let result;

    if (source === 'stats') {
      const players = await scrapeNRLSuperCoachStats();
      result = { players, source: 'nrlsupercoachstats.com' };
    } else if (source === 'ownership') {
      const ownership = await scrapeNRLSuperCoachLive();
      result = { ownership, source: 'nrlsupercoachlive.com' };
    } else {
      // Scrape both sources
      const [players, ownership] = await Promise.allSettled([
        scrapeNRLSuperCoachStats(),
        scrapeNRLSuperCoachLive()
      ]);

      const playersData = players.status === 'fulfilled' ? players.value : [];
      const ownershipData = ownership.status === 'fulfilled' ? ownership.value : [];
      
      // Merge ownership data into players
      const ownershipMap = new Map(ownershipData.map((o: any) => [o.id, o]));
      
      playersData.forEach((player: ScrapedPlayer) => {
        const ownershipInfo = ownershipMap.get(player.id);
        if (ownershipInfo) {
          player.ownership = ownershipInfo.ownership;
          player.price_change = ownershipInfo.price_change;
        }
      });

      result = {
        players: playersData,
        ownership: ownershipData,
        merged_count: playersData.filter((p: ScrapedPlayer) => p.ownership !== undefined).length,
        errors: [
          ...(players.status === 'rejected' ? [`nrlsupercoachstats.com: ${players.reason}`] : []),
          ...(ownership.status === 'rejected' ? [`nrlsupercoachlive.com: ${ownership.reason}`] : [])
        ]
      };
    }

    res.status(200).json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      message: `Successfully scraped NRL data`
    });

  } catch (error) {
    console.error('💥 Scraping error:', error);
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown scraping error',
      timestamp: new Date().toISOString()
    });
  }
}
