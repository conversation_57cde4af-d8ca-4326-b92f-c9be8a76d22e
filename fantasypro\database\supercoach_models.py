#!/usr/bin/env python3
"""
SuperCoach Database Models

Optimized database schema for storing NRL SuperCoach statistics,
player data, break-evens, prices, and historical performance data.
"""

from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Boolean, Text, JSON,
    ForeignKey, Index, UniqueConstraint, CheckConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, backref
from datetime import datetime
from typing import Optional, List, Dict, Any

Base = declarative_base()

class NRLTeam(Base):
    """NRL Teams table"""
    __tablename__ = 'nrl_teams'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    abbreviation = Column(String(10), nullable=False, unique=True)
    city = Column(String(50))
    founded_year = Column(Integer)
    home_venue = Column(String(100))
    primary_color = Column(String(7))  # Hex color code
    secondary_color = Column(String(7))
    logo_url = Column(String(255))
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    players = relationship("SuperCoachPlayer", back_populates="team")
    home_fixtures = relationship("NRLFixture", foreign_keys="NRLFixture.home_team_id", back_populates="home_team")
    away_fixtures = relationship("NRLFixture", foreign_keys="NRLFixture.away_team_id", back_populates="away_team")
    
    def __repr__(self):
        return f"<NRLTeam(name='{self.name}', abbr='{self.abbreviation}')>"

class SuperCoachPlayer(Base):
    """SuperCoach Players with comprehensive statistics"""
    __tablename__ = 'supercoach_players'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    team_id = Column(Integer, ForeignKey('nrl_teams.id'), nullable=False)
    position = Column(String(20), nullable=False)
    jersey_number = Column(Integer)
    
    # Current season data
    current_price = Column(Float)
    current_breakeven = Column(Integer)
    season_points = Column(Integer, default=0)
    season_average = Column(Float)
    games_played = Column(Integer, default=0)
    
    # Performance metrics
    form_rating = Column(Float)  # Recent form score
    ownership_percentage = Column(Float)
    minutes_per_game = Column(Float)
    consistency_rating = Column(Float)
    value_score = Column(Float)  # Price vs performance ratio
    
    # Season statistics
    season_high = Column(Integer)
    season_low = Column(Integer)
    tries = Column(Integer, default=0)
    try_assists = Column(Integer, default=0)
    goals = Column(Integer, default=0)
    field_goals = Column(Integer, default=0)
    tackles = Column(Integer, default=0)
    missed_tackles = Column(Integer, default=0)
    runs = Column(Integer, default=0)
    run_metres = Column(Integer, default=0)
    linebreaks = Column(Integer, default=0)
    linebreak_assists = Column(Integer, default=0)
    offloads = Column(Integer, default=0)
    
    # Additional data
    recent_scores = Column(JSON)  # Last 5 game scores
    injury_status = Column(String(50))
    suspension_status = Column(String(50))
    
    # Metadata
    is_active = Column(Boolean, default=True)
    last_updated = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    team = relationship("NRLTeam", back_populates="players")
    price_history = relationship("PlayerPriceHistory", back_populates="player")
    game_stats = relationship("PlayerGameStats", back_populates="player")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_player_team_position', 'team_id', 'position'),
        Index('idx_player_price', 'current_price'),
        Index('idx_player_breakeven', 'current_breakeven'),
        Index('idx_player_form', 'form_rating'),
        Index('idx_player_ownership', 'ownership_percentage'),
        Index('idx_player_active', 'is_active'),
    )
    
    def __repr__(self):
        return f"<SuperCoachPlayer(name='{self.name}', team='{self.team.name if self.team else 'Unknown'}', position='{self.position}')>"

class PlayerPriceHistory(Base):
    """Historical price changes for players"""
    __tablename__ = 'player_price_history'
    
    id = Column(Integer, primary_key=True)
    player_id = Column(Integer, ForeignKey('supercoach_players.id'), nullable=False)
    round_number = Column(Integer, nullable=False)
    season_year = Column(Integer, nullable=False)
    
    price_before = Column(Float, nullable=False)
    price_after = Column(Float, nullable=False)
    price_change = Column(Float, nullable=False)
    breakeven_before = Column(Integer)
    breakeven_after = Column(Integer)
    
    # Performance data that triggered the change
    round_score = Column(Integer)
    three_round_average = Column(Float)
    
    # Metadata
    recorded_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    player = relationship("SuperCoachPlayer", back_populates="price_history")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('player_id', 'round_number', 'season_year', name='uq_player_round_season'),
        Index('idx_price_history_player_round', 'player_id', 'round_number'),
        Index('idx_price_history_season', 'season_year'),
    )

class PlayerGameStats(Base):
    """Individual game statistics for players"""
    __tablename__ = 'player_game_stats'
    
    id = Column(Integer, primary_key=True)
    player_id = Column(Integer, ForeignKey('supercoach_players.id'), nullable=False)
    fixture_id = Column(Integer, ForeignKey('nrl_fixtures.id'), nullable=False)
    round_number = Column(Integer, nullable=False)
    season_year = Column(Integer, nullable=False)
    
    # Game performance
    supercoach_score = Column(Integer)
    minutes_played = Column(Integer)
    
    # Detailed statistics
    tries = Column(Integer, default=0)
    try_assists = Column(Integer, default=0)
    goals = Column(Integer, default=0)
    field_goals = Column(Integer, default=0)
    tackles = Column(Integer, default=0)
    missed_tackles = Column(Integer, default=0)
    runs = Column(Integer, default=0)
    run_metres = Column(Integer, default=0)
    linebreaks = Column(Integer, default=0)
    linebreak_assists = Column(Integer, default=0)
    offloads = Column(Integer, default=0)
    errors = Column(Integer, default=0)
    penalties = Column(Integer, default=0)
    sin_bins = Column(Integer, default=0)
    send_offs = Column(Integer, default=0)
    
    # Additional metrics
    involvement_percentage = Column(Float)
    effective_runs = Column(Integer, default=0)
    dummy_half_runs = Column(Integer, default=0)
    
    # Metadata
    recorded_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    player = relationship("SuperCoachPlayer", back_populates="game_stats")
    fixture = relationship("NRLFixture", back_populates="player_stats")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('player_id', 'fixture_id', name='uq_player_fixture'),
        Index('idx_game_stats_player_round', 'player_id', 'round_number'),
        Index('idx_game_stats_season', 'season_year'),
        Index('idx_game_stats_score', 'supercoach_score'),
    )

class NRLFixture(Base):
    """NRL fixtures and match information"""
    __tablename__ = 'nrl_fixtures'
    
    id = Column(Integer, primary_key=True)
    season_year = Column(Integer, nullable=False)
    round_number = Column(Integer, nullable=False)
    
    home_team_id = Column(Integer, ForeignKey('nrl_teams.id'), nullable=False)
    away_team_id = Column(Integer, ForeignKey('nrl_teams.id'), nullable=False)
    
    # Match details
    kickoff_time = Column(DateTime)
    venue = Column(String(100))
    weather_conditions = Column(String(100))
    temperature = Column(Float)
    
    # Scores
    home_score = Column(Integer)
    away_score = Column(Integer)
    
    # SuperCoach difficulty ratings
    home_difficulty = Column(Float)  # 1-5 scale
    away_difficulty = Column(Float)
    
    # Match status
    status = Column(String(20), default='scheduled')  # scheduled, live, completed, postponed
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    home_team = relationship("NRLTeam", foreign_keys=[home_team_id], back_populates="home_fixtures")
    away_team = relationship("NRLTeam", foreign_keys=[away_team_id], back_populates="away_fixtures")
    player_stats = relationship("PlayerGameStats", back_populates="fixture")
    
    # Constraints
    __table_args__ = (
        CheckConstraint('home_team_id != away_team_id', name='check_different_teams'),
        Index('idx_fixture_round_season', 'round_number', 'season_year'),
        Index('idx_fixture_teams', 'home_team_id', 'away_team_id'),
        Index('idx_fixture_kickoff', 'kickoff_time'),
    )

class PositionVsTeamStats(Base):
    """Position vs Team performance statistics"""
    __tablename__ = 'position_vs_team_stats'
    
    id = Column(Integer, primary_key=True)
    season_year = Column(Integer, nullable=False)
    position = Column(String(20), nullable=False)
    team_id = Column(Integer, ForeignKey('nrl_teams.id'), nullable=False)
    
    # Performance metrics
    average_points = Column(Float)
    total_points = Column(Integer)
    games_played = Column(Integer)
    players_used = Column(Integer)
    
    # Additional statistics
    highest_score = Column(Integer)
    lowest_score = Column(Integer)
    consistency_rating = Column(Float)
    
    # Metadata
    last_updated = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    team = relationship("NRLTeam")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('season_year', 'position', 'team_id', name='uq_season_position_team'),
        Index('idx_position_team_season', 'position', 'team_id', 'season_year'),
    )
