import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface ScrapedPlayer {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  points: number;
  average: number;
  ownership?: number;
  price_change?: number;
  source: string;
  scraped_at: string;
}

const WebScrapingTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testScraping = async (source?: string) => {
    setTesting(true);
    setResult(null);
    setError(null);

    try {
      console.log(`🕷️ Testing web scraping${source ? ` for ${source}` : ''}...`);
      
      const url = `/api/scrape-nrl${source ? `?source=${source}` : ''}`;
      const response = await fetch(url);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Scraping Success!', data);
        setResult(data);
      } else {
        const errorData = await response.json();
        console.error('❌ Scraping Error:', errorData);
        setError(errorData.error || 'Scraping failed');
      }
      
    } catch (err) {
      console.error('💥 Request Error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setTesting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card-premium p-6"
    >
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold themed-text-primary">NRL Web Scraping Test</h2>
          <p className="text-sm themed-text-tertiary">Test real data scraping from NRL sources</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => testScraping('stats')}
            disabled={testing}
            className="btn-secondary btn-ripple"
          >
            {testing ? 'Testing...' : 'Test Stats'}
          </button>
          <button
            onClick={() => testScraping('ownership')}
            disabled={testing}
            className="btn-accent btn-ripple"
          >
            {testing ? 'Testing...' : 'Test Ownership'}
          </button>
          <button
            onClick={() => testScraping()}
            disabled={testing}
            className="btn-primary btn-ripple"
          >
            {testing ? 'Testing...' : 'Test All Sources'}
          </button>
        </div>
      </div>

      {/* Loading State */}
      {testing && (
        <div className="flex items-center justify-center py-8">
          <div className="loading-spinner w-8 h-8"></div>
          <span className="ml-3 themed-text-secondary">Scraping NRL data...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
          <h3 className="font-semibold text-red-400 mb-2">❌ Scraping Failed</h3>
          <p className="text-sm text-red-300">{error}</p>
          
          <div className="mt-4 text-xs themed-text-tertiary">
            <p><strong>Possible issues:</strong></p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Website structure changed</li>
              <li>Rate limiting or blocking</li>
              <li>Network connectivity issues</li>
              <li>CORS or server-side errors</li>
            </ul>
          </div>
        </div>
      )}

      {/* Success State */}
      {result && result.success && (
        <div className="space-y-6">
          <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
            <h3 className="font-semibold text-green-400 mb-2">✅ Scraping Successful!</h3>
            <p className="text-sm text-green-300">Real NRL data retrieved from web sources</p>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {result.data.players && (
              <div className="text-center p-4 bg-blue-500/10 rounded-lg">
                <div className="text-2xl font-bold text-blue-400">
                  {result.data.players.length}
                </div>
                <div className="text-sm themed-text-secondary">Players Scraped</div>
              </div>
            )}
            
            {result.data.ownership && (
              <div className="text-center p-4 bg-purple-500/10 rounded-lg">
                <div className="text-2xl font-bold text-purple-400">
                  {result.data.ownership.length}
                </div>
                <div className="text-sm themed-text-secondary">Ownership Records</div>
              </div>
            )}
            
            {result.data.merged_count !== undefined && (
              <div className="text-center p-4 bg-green-500/10 rounded-lg">
                <div className="text-2xl font-bold text-green-400">
                  {result.data.merged_count}
                </div>
                <div className="text-sm themed-text-secondary">Merged Records</div>
              </div>
            )}
          </div>

          {/* Sample Players */}
          {result.data.players && result.data.players.length > 0 && (
            <div className="p-4 bg-slate-800 rounded-lg">
              <h4 className="font-semibold themed-text-primary mb-4">Sample Players (First 5)</h4>
              <div className="space-y-3">
                {result.data.players.slice(0, 5).map((player: ScrapedPlayer, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-700 rounded">
                    <div>
                      <div className="font-medium themed-text-primary">{player.name}</div>
                      <div className="text-sm themed-text-tertiary">
                        {player.team} • {player.position}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium themed-text-primary">
                        {player.price > 0 ? formatCurrency(player.price) : 'N/A'}
                      </div>
                      <div className="text-xs themed-text-tertiary">
                        {player.points > 0 ? `${player.points} pts` : 'No points'}
                        {player.ownership ? ` • ${player.ownership.toFixed(1)}% owned` : ''}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Errors */}
          {result.data.errors && result.data.errors.length > 0 && (
            <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
              <h4 className="font-semibold text-yellow-400 mb-2">⚠️ Partial Errors</h4>
              <ul className="text-sm text-yellow-300 space-y-1">
                {result.data.errors.map((error: string, index: number) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Raw Data */}
          <details className="p-4 bg-slate-800 rounded-lg">
            <summary className="cursor-pointer font-semibold themed-text-primary mb-2">
              View Raw Scraped Data
            </summary>
            <pre className="text-xs bg-slate-900 p-3 rounded overflow-auto max-h-64 mt-2">
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </details>

          {/* Next Steps */}
          <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <h4 className="font-semibold text-blue-400 mb-2">🚀 Next Steps</h4>
            <ul className="text-sm text-blue-300 space-y-1">
              <li>✅ Web scraping is working!</li>
              <li>🔄 Integrate scraped data into Players page</li>
              <li>🔄 Update My Team page with real data</li>
              <li>🔄 Set up automated scraping schedule</li>
              <li>🔄 Add data validation and cleaning</li>
            </ul>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 pt-6 border-t themed-border">
        <h4 className="font-semibold themed-text-primary mb-3">Data Sources</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong className="themed-text-secondary">nrlsupercoachstats.com:</strong>
            <div className="text-xs themed-text-tertiary mt-1">
              Player statistics, points, averages, team info
            </div>
          </div>
          <div>
            <strong className="themed-text-secondary">nrlsupercoachlive.com:</strong>
            <div className="text-xs themed-text-tertiary mt-1">
              Ownership percentages, price changes, trends
            </div>
          </div>
        </div>
        
        <div className="mt-4 p-3 bg-blue-500/10 rounded text-xs themed-text-tertiary">
          <strong>Note:</strong> Web scraping may take 10-30 seconds due to rate limiting and multiple sources.
          The scrapers are designed to be respectful of the target websites.
        </div>
      </div>
    </motion.div>
  );
};

export default WebScrapingTest;
