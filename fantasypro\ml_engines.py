#!/usr/bin/env python3
"""
FantasyPro Machine Learning Engines
Advanced ML algorithms for player predictions, injury analysis, and trade optimization
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)

@dataclass
class PlayerData:
    """Comprehensive player data for ML analysis"""
    player_id: str
    name: str
    team: str
    position: str
    price: float
    recent_scores: List[float]
    season_points: int
    games_played: int
    injury_history: List[Dict]
    ownership_percentage: float
    form_rating: float = 0.0
    breakeven: int = 0
    price_change: float = 0.0

class InjuryPredictionEngine:
    """ML engine for predicting injury risks and impacts"""
    
    def __init__(self):
        self.name = "Injury Prediction Engine"
        self.risk_factors = {
            'age': 0.15,
            'injury_history': 0.35,
            'position_risk': 0.20,
            'workload': 0.15,
            'recent_form': 0.10,
            'team_schedule': 0.05
        }
        
        # Position-based injury risk multipliers
        self.position_risk_multipliers = {
            'Prop': 1.3,
            'Hooker': 1.2,
            'Second-row': 1.25,
            'Lock': 1.2,
            'Halfback': 0.9,
            'Five-eighth': 0.95,
            'Centre': 1.1,
            'Winger': 0.8,
            'Fullback': 1.0
        }
    
    def predict_injury_risk(self, player: PlayerData, injury_reports: List[Dict]) -> Dict[str, Any]:
        """Predict injury risk for a player"""
        
        # Age factor (higher age = higher risk)
        age_factor = min(1.0, (player.price / 1000000) * 0.1)  # Approximate age from price
        
        # Injury history analysis
        history_factor = self._analyze_injury_history(player.injury_history)
        
        # Position risk
        position_factor = self.position_risk_multipliers.get(player.position, 1.0)
        
        # Workload analysis (games played vs expected)
        workload_factor = min(1.2, player.games_played / 20) if player.games_played > 15 else 0.8
        
        # Recent form impact (poor form might indicate underlying issues)
        form_factor = 1.0
        if player.recent_scores:
            avg_recent = sum(player.recent_scores) / len(player.recent_scores)
            expected_avg = player.season_points / max(1, player.games_played)
            if avg_recent < expected_avg * 0.8:
                form_factor = 1.2
        
        # Current injury status
        current_injury_factor = self._check_current_injuries(player.name, injury_reports)
        
        # Calculate overall risk
        base_risk = 0.1  # Everyone has base 10% risk
        calculated_risk = (
            base_risk +
            age_factor * self.risk_factors['age'] +
            history_factor * self.risk_factors['injury_history'] +
            (position_factor - 1) * self.risk_factors['position_risk'] +
            (workload_factor - 1) * self.risk_factors['workload'] +
            (form_factor - 1) * self.risk_factors['recent_form'] +
            current_injury_factor
        )
        
        risk_score = min(0.95, max(0.05, calculated_risk))
        
        # Risk category
        if risk_score < 0.2:
            risk_category = "Low"
        elif risk_score < 0.4:
            risk_category = "Medium"
        elif risk_score < 0.6:
            risk_category = "High"
        else:
            risk_category = "Very High"
        
        return {
            'player_id': player.player_id,
            'player_name': player.name,
            'risk_score': round(risk_score, 3),
            'risk_category': risk_category,
            'factors': {
                'age_factor': round(age_factor, 3),
                'history_factor': round(history_factor, 3),
                'position_factor': round(position_factor, 3),
                'workload_factor': round(workload_factor, 3),
                'form_factor': round(form_factor, 3),
                'current_injury': current_injury_factor > 0
            },
            'recommendation': self._generate_injury_recommendation(risk_score, risk_category)
        }
    
    def _analyze_injury_history(self, injury_history: List[Dict]) -> float:
        """Analyze player's injury history"""
        if not injury_history:
            return 0.0
        
        # Recent injuries (last 2 years) weighted more heavily
        recent_cutoff = datetime.now() - timedelta(days=730)
        recent_injuries = []
        older_injuries = []
        
        for injury in injury_history:
            injury_date = datetime.fromisoformat(injury.get('date', '2020-01-01'))
            if injury_date > recent_cutoff:
                recent_injuries.append(injury)
            else:
                older_injuries.append(injury)
        
        # Calculate severity scores
        recent_severity = sum(injury.get('severity', 1) for injury in recent_injuries)
        older_severity = sum(injury.get('severity', 1) for injury in older_injuries) * 0.5
        
        total_severity = recent_severity + older_severity
        return min(0.5, total_severity * 0.1)
    
    def _check_current_injuries(self, player_name: str, injury_reports: List[Dict]) -> float:
        """Check if player is currently injured"""
        for injury in injury_reports:
            if injury.get('player_name', '').lower() == player_name.lower():
                status = injury.get('injury_status', '').lower()
                if any(keyword in status for keyword in ['injured', 'out', 'doubtful']):
                    return 0.3  # High current injury factor
                elif 'questionable' in status:
                    return 0.15  # Medium current injury factor
        return 0.0
    
    def _generate_injury_recommendation(self, risk_score: float, risk_category: str) -> str:
        """Generate injury-based recommendation"""
        if risk_score < 0.2:
            return "Safe pick - low injury risk"
        elif risk_score < 0.4:
            return "Monitor closely - moderate injury risk"
        elif risk_score < 0.6:
            return "Consider alternatives - high injury risk"
        else:
            return "Avoid - very high injury risk"

class PerformancePredictionEngine:
    """ML engine for predicting player performance"""
    
    def __init__(self):
        self.name = "Performance Prediction Engine"
        self.feature_weights = {
            'recent_form': 0.30,
            'season_average': 0.25,
            'price_momentum': 0.15,
            'ownership_trend': 0.10,
            'injury_risk': 0.10,
            'fixture_difficulty': 0.10
        }
    
    def predict_performance(self, player: PlayerData, injury_risk: Dict) -> Dict[str, Any]:
        """Predict player performance for next round"""
        
        # Recent form analysis
        form_score = self._calculate_form_score(player.recent_scores)
        
        # Season average
        season_avg = player.season_points / max(1, player.games_played)
        
        # Price momentum (rising prices indicate good form)
        price_momentum = max(0.8, min(1.2, 1 + player.price_change / 100000))
        
        # Ownership trend impact
        ownership_impact = self._calculate_ownership_impact(player.ownership_percentage)
        
        # Injury risk impact
        injury_impact = 1 - (injury_risk.get('risk_score', 0.1) * 0.5)
        
        # Base prediction
        base_prediction = (
            form_score * self.feature_weights['recent_form'] +
            season_avg * self.feature_weights['season_average']
        )
        
        # Apply multipliers
        predicted_score = (
            base_prediction * 
            price_momentum * 
            ownership_impact * 
            injury_impact
        )
        
        # Calculate confidence
        confidence = self._calculate_confidence(player, injury_risk)
        
        # Performance category
        if predicted_score >= 70:
            category = "Premium"
        elif predicted_score >= 50:
            category = "Solid"
        elif predicted_score >= 35:
            category = "Value"
        else:
            category = "Avoid"
        
        return {
            'player_id': player.player_id,
            'player_name': player.name,
            'predicted_score': round(predicted_score, 1),
            'confidence': round(confidence, 2),
            'category': category,
            'factors': {
                'form_score': round(form_score, 1),
                'season_avg': round(season_avg, 1),
                'price_momentum': round(price_momentum, 2),
                'ownership_impact': round(ownership_impact, 2),
                'injury_impact': round(injury_impact, 2)
            }
        }
    
    def _calculate_form_score(self, recent_scores: List[float]) -> float:
        """Calculate weighted form score"""
        if not recent_scores:
            return 40.0  # Default average
        
        # Weight recent games more heavily
        weights = [0.4, 0.3, 0.2, 0.1] if len(recent_scores) >= 4 else [1.0]
        weights = weights[:len(recent_scores)]
        
        weighted_sum = sum(score * weight for score, weight in zip(recent_scores, weights))
        weight_sum = sum(weights)
        
        return weighted_sum / weight_sum if weight_sum > 0 else 40.0
    
    def _calculate_ownership_impact(self, ownership: float) -> float:
        """Calculate ownership impact on performance prediction"""
        # High ownership might indicate overvaluation
        if ownership > 50:
            return 0.95  # Slight penalty for very popular picks
        elif ownership < 5:
            return 1.05  # Slight bonus for differential picks
        else:
            return 1.0
    
    def _calculate_confidence(self, player: PlayerData, injury_risk: Dict) -> float:
        """Calculate prediction confidence"""
        confidence = 0.6  # Base confidence
        
        # More games = higher confidence
        if player.games_played > 15:
            confidence += 0.2
        elif player.games_played > 10:
            confidence += 0.1
        
        # Recent form data
        if len(player.recent_scores) >= 4:
            confidence += 0.15
        elif len(player.recent_scores) >= 2:
            confidence += 0.1
        
        # Lower injury risk = higher confidence
        confidence += (1 - injury_risk.get('risk_score', 0.1)) * 0.1
        
        return min(0.95, confidence)

class TradeOptimizationEngine:
    """ML engine for optimizing trades and team composition"""
    
    def __init__(self, injury_engine: InjuryPredictionEngine, 
                 performance_engine: PerformancePredictionEngine):
        self.name = "Trade Optimization Engine"
        self.injury_engine = injury_engine
        self.performance_engine = performance_engine
    
    def analyze_trade_opportunity(self, player_out: PlayerData, player_in: PlayerData, 
                                injury_reports: List[Dict]) -> Dict[str, Any]:
        """Analyze a specific trade opportunity"""
        
        # Get predictions for both players
        injury_out = self.injury_engine.predict_injury_risk(player_out, injury_reports)
        injury_in = self.injury_engine.predict_injury_risk(player_in, injury_reports)
        
        perf_out = self.performance_engine.predict_performance(player_out, injury_out)
        perf_in = self.performance_engine.predict_performance(player_in, injury_in)
        
        # Calculate trade metrics
        price_diff = player_in.price - player_out.price
        points_diff = perf_in['predicted_score'] - perf_out['predicted_score']
        risk_diff = injury_in['risk_score'] - injury_out['risk_score']
        
        # Trade value calculation
        if price_diff == 0:
            value_ratio = float('inf') if points_diff > 0 else 0
        else:
            value_ratio = points_diff / (price_diff / 100000) if price_diff > 0 else points_diff * 100
        
        # Trade recommendation
        if points_diff > 5 and risk_diff < 0.1:
            recommendation = "Strong Buy"
        elif points_diff > 2 and risk_diff < 0.2:
            recommendation = "Buy"
        elif points_diff > -2 and risk_diff < 0.1:
            recommendation = "Hold"
        elif points_diff < -5 or risk_diff > 0.3:
            recommendation = "Avoid"
        else:
            recommendation = "Neutral"
        
        return {
            'player_out': {
                'name': player_out.name,
                'predicted_score': perf_out['predicted_score'],
                'injury_risk': injury_out['risk_score'],
                'price': player_out.price
            },
            'player_in': {
                'name': player_in.name,
                'predicted_score': perf_in['predicted_score'],
                'injury_risk': injury_in['risk_score'],
                'price': player_in.price
            },
            'trade_metrics': {
                'price_difference': price_diff,
                'points_difference': round(points_diff, 1),
                'risk_difference': round(risk_diff, 3),
                'value_ratio': round(value_ratio, 2) if abs(value_ratio) != float('inf') else value_ratio
            },
            'recommendation': recommendation,
            'confidence': min(perf_out['confidence'], perf_in['confidence'])
        }

def main():
    """Test the ML engines"""
    print("🤖 Testing FantasyPro ML Engines")
    
    # Create sample player data
    sample_player = PlayerData(
        player_id="test_1",
        name="James Tedesco",
        team="Sydney Roosters",
        position="Fullback",
        price=817700,
        recent_scores=[85.4, 72.1, 91.2, 68.5],
        season_points=1200,
        games_played=15,
        injury_history=[],
        ownership_percentage=45.2
    )
    
    # Test engines
    injury_engine = InjuryPredictionEngine()
    performance_engine = PerformancePredictionEngine()
    
    injury_prediction = injury_engine.predict_injury_risk(sample_player, [])
    performance_prediction = performance_engine.predict_performance(sample_player, injury_prediction)
    
    print(f"Injury Risk: {injury_prediction}")
    print(f"Performance Prediction: {performance_prediction}")

if __name__ == "__main__":
    main()
