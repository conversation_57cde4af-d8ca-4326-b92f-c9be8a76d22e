#!/usr/bin/env python3
"""
SportRadar Setup Script
Helps configure SportRadar API integration for FantasyPro
"""

import os
import json
from sportradar_client import SportRadarNRLClient

def setup_sportradar():
    """
    Interactive setup for SportRadar API
    """
    print("🏈 FantasyPro SportRadar Setup")
    print("=" * 40)
    
    # Get API key
    api_key = input("Enter your SportRadar API key: ").strip()
    
    if not api_key:
        print("❌ No API key provided. Exiting.")
        return
    
    print(f"\n🔑 Testing API key: {api_key[:8]}...")
    
    try:
        # Test the API key
        client = SportRadarNRLClient(api_key)
        
        print("📡 Testing connection to SportRadar...")
        competitions = client.get_competitions()
        
        if competitions:
            print("✅ API key is valid!")
            print(f"📊 Found {len(competitions)} competitions")
            
            # Show available competitions
            print("\n🏆 Available competitions:")
            for i, comp in enumerate(competitions[:10]):  # Show first 10
                print(f"  {i+1}. {comp.get('name', 'Unknown')} - ID: {comp.get('id', 'Unknown')}")
            
            # Ask user to select NRL competition
            print("\n🔍 Looking for NRL competition...")
            nrl_comp = None
            for comp in competitions:
                comp_name = comp.get('name', '').lower()
                if 'nrl' in comp_name or 'national rugby league' in comp_name:
                    nrl_comp = comp
                    break
            
            if nrl_comp:
                print(f"✅ Found NRL competition: {nrl_comp.get('name')} - {nrl_comp.get('id')}")
                
                # Update the client with correct competition ID
                client.nrl_competition_id = nrl_comp.get('id')
                
                # Test getting players
                print("\n👥 Testing player data retrieval...")
                players = client.get_all_players()
                
                if players:
                    print(f"✅ Successfully retrieved {len(players)} players!")
                    
                    # Show sample players
                    print("\n📋 Sample players:")
                    for player in players[:5]:
                        print(f"  • {player.get('name')} - {player.get('team')} ({player.get('position')})")
                    
                    # Save configuration
                    config = {
                        'api_key': api_key,
                        'nrl_competition_id': nrl_comp.get('id'),
                        'setup_date': str(datetime.now()),
                        'test_successful': True
                    }
                    
                    with open('sportradar_config.json', 'w') as f:
                        json.dump(config, f, indent=2)
                    
                    print(f"\n💾 Configuration saved to sportradar_config.json")
                    
                    # Set environment variable
                    print(f"\n🔧 To use SportRadar in your application, set the environment variable:")
                    print(f"   export SPORTRADAR_API_KEY={api_key}")
                    print(f"   # or on Windows:")
                    print(f"   set SPORTRADAR_API_KEY={api_key}")
                    
                    return True
                else:
                    print("❌ Could not retrieve player data")
            else:
                print("⚠️  Could not find NRL competition automatically")
                print("You may need to manually set the competition ID in sportradar_client.py")
        else:
            print("❌ Could not retrieve competitions - check your API key")
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False
    
    return False

def test_existing_setup():
    """
    Test existing SportRadar setup
    """
    api_key = os.getenv('SPORTRADAR_API_KEY')
    
    if not api_key:
        print("❌ SPORTRADAR_API_KEY environment variable not set")
        return False
    
    try:
        client = SportRadarNRLClient(api_key)
        players = client.get_all_players()
        
        if players:
            print(f"✅ SportRadar setup working! Found {len(players)} players")
            return True
        else:
            print("⚠️  SportRadar connected but no players found")
            return False
            
    except Exception as e:
        print(f"❌ SportRadar test failed: {e}")
        return False

def main():
    """
    Main setup function
    """
    print("🏈 FantasyPro SportRadar Integration")
    print("=" * 50)
    
    choice = input("\n1. Setup new SportRadar API key\n2. Test existing setup\n\nChoose option (1 or 2): ").strip()
    
    if choice == "1":
        success = setup_sportradar()
        if success:
            print("\n🎉 SportRadar setup complete!")
            print("You can now restart your FantasyPro API to use SportRadar data.")
        else:
            print("\n❌ Setup failed. Please check your API key and try again.")
    
    elif choice == "2":
        test_existing_setup()
    
    else:
        print("Invalid choice. Please run the script again.")

if __name__ == "__main__":
    from datetime import datetime
    main()
