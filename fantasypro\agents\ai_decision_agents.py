#!/usr/bin/env python3
"""
FantasyPro AI Decision Engine Agents

Advanced AI agents for player recommendations, team optimization, 
trade suggestions, and strategic analysis using machine learning.
"""

import os
import sys
import math
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import json
import logging

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logging_config import setup_logging

logger = setup_logging(__name__)

@dataclass
class PlayerMetrics:
    """Player performance metrics for AI analysis."""
    player_id: int
    name: str
    position: str
    team: str
    price: float
    recent_scores: List[float]
    season_points: int
    games_played: int
    ownership: float
    injury_history: List[Dict]
    fixture_difficulty: List[float]
    weather_impact: float = 0.0
    form_trend: str = "stable"

@dataclass
class TeamComposition:
    """Team composition for optimization."""
    players: List[int]
    total_cost: float
    projected_points: float
    risk_score: float
    balance_score: float
    strategy: str

class PlayerPerformancePredictor:
    """AI agent for predicting player performance using statistical models."""
    
    def __init__(self):
        self.name = "Player Performance Predictor"
        self.model_weights = {
            'recent_form': 0.35,
            'season_average': 0.25,
            'fixture_difficulty': 0.20,
            'injury_risk': 0.10,
            'weather_impact': 0.05,
            'team_form': 0.05
        }
        
    def calculate_form_score(self, recent_scores: List[float]) -> float:
        """Calculate weighted form score with recency bias."""
        if not recent_scores:
            return 0.0

        # Apply exponential decay weights (more recent games weighted higher)
        n = len(recent_scores)
        weights = [math.exp(-1 + i * (1 / (n - 1))) for i in range(n)]
        weight_sum = sum(weights)
        weights = [w / weight_sum for w in weights]

        weighted_average = sum(score * weight for score, weight in zip(recent_scores, weights))

        # Calculate consistency (lower variance = higher consistency)
        mean_score = sum(recent_scores) / len(recent_scores)
        variance = sum((score - mean_score) ** 2 for score in recent_scores) / len(recent_scores)
        consistency = 1 / (1 + variance)

        # Combine average and consistency
        form_score = weighted_average * (0.8 + 0.2 * consistency)
        return min(100, max(0, form_score))
    
    def assess_injury_risk(self, injury_history: List[Dict]) -> float:
        """Assess injury risk based on historical data."""
        if not injury_history:
            return 0.1  # Base risk
        
        recent_injuries = [
            inj for inj in injury_history 
            if datetime.fromisoformat(inj.get('date', '2020-01-01')) > datetime.now() - timedelta(days=365)
        ]
        
        # Risk factors
        injury_count = len(recent_injuries)
        severity_score = sum(inj.get('severity', 1) for inj in recent_injuries) / max(1, injury_count)
        recency_factor = 1.0
        
        if recent_injuries:
            days_since_last = (datetime.now() - datetime.fromisoformat(recent_injuries[-1]['date'])).days
            recency_factor = max(0.5, 1 - days_since_last / 365)
        
        risk_score = min(0.8, (injury_count * 0.1 + severity_score * 0.05) * recency_factor)
        return risk_score
    
    def predict_performance(self, player: PlayerMetrics) -> Dict[str, Any]:
        """Predict player performance for upcoming games."""
        
        # Calculate component scores
        form_score = self.calculate_form_score(player.recent_scores)
        season_avg = player.season_points / max(1, player.games_played)
        
        # Fixture difficulty impact (lower difficulty = higher expected score)
        avg_fixture_difficulty = sum(player.fixture_difficulty) / len(player.fixture_difficulty) if player.fixture_difficulty else 3.0
        fixture_multiplier = 1.2 - (avg_fixture_difficulty - 1) * 0.1
        
        # Injury risk impact
        injury_risk = self.assess_injury_risk(player.injury_history)
        injury_multiplier = 1 - injury_risk
        
        # Weather impact
        weather_multiplier = 1 + player.weather_impact
        
        # Team form (simplified)
        team_multiplier = 1.0  # Would be calculated from team data
        
        # Combine all factors
        base_prediction = (
            form_score * self.model_weights['recent_form'] +
            season_avg * self.model_weights['season_average']
        )
        
        # Apply multipliers
        predicted_score = (
            base_prediction * 
            fixture_multiplier * 
            injury_multiplier * 
            weather_multiplier * 
            team_multiplier
        )
        
        # Calculate confidence based on data quality
        confidence = self._calculate_confidence(player)
        
        return {
            'player_id': player.player_id,
            'predicted_score': round(predicted_score, 1),
            'confidence': round(confidence, 2),
            'form_score': round(form_score, 1),
            'injury_risk': round(injury_risk, 2),
            'fixture_impact': round(fixture_multiplier, 2),
            'components': {
                'base_prediction': round(base_prediction, 1),
                'fixture_multiplier': round(fixture_multiplier, 2),
                'injury_multiplier': round(injury_multiplier, 2),
                'weather_multiplier': round(weather_multiplier, 2)
            }
        }
    
    def _calculate_confidence(self, player: PlayerMetrics) -> float:
        """Calculate prediction confidence based on data quality."""
        confidence = 0.5  # Base confidence
        
        # More games played = higher confidence
        if player.games_played > 15:
            confidence += 0.2
        elif player.games_played > 10:
            confidence += 0.1
        
        # Recent form data availability
        if len(player.recent_scores) >= 5:
            confidence += 0.2
        elif len(player.recent_scores) >= 3:
            confidence += 0.1
        
        # Consistency in recent scores
        if player.recent_scores:
            mean_score = sum(player.recent_scores) / len(player.recent_scores)
            variance = sum((score - mean_score) ** 2 for score in player.recent_scores) / len(player.recent_scores)
            std_dev = math.sqrt(variance)
            cv = std_dev / max(1, mean_score)
            if cv < 0.3:  # Low coefficient of variation = consistent
                confidence += 0.1
        
        return min(0.95, confidence)

class TeamOptimizationAgent:
    """AI agent for optimizing team composition and strategy."""
    
    def __init__(self, predictor: PlayerPerformancePredictor):
        self.name = "Team Optimization Agent"
        self.predictor = predictor
        self.position_requirements = {
            'Fullback': (1, 2),
            'Winger': (2, 4),
            'Centre': (2, 4),
            'Five-eighth': (1, 2),
            'Halfback': (1, 2),
            'Lock': (1, 2),
            'Second-row': (2, 4),
            'Prop': (2, 4),
            'Hooker': (1, 2)
        }
        
    def optimize_team(self, available_players: List[PlayerMetrics], 
                     budget: float = 9500000, strategy: str = "balanced") -> List[TeamComposition]:
        """Optimize team composition using different strategies."""
        
        strategies = {
            "balanced": self._balanced_strategy,
            "high_risk_high_reward": self._aggressive_strategy,
            "conservative": self._conservative_strategy,
            "value_focused": self._value_strategy
        }
        
        if strategy not in strategies:
            strategy = "balanced"
        
        # Get predictions for all players
        player_predictions = {}
        for player in available_players:
            pred = self.predictor.predict_performance(player)
            player_predictions[player.player_id] = pred
        
        # Generate optimized teams
        optimized_teams = []
        
        if strategy == "all":
            for strat_name, strat_func in strategies.items():
                team = strat_func(available_players, player_predictions, budget)
                if team:
                    team.strategy = strat_name
                    optimized_teams.append(team)
        else:
            team = strategies[strategy](available_players, player_predictions, budget)
            if team:
                team.strategy = strategy
                optimized_teams.append(team)
        
        return optimized_teams
    
    def _balanced_strategy(self, players: List[PlayerMetrics], 
                          predictions: Dict[int, Dict], budget: float) -> Optional[TeamComposition]:
        """Balanced strategy focusing on consistent performers."""
        
        # Score players based on predicted points and consistency
        player_scores = []
        for player in players:
            pred = predictions[player.player_id]
            value_score = pred['predicted_score'] / (player.price / 100000)
            consistency_bonus = 1 - (pred['injury_risk'] * 0.5)
            total_score = pred['predicted_score'] * consistency_bonus + value_score * 0.3
            
            player_scores.append({
                'player': player,
                'score': total_score,
                'predicted_points': pred['predicted_score'],
                'value_score': value_score
            })
        
        # Sort by score and select team
        player_scores.sort(key=lambda x: x['score'], reverse=True)
        
        return self._select_team_greedy(player_scores, budget)
    
    def _aggressive_strategy(self, players: List[PlayerMetrics], 
                           predictions: Dict[int, Dict], budget: float) -> Optional[TeamComposition]:
        """High risk/high reward strategy focusing on ceiling potential."""
        
        player_scores = []
        for player in players:
            pred = predictions[player.player_id]
            # Focus on high ceiling players
            ceiling_score = max(player.recent_scores) if player.recent_scores else pred['predicted_score']
            risk_bonus = pred['injury_risk'] * 0.5  # Embrace risk
            total_score = ceiling_score + pred['predicted_score'] + risk_bonus
            
            player_scores.append({
                'player': player,
                'score': total_score,
                'predicted_points': pred['predicted_score'],
                'ceiling': ceiling_score
            })
        
        player_scores.sort(key=lambda x: x['score'], reverse=True)
        return self._select_team_greedy(player_scores, budget)
    
    def _conservative_strategy(self, players: List[PlayerMetrics], 
                             predictions: Dict[int, Dict], budget: float) -> Optional[TeamComposition]:
        """Conservative strategy focusing on safe, consistent picks."""
        
        player_scores = []
        for player in players:
            pred = predictions[player.player_id]
            # Penalize injury risk heavily
            safety_score = pred['predicted_score'] * (1 - pred['injury_risk'] * 2)
            if player.recent_scores:
                mean_score = sum(player.recent_scores) / len(player.recent_scores)
                variance = sum((score - mean_score) ** 2 for score in player.recent_scores) / len(player.recent_scores)
                consistency = 1 / (1 + variance)
            else:
                consistency = 0.5
            total_score = safety_score * (0.7 + 0.3 * consistency)
            
            player_scores.append({
                'player': player,
                'score': total_score,
                'predicted_points': pred['predicted_score'],
                'safety_score': safety_score
            })
        
        player_scores.sort(key=lambda x: x['score'], reverse=True)
        return self._select_team_greedy(player_scores, budget)
    
    def _value_strategy(self, players: List[PlayerMetrics], 
                       predictions: Dict[int, Dict], budget: float) -> Optional[TeamComposition]:
        """Value-focused strategy maximizing points per dollar."""
        
        player_scores = []
        for player in players:
            pred = predictions[player.player_id]
            value_score = pred['predicted_score'] / (player.price / 100000)
            total_score = value_score
            
            player_scores.append({
                'player': player,
                'score': total_score,
                'predicted_points': pred['predicted_score'],
                'value_score': value_score
            })
        
        player_scores.sort(key=lambda x: x['score'], reverse=True)
        return self._select_team_greedy(player_scores, budget)
    
    def _select_team_greedy(self, player_scores: List[Dict], budget: float) -> Optional[TeamComposition]:
        """Select team using greedy algorithm with position constraints."""
        
        selected_players = []
        total_cost = 0
        position_counts = {pos: 0 for pos in self.position_requirements.keys()}
        
        # Try to select 17 players within budget and position constraints
        for player_data in player_scores:
            player = player_data['player']
            
            # Check budget constraint
            if total_cost + player.price > budget:
                continue
            
            # Check position constraint
            pos = player.position
            min_req, max_req = self.position_requirements.get(pos, (0, 17))
            
            if position_counts[pos] >= max_req:
                continue
            
            # Add player to team
            selected_players.append(player.player_id)
            total_cost += player.price
            position_counts[pos] += 1
            
            # Stop when we have 17 players
            if len(selected_players) >= 17:
                break
        
        if len(selected_players) < 13:  # Minimum viable team
            return None
        
        # Calculate team metrics
        total_predicted_points = sum(
            player_data['predicted_points'] 
            for player_data in player_scores 
            if player_data['player'].player_id in selected_players
        )
        
        risk_score = self._calculate_team_risk(
            [p['player'] for p in player_scores if p['player'].player_id in selected_players]
        )
        
        balance_score = self._calculate_team_balance(position_counts)
        
        return TeamComposition(
            players=selected_players,
            total_cost=total_cost,
            projected_points=total_predicted_points,
            risk_score=risk_score,
            balance_score=balance_score,
            strategy=""
        )
    
    def _calculate_team_risk(self, players: List[PlayerMetrics]) -> float:
        """Calculate overall team risk score."""
        if not players:
            return 0.5
        
        injury_risks = [self.predictor.assess_injury_risk(p.injury_history) for p in players]
        avg_injury_risk = sum(injury_risks) / len(injury_risks) if injury_risks else 0.1

        # Price concentration risk
        prices = [p.price for p in players]
        if prices:
            mean_price = sum(prices) / len(prices)
            variance = sum((price - mean_price) ** 2 for price in prices) / len(prices)
            std_dev = math.sqrt(variance)
            price_concentration = std_dev / mean_price
        else:
            price_concentration = 0
        
        # Combine risks
        total_risk = avg_injury_risk * 0.7 + min(0.3, price_concentration * 0.3)
        return round(total_risk, 2)
    
    def _calculate_team_balance(self, position_counts: Dict[str, int]) -> float:
        """Calculate team balance score based on position distribution."""
        balance_score = 1.0
        
        for pos, count in position_counts.items():
            min_req, max_req = self.position_requirements.get(pos, (0, 17))
            
            if count < min_req:
                balance_score -= (min_req - count) * 0.1
            elif count > max_req:
                balance_score -= (count - max_req) * 0.05
        
        return max(0, min(1, balance_score))

class TradeAnalysisAgent:
    """AI agent for analyzing trading opportunities and strategies."""
    
    def __init__(self, predictor: PlayerPerformancePredictor):
        self.name = "Trade Analysis Agent"
        self.predictor = predictor
        
    def analyze_trade_opportunities(self, current_team: List[PlayerMetrics], 
                                  available_players: List[PlayerMetrics],
                                  budget: float = 0) -> List[Dict[str, Any]]:
        """Analyze potential trading opportunities."""
        
        trade_opportunities = []
        
        # Get predictions for all players
        current_predictions = {
            p.player_id: self.predictor.predict_performance(p) 
            for p in current_team
        }
        
        available_predictions = {
            p.player_id: self.predictor.predict_performance(p) 
            for p in available_players
        }
        
        # Analyze each current player for potential trades
        for current_player in current_team:
            current_pred = current_predictions[current_player.player_id]
            
            # Find potential replacements in same position
            same_position_players = [
                p for p in available_players 
                if p.position == current_player.position
            ]
            
            for replacement in same_position_players:
                replacement_pred = available_predictions[replacement.player_id]
                
                # Calculate trade metrics
                price_diff = replacement.price - current_player.price
                points_diff = replacement_pred['predicted_score'] - current_pred['predicted_score']
                
                # Skip if can't afford
                if price_diff > budget:
                    continue
                
                # Calculate trade value
                if price_diff == 0:
                    value_ratio = float('inf') if points_diff > 0 else 0
                else:
                    value_ratio = points_diff / (price_diff / 100000) if price_diff > 0 else points_diff * 100
                
                # Risk assessment
                risk_change = (
                    self.predictor.assess_injury_risk(replacement.injury_history) - 
                    self.predictor.assess_injury_risk(current_player.injury_history)
                )
                
                # Trade recommendation
                recommendation = self._generate_trade_recommendation(
                    points_diff, price_diff, value_ratio, risk_change
                )
                
                if recommendation['action'] != 'avoid':
                    trade_opportunities.append({
                        'player_out': {
                            'id': current_player.player_id,
                            'name': current_player.name,
                            'predicted_score': current_pred['predicted_score']
                        },
                        'player_in': {
                            'id': replacement.player_id,
                            'name': replacement.name,
                            'predicted_score': replacement_pred['predicted_score']
                        },
                        'price_difference': price_diff,
                        'points_difference': round(points_diff, 1),
                        'value_ratio': round(value_ratio, 2),
                        'risk_change': round(risk_change, 2),
                        'recommendation': recommendation,
                        'confidence': min(current_pred['confidence'], replacement_pred['confidence'])
                    })
        
        # Sort by value and return top opportunities
        trade_opportunities.sort(key=lambda x: x['value_ratio'], reverse=True)
        return trade_opportunities[:10]
    
    def _generate_trade_recommendation(self, points_diff: float, price_diff: float, 
                                     value_ratio: float, risk_change: float) -> Dict[str, Any]:
        """Generate trade recommendation based on analysis."""
        
        if points_diff > 5 and value_ratio > 2:
            return {
                'action': 'strong_buy',
                'priority': 'high',
                'reasoning': 'Significant points upgrade with excellent value'
            }
        elif points_diff > 2 and value_ratio > 1:
            return {
                'action': 'buy',
                'priority': 'medium',
                'reasoning': 'Good points upgrade with positive value'
            }
        elif points_diff > 0 and price_diff <= 0:
            return {
                'action': 'buy',
                'priority': 'medium',
                'reasoning': 'Points upgrade for same or lower price'
            }
        elif points_diff < -3:
            return {
                'action': 'avoid',
                'priority': 'low',
                'reasoning': 'Significant points downgrade'
            }
        elif risk_change > 0.2:
            return {
                'action': 'avoid',
                'priority': 'low',
                'reasoning': 'Significant increase in injury risk'
            }
        else:
            return {
                'action': 'hold',
                'priority': 'low',
                'reasoning': 'Marginal benefit, not worth the trade'
            }

def main():
    """Demo function to test the AI decision agents."""
    print("🤖 Testing FantasyPro AI Decision Engine Agents...")
    print("=" * 60)
    
    # Create sample player data
    sample_players = [
        PlayerMetrics(
            player_id=1, name="Nathan Cleary", position="Halfback", team="Penrith Panthers",
            price=750000, recent_scores=[72, 58, 69, 71, 63], season_points=1250, games_played=20,
            ownership=45.2, injury_history=[], fixture_difficulty=[3.5, 2.8, 4.1]
        ),
        PlayerMetrics(
            player_id=2, name="James Tedesco", position="Fullback", team="Sydney Roosters",
            price=820000, recent_scores=[85, 92, 78, 88, 91], season_points=1380, games_played=22,
            ownership=52.8, injury_history=[], fixture_difficulty=[2.1, 3.2, 2.9]
        ),
        PlayerMetrics(
            player_id=3, name="Kalyn Ponga", position="Fullback", team="Newcastle Knights",
            price=780000, recent_scores=[45, 89, 67, 92, 78], season_points=1200, games_played=18,
            ownership=41.2, injury_history=[{'date': '2024-06-01', 'severity': 2}], fixture_difficulty=[4.2, 3.8, 3.5]
        )
    ]
    
    # Test Performance Predictor
    predictor = PlayerPerformancePredictor()
    print("🔮 Performance Predictions:")
    for player in sample_players:
        prediction = predictor.predict_performance(player)
        print(f"   {player.name}: {prediction['predicted_score']} pts (confidence: {prediction['confidence']:.0%})")
    
    print()
    
    # Test Team Optimizer
    optimizer = TeamOptimizationAgent(predictor)
    print("🎯 Team Optimization:")
    optimized_teams = optimizer.optimize_team(sample_players, strategy="balanced")
    for team in optimized_teams:
        print(f"   Strategy: {team.strategy}")
        print(f"   Players: {len(team.players)}")
        print(f"   Projected Points: {team.projected_points:.1f}")
        print(f"   Risk Score: {team.risk_score:.2f}")
    
    print()
    
    # Test Trade Analyzer
    trade_analyzer = TradeAnalysisAgent(predictor)
    print("💱 Trade Analysis:")
    current_team = sample_players[:2]
    available = sample_players[2:]
    trades = trade_analyzer.analyze_trade_opportunities(current_team, available, budget=100000)
    
    for trade in trades[:3]:
        print(f"   {trade['player_out']['name']} → {trade['player_in']['name']}")
        print(f"   Points: {trade['points_difference']:+.1f}, Price: ${trade['price_difference']:+,.0f}")
        print(f"   Action: {trade['recommendation']['action']}")
    
    print("\n✅ AI Decision Engine Agents test completed!")

if __name__ == "__main__":
    main()
