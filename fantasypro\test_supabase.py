#!/usr/bin/env python3
"""
Test Supabase Integration
"""

from supabase_client import FantasyProSupabaseClient
from data_cache import FantasyProDataCache

def main():
    print("🧪 Testing Supabase Integration")
    print("=" * 50)
    
    # Initialize clients
    supabase_client = FantasyProSupabaseClient()
    local_cache = FantasyProDataCache()
    
    # Set Supabase service key
    service_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg"
    supabase_client.set_service_key(service_key)
    print("✅ Supabase client authenticated")
    
    # Get local data
    print("\n📊 Getting local data...")
    local_players = local_cache.get_all_players()
    local_teams = local_cache.get_all_teams()
    print(f"Local players: {len(local_players)}")
    print(f"Local teams: {len(local_teams)}")
    
    # Migrate to Supabase
    print("\n🚀 Migrating to Supabase...")
    
    # Store teams first
    if local_teams:
        print("Storing teams...")
        teams_result = supabase_client.store_teams(local_teams)
        print(f"Teams result: {teams_result}")
    else:
        print("No teams to store")
    
    # Store players
    if local_players:
        print("Storing players...")
        players_result = supabase_client.store_players(local_players)
        print(f"Players result: {players_result}")
    else:
        print("No players to store")
    
    # Test Supabase queries
    print("\n🔍 Testing Supabase queries...")
    
    # Get all players
    supabase_players = supabase_client.get_all_players(10)
    print(f"Supabase players (first 10): {len(supabase_players)}")
    for player in supabase_players[:3]:
        print(f"  - {player['name']} ({player['team']})")
    
    # Test search
    search_results = supabase_client.search_players("Tedesco", 5)
    print(f"\nSearch results for 'Tedesco': {len(search_results)}")
    for player in search_results:
        print(f"  - {player['name']} ({player['team']})")
    
    # Get stats
    stats = supabase_client.get_cache_stats()
    print(f"\nSupabase stats: {stats}")
    
    print("\n🎉 Supabase integration test complete!")

if __name__ == "__main__":
    main()
