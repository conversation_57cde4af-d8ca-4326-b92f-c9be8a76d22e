/**
 * Simple Node.js test for SportRadar API
 * Run with: node test-sportradar.js
 */

const https = require('https');

const API_KEY = 'aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN';
const BASE_URL = 'api.sportradar.com';

// Test endpoints - trying different SportRadar patterns
const endpoints = [
  // Standard patterns
  '/rugby-league/trial/v3/competitions.json',
  '/rugby-league/trial/v3/seasons.json',
  '/rugby-league/trial/v3/teams.json',
  '/rugby-league/trial/v3/players.json',
  '/rugby-league/trial/v3/schedules.json',
  '/rugby-league/trial/v3/fixtures.json',
  '/rugby-league/trial/v3/injuries.json',

  // Alternative patterns (common in SportRadar APIs)
  '/rugby-league/trial/v3/competitions',
  '/rugby-league/trial/v3/seasons',
  '/rugby-league/trial/v3/teams',
  '/rugby-league/trial/v3/players',
  '/rugby-league/trial/v3/schedules',
  '/rugby-league/trial/v3/fixtures',
  '/rugby-league/trial/v3/injuries',

  // Specific competition patterns
  '/rugby-league/trial/v3/competitions/sr:competition:1/info.json',
  '/rugby-league/trial/v3/competitions/sr:competition:1/standings.json',
  '/rugby-league/trial/v3/competitions/sr:competition:1/teams.json',
  '/rugby-league/trial/v3/competitions/sr:competition:1/schedule.json',

  // Season-specific patterns
  '/rugby-league/trial/v3/seasons/sr:season:1/info.json',
  '/rugby-league/trial/v3/seasons/sr:season:1/teams.json',
  '/rugby-league/trial/v3/seasons/sr:season:1/schedule.json',
  '/rugby-league/trial/v3/seasons/sr:season:1/standings.json',

  // Team-specific patterns
  '/rugby-league/trial/v3/teams/sr:team:1/profile.json',
  '/rugby-league/trial/v3/teams/sr:team:1/players.json',
  '/rugby-league/trial/v3/teams/sr:team:1/schedule.json',

  // Match/fixture patterns
  '/rugby-league/trial/v3/matches/sr:match:1/summary.json',
  '/rugby-league/trial/v3/matches/sr:match:1/info.json',

  // Alternative version numbers
  '/rugby-league/trial/v2/competitions.json',
  '/rugby-league/trial/v4/competitions.json',

  // Different sport identifiers
  '/rugby/trial/v3/competitions.json',
  '/nrl/trial/v3/competitions.json',

  // Without trial
  '/rugby-league/v3/competitions.json',

  // Different base structure
  '/v3/rugby-league/competitions.json'
];

function testEndpoint(endpoint, authMethod = 'query') {
  return new Promise((resolve, reject) => {
    let path, headers;

    // Try different authentication methods
    if (authMethod === 'query') {
      path = `${endpoint}?api_key=${API_KEY}`;
      headers = {
        'Accept': 'application/json',
        'User-Agent': 'FantasyPro/1.0'
      };
    } else if (authMethod === 'header') {
      path = endpoint;
      headers = {
        'Accept': 'application/json',
        'User-Agent': 'FantasyPro/1.0',
        'X-API-Key': API_KEY
      };
    } else if (authMethod === 'bearer') {
      path = endpoint;
      headers = {
        'Accept': 'application/json',
        'User-Agent': 'FantasyPro/1.0',
        'Authorization': `Bearer ${API_KEY}`
      };
    }

    console.log(`🧪 Testing: ${endpoint} (auth: ${authMethod})`);
    console.log(`📡 Full URL: https://${BASE_URL}${path}`);

    const options = {
      hostname: BASE_URL,
      path: path,
      method: 'GET',
      headers: headers
    };

    const req = https.request(options, (res) => {
      console.log(`   Status: ${res.statusCode} ${res.statusMessage}`);
      console.log(`   Headers:`, res.headers);
      
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const jsonData = JSON.parse(data);
            console.log(`   ✅ SUCCESS!`);
            console.log(`   📊 Data keys:`, Object.keys(jsonData));
            console.log(`   📄 Sample data:`, JSON.stringify(jsonData, null, 2).substring(0, 300) + '...');
            
            resolve({
              endpoint,
              success: true,
              status: res.statusCode,
              data: jsonData
            });
          } catch (parseError) {
            console.log(`   ❌ JSON Parse Error:`, parseError.message);
            console.log(`   📄 Raw response:`, data.substring(0, 200) + '...');
            
            resolve({
              endpoint,
              success: false,
              status: res.statusCode,
              error: `JSON Parse Error: ${parseError.message}`,
              rawData: data
            });
          }
        } else {
          console.log(`   ❌ FAILED: ${res.statusCode}`);
          console.log(`   📄 Error response:`, data);
          
          resolve({
            endpoint,
            success: false,
            status: res.statusCode,
            error: data
          });
        }
      });
    });

    req.on('error', (error) => {
      console.log(`   💥 Request Error:`, error.message);
      
      resolve({
        endpoint,
        success: false,
        error: error.message
      });
    });

    req.setTimeout(10000, () => {
      console.log(`   ⏰ Request timeout`);
      req.destroy();
      
      resolve({
        endpoint,
        success: false,
        error: 'Request timeout'
      });
    });

    req.end();
  });
}

async function testAllEndpoints() {
  console.log('🚀 TESTING SPORTRADAR NRL API');
  console.log('============================');
  console.log(`🔑 API Key: ${API_KEY}`);
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log('');

  const results = [];

  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.push(result);
    console.log('');
    
    // Rate limiting - wait 1 second between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Summary
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  console.log('📊 SUMMARY');
  console.log('==========');
  console.log(`✅ Successful: ${successful.length}`);
  console.log(`❌ Failed: ${failed.length}`);
  console.log('');

  if (successful.length > 0) {
    console.log('🎉 WORKING ENDPOINTS:');
    successful.forEach(result => {
      console.log(`   ✅ ${result.endpoint}`);
    });
    console.log('');
  }

  if (failed.length > 0) {
    console.log('💥 FAILED ENDPOINTS:');
    failed.forEach(result => {
      console.log(`   ❌ ${result.endpoint} - ${result.error || result.status}`);
    });
    console.log('');
  }

  // Recommendations
  console.log('💡 RECOMMENDATIONS:');
  if (successful.length > 0) {
    console.log('   ✅ SportRadar API is working!');
    console.log('   🔄 Update FantasyPro services to use real data');
    console.log('   📊 Integrate working endpoints into application');
  } else {
    console.log('   ❌ No endpoints working');
    if (failed.some(r => r.error && r.error.includes('401'))) {
      console.log('   🔑 Check API key permissions');
    }
    if (failed.some(r => r.error && r.error.includes('404'))) {
      console.log('   🔍 Check endpoint URLs');
    }
    if (failed.some(r => r.error && r.error.includes('timeout'))) {
      console.log('   ⏰ Check network connectivity');
    }
  }

  return results;
}

// Test different authentication methods
async function testAuthMethods() {
  console.log('🔑 TESTING DIFFERENT AUTHENTICATION METHODS');
  console.log('==========================================');

  const testEndpoint = '/rugby-league/trial/v3/competitions.json';
  const authMethods = ['query', 'header', 'bearer'];

  for (const method of authMethods) {
    console.log(`\n🧪 Testing ${method} authentication...`);
    const result = await testEndpoint(testEndpoint, method);

    if (result.success) {
      console.log(`✅ ${method} authentication WORKS!`);
      return { method, result };
    } else {
      console.log(`❌ ${method} authentication failed: ${result.error || result.status}`);
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  return null;
}

// Quick test for a single endpoint
async function quickTest() {
  console.log('🎯 QUICK TEST - COMPETITIONS ENDPOINT');
  console.log('====================================');

  const result = await testEndpoint('/rugby-league/trial/v3/competitions.json');

  if (result.success) {
    console.log('🎉 SUCCESS! SportRadar API is working!');
    console.log('📊 Ready to integrate real data into FantasyPro');
  } else {
    console.log('❌ FAILED! Need to troubleshoot');
    console.log(`🔍 Error: ${result.error}`);

    // Try different auth methods
    console.log('\n🔄 Trying different authentication methods...');
    const authResult = await testAuthMethods();

    if (authResult) {
      console.log(`🎉 Found working authentication: ${authResult.method}`);
      return authResult.result;
    }
  }

  return result;
}

// Run the test
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes('--quick')) {
    quickTest().catch(console.error);
  } else if (args.includes('--discover')) {
    discoverEndpoints().catch(console.error);
  } else if (args.includes('--auth')) {
    testAuthMethods().catch(console.error);
  } else {
    console.log('🚀 SPORTRADAR API COMPREHENSIVE TEST');
    console.log('===================================');
    console.log('Available options:');
    console.log('  --quick     : Test single endpoint with multiple auth methods');
    console.log('  --discover  : Comprehensive endpoint discovery');
    console.log('  --auth      : Test different authentication methods');
    console.log('  (no args)   : Test all endpoints');
    console.log('');

    testAllEndpoints().catch(console.error);
  }
}

// Comprehensive endpoint discovery
async function discoverEndpoints() {
  console.log('🔍 COMPREHENSIVE ENDPOINT DISCOVERY');
  console.log('===================================');

  const priorityEndpoints = [
    '/rugby-league/trial/v3/competitions.json',
    '/rugby-league/trial/v3/competitions',
    '/rugby-league/trial/v3/seasons.json',
    '/rugby-league/trial/v3/teams.json',
    '/rugby-league/trial/v3/players.json'
  ];

  console.log('🎯 Testing priority endpoints first...\n');

  for (const endpoint of priorityEndpoints) {
    console.log(`📡 Testing: ${endpoint}`);

    // Try all auth methods for each priority endpoint
    for (const authMethod of ['query', 'header', 'bearer']) {
      const result = await testEndpoint(endpoint, authMethod);

      if (result.success) {
        console.log(`🎉 FOUND WORKING ENDPOINT!`);
        console.log(`   Endpoint: ${endpoint}`);
        console.log(`   Auth: ${authMethod}`);
        console.log(`   Data keys: ${Object.keys(result.data).join(', ')}`);
        return { endpoint, authMethod, result };
      } else {
        console.log(`   ❌ ${authMethod}: ${result.status || result.error}`);
      }

      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('');
  }

  console.log('❌ No priority endpoints working. Testing all endpoints...\n');

  // If priority endpoints fail, test all endpoints with query auth only
  const results = [];
  for (const endpoint of endpoints.slice(0, 10)) { // Limit to first 10 to avoid rate limits
    const result = await testEndpoint(endpoint, 'query');
    results.push(result);

    if (result.success) {
      console.log(`🎉 FOUND WORKING ENDPOINT: ${endpoint}`);
      return { endpoint, authMethod: 'query', result };
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  return null;
}

module.exports = { testAllEndpoints, quickTest, testEndpoint, testAuthMethods, discoverEndpoints };
