/**
 * SportRadar API Test Utility
 * Test real API connectivity and response structure
 */

const SPORTRADAR_API_KEY = 'aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN';
const SPORTRADAR_BASE_URL = 'https://api.sportradar.com/rugby-league/trial/v3';

export interface APITestResult {
  success: boolean;
  error?: string;
  data?: any;
  endpoint: string;
  responseTime: number;
}

/**
 * Test SportRadar API endpoints
 */
export class SportRadarAPITester {
  
  /**
   * Test basic API connectivity
   */
  static async testConnectivity(): Promise<APITestResult> {
    const endpoint = `${SPORTRADAR_BASE_URL}/competitions.json?api_key=${SPORTRADAR_API_KEY}`;
    const startTime = Date.now();
    
    try {
      console.log('🧪 Testing SportRadar API connectivity...');
      console.log('📡 Endpoint:', endpoint);
      
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });
      
      const responseTime = Date.now() - startTime;
      
      console.log('📊 Response Status:', response.status);
      console.log('⏱️ Response Time:', responseTime + 'ms');
      console.log('🔗 Response Headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error Response:', errorText);
        
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText} - ${errorText}`,
          endpoint,
          responseTime
        };
      }
      
      const data = await response.json();
      console.log('✅ API Response Data:', data);
      
      return {
        success: true,
        data,
        endpoint,
        responseTime
      };
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error('💥 Network/CORS Error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint,
        responseTime
      };
    }
  }
  
  /**
   * Test multiple endpoints to understand API structure
   */
  static async testMultipleEndpoints(): Promise<APITestResult[]> {
    const endpoints = [
      '/competitions.json',
      '/competitions/sr:competition:1/info.json', // Example competition ID
      '/teams.json',
      '/injuries.json',
      '/schedules.json'
    ];
    
    const results: APITestResult[] = [];
    
    for (const endpoint of endpoints) {
      const fullUrl = `${SPORTRADAR_BASE_URL}${endpoint}?api_key=${SPORTRADAR_API_KEY}`;
      const startTime = Date.now();
      
      try {
        console.log(`🧪 Testing endpoint: ${endpoint}`);
        
        const response = await fetch(fullUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        });
        
        const responseTime = Date.now() - startTime;
        
        if (response.ok) {
          const data = await response.json();
          results.push({
            success: true,
            data,
            endpoint: fullUrl,
            responseTime
          });
          console.log(`✅ ${endpoint} - Success`);
        } else {
          const errorText = await response.text();
          results.push({
            success: false,
            error: `HTTP ${response.status}: ${errorText}`,
            endpoint: fullUrl,
            responseTime
          });
          console.log(`❌ ${endpoint} - Failed: ${response.status}`);
        }
        
        // Rate limiting delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        const responseTime = Date.now() - startTime;
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          endpoint: fullUrl,
          responseTime
        });
        console.log(`💥 ${endpoint} - Error:`, error);
      }
    }
    
    return results;
  }
  
  /**
   * Test with different HTTP methods and headers
   */
  static async testWithDifferentConfigs(): Promise<APITestResult[]> {
    const endpoint = `${SPORTRADAR_BASE_URL}/competitions.json?api_key=${SPORTRADAR_API_KEY}`;
    const configs = [
      {
        name: 'Basic GET',
        options: {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          }
        }
      },
      {
        name: 'GET with User-Agent',
        options: {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'FantasyPro/1.0'
          }
        }
      },
      {
        name: 'GET with CORS headers',
        options: {
          method: 'GET',
          mode: 'cors' as RequestMode,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        }
      }
    ];
    
    const results: APITestResult[] = [];
    
    for (const config of configs) {
      const startTime = Date.now();
      
      try {
        console.log(`🧪 Testing: ${config.name}`);
        
        const response = await fetch(endpoint, config.options);
        const responseTime = Date.now() - startTime;
        
        if (response.ok) {
          const data = await response.json();
          results.push({
            success: true,
            data,
            endpoint: `${config.name}: ${endpoint}`,
            responseTime
          });
          console.log(`✅ ${config.name} - Success`);
        } else {
          const errorText = await response.text();
          results.push({
            success: false,
            error: `HTTP ${response.status}: ${errorText}`,
            endpoint: `${config.name}: ${endpoint}`,
            responseTime
          });
          console.log(`❌ ${config.name} - Failed: ${response.status}`);
        }
        
      } catch (error) {
        const responseTime = Date.now() - startTime;
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          endpoint: `${config.name}: ${endpoint}`,
          responseTime
        });
        console.log(`💥 ${config.name} - Error:`, error);
      }
      
      // Rate limiting delay
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return results;
  }
  
  /**
   * Run comprehensive API tests
   */
  static async runFullTest(): Promise<{
    connectivity: APITestResult;
    endpoints: APITestResult[];
    configurations: APITestResult[];
    summary: {
      totalTests: number;
      successful: number;
      failed: number;
      avgResponseTime: number;
    };
  }> {
    console.log('🚀 Starting comprehensive SportRadar API test...');
    
    const connectivity = await this.testConnectivity();
    const endpoints = await this.testMultipleEndpoints();
    const configurations = await this.testWithDifferentConfigs();
    
    const allResults = [connectivity, ...endpoints, ...configurations];
    const successful = allResults.filter(r => r.success).length;
    const failed = allResults.filter(r => !r.success).length;
    const avgResponseTime = allResults.reduce((sum, r) => sum + r.responseTime, 0) / allResults.length;
    
    const summary = {
      totalTests: allResults.length,
      successful,
      failed,
      avgResponseTime: Math.round(avgResponseTime)
    };
    
    console.log('📊 Test Summary:', summary);
    
    return {
      connectivity,
      endpoints,
      configurations,
      summary
    };
  }
}

/**
 * Quick test function for immediate use
 */
export async function quickAPITest(): Promise<void> {
  console.log('🧪 Quick SportRadar API Test');
  console.log('============================');
  
  const result = await SportRadarAPITester.testConnectivity();
  
  if (result.success) {
    console.log('🎉 SUCCESS! SportRadar API is working!');
    console.log('📊 Response data structure:', Object.keys(result.data || {}));
  } else {
    console.log('❌ FAILED! SportRadar API issue:');
    console.log('🔍 Error:', result.error);
    
    if (result.error?.includes('CORS')) {
      console.log('💡 SOLUTION: Need to use server-side proxy or backend API');
    } else if (result.error?.includes('401') || result.error?.includes('403')) {
      console.log('💡 SOLUTION: Check API key or endpoint permissions');
    } else if (result.error?.includes('404')) {
      console.log('💡 SOLUTION: Check endpoint URL structure');
    }
  }
}

export default SportRadarAPITester;
