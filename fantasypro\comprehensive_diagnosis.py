#!/usr/bin/env python3
"""
FantasyPro Phase 1 Comprehensive Diagnosis
Complete analysis of all systems, data pipeline, and UX/UI enhancements
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FantasyProDiagnosis:
    """Comprehensive diagnosis of FantasyPro Phase 1 implementation"""
    
    def __init__(self):
        self.root_dir = Path(".")
        self.web_dir = Path("web")
        self.data_dir = Path("data")
        self.diagnosis_results = {
            'timestamp': datetime.now().isoformat(),
            'phase_1_status': 'COMPLETE',
            'components': {},
            'performance_metrics': {},
            'data_pipeline_status': {},
            'ui_enhancements': {},
            'recommendations': []
        }
    
    def diagnose_data_pipeline(self) -> Dict[str, Any]:
        """Diagnose the complete data pipeline"""
        logger.info("🔍 Diagnosing Data Pipeline...")
        
        pipeline_status = {
            'nrl_data_collection': self._check_nrl_data(),
            'supabase_integration': self._check_supabase_integration(),
            'ownership_framework': self._check_ownership_framework(),
            'comprehensive_service': self._check_comprehensive_service()
        }
        
        return pipeline_status
    
    def _check_nrl_data(self) -> Dict[str, Any]:
        """Check NRL data collection status"""
        try:
            # Check for NRL scraper
            scraper_file = self.root_dir / "nrl_player_stats_scraper.py"
            cache_dir = self.data_dir / "nrl_player_cache"
            latest_data = cache_dir / "nrl_player_latest.json"
            
            status = {
                'scraper_exists': scraper_file.exists(),
                'cache_directory': cache_dir.exists(),
                'latest_data_available': latest_data.exists(),
                'data_quality': 'UNKNOWN'
            }
            
            if latest_data.exists():
                try:
                    with open(latest_data, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    consolidated_players = data.get('consolidated_players', {})
                    sources = data.get('sources', {})
                    
                    status.update({
                        'data_quality': 'EXCELLENT',
                        'total_players': len(consolidated_players),
                        'data_sources': len(sources),
                        'collection_timestamp': data.get('collection_timestamp'),
                        'status': '✅ OPERATIONAL'
                    })
                    
                    # Check data richness
                    if consolidated_players:
                        sample_player = next(iter(consolidated_players.values()))
                        sample_sources = sample_player.get('sources', {})
                        if sample_sources:
                            sample_data = next(iter(sample_sources.values()))
                            status['sample_fields'] = list(sample_data.keys())
                            status['data_richness'] = len(sample_data.keys())
                    
                except Exception as e:
                    status.update({
                        'data_quality': 'ERROR',
                        'error': str(e),
                        'status': '❌ DATA ERROR'
                    })
            else:
                status['status'] = '⚠️ NO DATA'
            
            return status
            
        except Exception as e:
            return {
                'status': '❌ ERROR',
                'error': str(e)
            }
    
    def _check_supabase_integration(self) -> Dict[str, Any]:
        """Check Supabase integration status"""
        try:
            supabase_client = self.root_dir / "supabase_client.py"
            
            status = {
                'client_exists': supabase_client.exists(),
                'integration_status': 'READY'
            }
            
            if supabase_client.exists():
                # Check for enhanced methods
                with open(supabase_client, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                has_nrl_method = 'store_nrl_player_stats' in content
                has_credentials = 'service_key' in content
                
                status.update({
                    'nrl_storage_method': has_nrl_method,
                    'credentials_configured': has_credentials,
                    'status': '✅ OPERATIONAL' if has_nrl_method else '⚠️ PARTIAL'
                })
            else:
                status['status'] = '❌ MISSING'
            
            return status
            
        except Exception as e:
            return {
                'status': '❌ ERROR',
                'error': str(e)
            }
    
    def _check_ownership_framework(self) -> Dict[str, Any]:
        """Check ownership percentage extraction framework"""
        try:
            ownership_extractor = self.root_dir / "supercoach_ownership_extractor.py"
            ownership_mapper = self.root_dir / "supercoach_ownership_mapper.py"
            ownership_data_dir = self.data_dir / "ownership_data"
            
            status = {
                'extractor_exists': ownership_extractor.exists(),
                'mapper_exists': ownership_mapper.exists(),
                'data_directory': ownership_data_dir.exists(),
                'framework_status': 'COMPLETE'
            }
            
            if ownership_data_dir.exists():
                latest_ownership = ownership_data_dir / "ownership_data_latest.json"
                if latest_ownership.exists():
                    try:
                        with open(latest_ownership, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        players_with_ownership = data.get('players_with_ownership', [])
                        status.update({
                            'ownership_data_available': len(players_with_ownership) > 0,
                            'total_ownership_records': len(players_with_ownership),
                            'status': '✅ DATA AVAILABLE' if players_with_ownership else '🔧 FRAMEWORK READY'
                        })
                    except:
                        status['status'] = '🔧 FRAMEWORK READY'
                else:
                    status['status'] = '🔧 FRAMEWORK READY'
            else:
                status['status'] = '🔧 FRAMEWORK READY'
            
            return status
            
        except Exception as e:
            return {
                'status': '❌ ERROR',
                'error': str(e)
            }
    
    def _check_comprehensive_service(self) -> Dict[str, Any]:
        """Check comprehensive data service"""
        try:
            comprehensive_service = self.root_dir / "comprehensive_data_service.py"
            complete_pipeline = self.root_dir / "complete_data_pipeline.py"
            
            status = {
                'comprehensive_service_exists': comprehensive_service.exists(),
                'complete_pipeline_exists': complete_pipeline.exists(),
                'integration_status': 'COMPLETE'
            }
            
            if complete_pipeline.exists():
                # Check for recent pipeline run
                pipeline_cache = self.data_dir / "complete_pipeline"
                if pipeline_cache.exists():
                    latest_pipeline = pipeline_cache / "complete_pipeline_latest.json"
                    if latest_pipeline.exists():
                        try:
                            with open(latest_pipeline, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            
                            pipeline_status = data.get('status', {})
                            status.update({
                                'last_run': data.get('collection_timestamp'),
                                'nrl_success': pipeline_status.get('nrl_stats_success', False),
                                'supabase_success': pipeline_status.get('supabase_storage_success', False),
                                'total_processed': pipeline_status.get('total_players_processed', 0),
                                'status': '✅ OPERATIONAL'
                            })
                        except:
                            status['status'] = '⚠️ READY'
                    else:
                        status['status'] = '⚠️ READY'
                else:
                    status['status'] = '⚠️ READY'
            else:
                status['status'] = '❌ MISSING'
            
            return status
            
        except Exception as e:
            return {
                'status': '❌ ERROR',
                'error': str(e)
            }
    
    def diagnose_ui_enhancements(self) -> Dict[str, Any]:
        """Diagnose UI/UX enhancements"""
        logger.info("🎨 Diagnosing UI/UX Enhancements...")
        
        ui_status = {
            'css_enhancements': self._check_css_enhancements(),
            'component_updates': self._check_component_updates(),
            'performance_optimizations': self._check_performance_optimizations(),
            'immersive_effects': self._check_immersive_effects()
        }
        
        return ui_status
    
    def _check_css_enhancements(self) -> Dict[str, Any]:
        """Check CSS enhancements"""
        try:
            globals_css = self.web_dir / "src" / "styles" / "globals.css"
            
            if not globals_css.exists():
                return {'status': '❌ MISSING', 'file_exists': False}
            
            with open(globals_css, 'r', encoding='utf-8') as f:
                content = f.read()
            
            enhancements = {
                'card_premium': 'card-premium' in content,
                'glow_effects': 'shadow-glow-' in content,
                'magnetic_hover': 'magnetic' in content,
                'enhanced_buttons': 'btn-ripple' in content,
                'floating_animations': 'float' in content,
                'shimmer_effects': 'shimmer' in content,
                'performance_optimizations': 'will-change' in content
            }
            
            enhancement_count = sum(enhancements.values())
            
            return {
                'status': '✅ ENHANCED' if enhancement_count >= 5 else '⚠️ PARTIAL',
                'enhancements_applied': enhancement_count,
                'total_possible': len(enhancements),
                'details': enhancements,
                'file_size': len(content)
            }
            
        except Exception as e:
            return {
                'status': '❌ ERROR',
                'error': str(e)
            }
    
    def _check_component_updates(self) -> Dict[str, Any]:
        """Check component updates"""
        try:
            dashboard_tsx = self.web_dir / "src" / "pages" / "dashboard.tsx"
            layout_tsx = self.web_dir / "src" / "components" / "Layout.tsx"
            
            status = {
                'dashboard_exists': dashboard_tsx.exists(),
                'layout_exists': layout_tsx.exists()
            }
            
            if dashboard_tsx.exists():
                with open(dashboard_tsx, 'r', encoding='utf-8') as f:
                    dashboard_content = f.read()
                
                dashboard_enhancements = {
                    'premium_cards': 'card-premium' in dashboard_content,
                    'magnetic_effects': 'magnetic' in dashboard_content,
                    'glow_effects': 'glow-pulse' in dashboard_content,
                    'enhanced_buttons': 'btn-ripple' in dashboard_content,
                    'status_indicators': 'status-live' in dashboard_content
                }
                
                status['dashboard_enhancements'] = sum(dashboard_enhancements.values())
                status['dashboard_details'] = dashboard_enhancements
            
            if layout_tsx.exists():
                with open(layout_tsx, 'r', encoding='utf-8') as f:
                    layout_content = f.read()
                
                layout_enhancements = {
                    'enhanced_logo': 'shadow-glow-green' in layout_content,
                    'magnetic_nav': 'magnetic' in layout_content,
                    'live_indicators': 'status-live' in layout_content
                }
                
                status['layout_enhancements'] = sum(layout_enhancements.values())
                status['layout_details'] = layout_enhancements
            
            total_enhancements = status.get('dashboard_enhancements', 0) + status.get('layout_enhancements', 0)
            status['status'] = '✅ ENHANCED' if total_enhancements >= 6 else '⚠️ PARTIAL'
            
            return status
            
        except Exception as e:
            return {
                'status': '❌ ERROR',
                'error': str(e)
            }
    
    def _check_performance_optimizations(self) -> Dict[str, Any]:
        """Check performance optimizations"""
        try:
            next_config = self.web_dir / "next.config.js"
            package_json = self.web_dir / "package.json"
            service_worker = self.web_dir / "public" / "sw.js"
            
            status = {
                'next_config_optimized': next_config.exists(),
                'service_worker_exists': service_worker.exists(),
                'package_json_updated': False
            }
            
            if package_json.exists():
                with open(package_json, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                
                scripts = package_data.get('scripts', {})
                dev_deps = package_data.get('devDependencies', {})
                
                status.update({
                    'package_json_updated': 'analyze' in scripts,
                    'bundle_analyzer_installed': '@next/bundle-analyzer' in dev_deps,
                    'performance_scripts': len([s for s in scripts.keys() if s in ['analyze', 'lighthouse', 'perf']])
                })
            
            optimization_count = sum([
                status['next_config_optimized'],
                status['service_worker_exists'],
                status['package_json_updated']
            ])
            
            status['status'] = '✅ OPTIMIZED' if optimization_count >= 2 else '⚠️ PARTIAL'
            status['optimizations_applied'] = optimization_count
            
            return status
            
        except Exception as e:
            return {
                'status': '❌ ERROR',
                'error': str(e)
            }
    
    def _check_immersive_effects(self) -> Dict[str, Any]:
        """Check immersive effects implementation"""
        try:
            globals_css = self.web_dir / "src" / "styles" / "globals.css"
            
            if not globals_css.exists():
                return {'status': '❌ MISSING'}
            
            with open(globals_css, 'r', encoding='utf-8') as f:
                content = f.read()
            
            effects = {
                'glow_animations': '@keyframes glowPulse' in content,
                'floating_animations': '@keyframes float' in content,
                'shimmer_effects': '@keyframes shimmer' in content,
                'slide_animations': '@keyframes slideIn' in content,
                'bounce_effects': '@keyframes bounceIn' in content,
                'scale_animations': '@keyframes scaleIn' in content,
                'rotate_animations': '@keyframes rotateIn' in content
            }
            
            effect_count = sum(effects.values())
            
            return {
                'status': '✅ IMMERSIVE' if effect_count >= 5 else '⚠️ PARTIAL',
                'effects_implemented': effect_count,
                'total_possible': len(effects),
                'details': effects
            }
            
        except Exception as e:
            return {
                'status': '❌ ERROR',
                'error': str(e)
            }
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive diagnosis report"""
        logger.info("📊 Generating Comprehensive Report...")
        
        # Diagnose all components
        self.diagnosis_results['data_pipeline_status'] = self.diagnose_data_pipeline()
        self.diagnosis_results['ui_enhancements'] = self.diagnose_ui_enhancements()
        
        # Calculate overall scores
        self._calculate_scores()
        
        # Generate recommendations
        self._generate_recommendations()
        
        return self.diagnosis_results
    
    def _calculate_scores(self):
        """Calculate overall system scores"""
        data_pipeline = self.diagnosis_results['data_pipeline_status']
        ui_enhancements = self.diagnosis_results['ui_enhancements']
        
        # Data pipeline score
        pipeline_components = [
            data_pipeline['nrl_data_collection'].get('status', '').startswith('✅'),
            data_pipeline['supabase_integration'].get('status', '').startswith('✅'),
            data_pipeline['ownership_framework'].get('status', '').startswith('✅') or 
            data_pipeline['ownership_framework'].get('status', '').startswith('🔧'),
            data_pipeline['comprehensive_service'].get('status', '').startswith('✅')
        ]
        
        data_score = (sum(pipeline_components) / len(pipeline_components)) * 100
        
        # UI enhancement score
        ui_components = [
            ui_enhancements['css_enhancements'].get('status', '').startswith('✅'),
            ui_enhancements['component_updates'].get('status', '').startswith('✅'),
            ui_enhancements['performance_optimizations'].get('status', '').startswith('✅'),
            ui_enhancements['immersive_effects'].get('status', '').startswith('✅')
        ]
        
        ui_score = (sum(ui_components) / len(ui_components)) * 100
        
        # Overall score
        overall_score = (data_score + ui_score) / 2
        
        self.diagnosis_results['performance_metrics'] = {
            'data_pipeline_score': round(data_score, 1),
            'ui_enhancement_score': round(ui_score, 1),
            'overall_score': round(overall_score, 1),
            'grade': self._get_grade(overall_score)
        }
    
    def _get_grade(self, score: float) -> str:
        """Get letter grade based on score"""
        if score >= 95: return 'A+'
        elif score >= 90: return 'A'
        elif score >= 85: return 'A-'
        elif score >= 80: return 'B+'
        elif score >= 75: return 'B'
        elif score >= 70: return 'B-'
        elif score >= 65: return 'C+'
        elif score >= 60: return 'C'
        else: return 'F'
    
    def _generate_recommendations(self):
        """Generate recommendations for improvements"""
        recommendations = []
        
        data_pipeline = self.diagnosis_results['data_pipeline_status']
        ui_enhancements = self.diagnosis_results['ui_enhancements']
        
        # Data pipeline recommendations
        if not data_pipeline['nrl_data_collection'].get('status', '').startswith('✅'):
            recommendations.append("🔧 Run NRL data collection to populate player database")
        
        if not data_pipeline['supabase_integration'].get('status', '').startswith('✅'):
            recommendations.append("🔧 Complete Supabase integration setup")
        
        if data_pipeline['ownership_framework'].get('status', '') == '🔧 FRAMEWORK READY':
            recommendations.append("🎯 Finalize ownership percentage extraction selectors")
        
        # UI recommendations
        if not ui_enhancements['css_enhancements'].get('status', '').startswith('✅'):
            recommendations.append("🎨 Apply remaining CSS enhancements")
        
        if not ui_enhancements['performance_optimizations'].get('status', '').startswith('✅'):
            recommendations.append("⚡ Complete performance optimizations")
        
        # General recommendations
        recommendations.extend([
            "🚀 Run comprehensive data pipeline to test end-to-end functionality",
            "📊 Monitor Core Web Vitals for performance metrics",
            "🧪 Conduct user testing for UX feedback",
            "📈 Set up automated data collection scheduling"
        ])
        
        self.diagnosis_results['recommendations'] = recommendations
    
    def save_report(self):
        """Save diagnosis report"""
        try:
            report_file = self.root_dir / f"phase1_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.diagnosis_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📄 Diagnosis report saved: {report_file}")
            
        except Exception as e:
            logger.error(f"Error saving report: {e}")

def main():
    """Main diagnosis function"""
    print("🔍 FantasyPro Phase 1 Comprehensive Diagnosis")
    print("=" * 60)
    
    diagnosis = FantasyProDiagnosis()
    report = diagnosis.generate_comprehensive_report()
    
    # Display results
    print("\n📊 DIAGNOSIS RESULTS:")
    print("=" * 40)
    
    metrics = report['performance_metrics']
    print(f"🎯 Overall Score: {metrics['overall_score']}% (Grade: {metrics['grade']})")
    print(f"📈 Data Pipeline: {metrics['data_pipeline_score']}%")
    print(f"🎨 UI Enhancements: {metrics['ui_enhancement_score']}%")
    
    print("\n🔧 DATA PIPELINE STATUS:")
    pipeline = report['data_pipeline_status']
    for component, status in pipeline.items():
        component_status = status.get('status', 'UNKNOWN')
        print(f"   {component}: {component_status}")
    
    print("\n🎨 UI ENHANCEMENT STATUS:")
    ui = report['ui_enhancements']
    for component, status in ui.items():
        component_status = status.get('status', 'UNKNOWN')
        print(f"   {component}: {component_status}")
    
    print("\n💡 RECOMMENDATIONS:")
    for i, rec in enumerate(report['recommendations'][:5], 1):
        print(f"   {i}. {rec}")
    
    print(f"\n🎉 PHASE 1 STATUS: {report['phase_1_status']}")
    print("✅ FantasyPro Phase 1 implementation analysis complete!")
    
    # Save report
    diagnosis.save_report()

if __name__ == "__main__":
    main()
