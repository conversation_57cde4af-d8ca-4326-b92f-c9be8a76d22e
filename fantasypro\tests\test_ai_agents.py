#!/usr/bin/env python3
"""
FantasyPro AI Agents Test Suite

Comprehensive tests for AI decision engines, player analysis,
and recommendation systems.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.ai_decision_agents import (
    PlayerPerformancePredictor, TeamOptimizationAgent, TradeAnalysisAgent,
    PlayerMetrics, TeamComposition
)

class TestPlayerPerformancePredictor(unittest.TestCase):
    """Test cases for PlayerPerformancePredictor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.predictor = PlayerPerformancePredictor()
        self.sample_player = PlayerMetrics(
            player_id=1,
            name="Test Player",
            position="Halfback",
            team="Test Team",
            price=750000,
            recent_scores=[70, 65, 80, 75, 72],
            season_points=1200,
            games_played=20,
            ownership=45.0,
            injury_history=[],
            fixture_difficulty=[3.0, 2.5, 4.0]
        )
    
    def test_calculate_form_score(self):
        """Test form score calculation."""
        scores = [70.0, 65.0, 80.0, 75.0, 72.0]
        form_score = self.predictor.calculate_form_score(scores)
        
        self.assertIsInstance(form_score, float)
        self.assertGreaterEqual(form_score, 0)
        self.assertLessEqual(form_score, 100)
    
    def test_calculate_form_score_empty(self):
        """Test form score with empty scores."""
        form_score = self.predictor.calculate_form_score([])
        self.assertEqual(form_score, 0.0)
    
    def test_assess_injury_risk_no_history(self):
        """Test injury risk assessment with no history."""
        risk = self.predictor.assess_injury_risk([])
        self.assertEqual(risk, 0.1)  # Base risk
    
    def test_assess_injury_risk_with_history(self):
        """Test injury risk assessment with injury history."""
        injury_history = [
            {'date': (datetime.now() - timedelta(days=30)).isoformat(), 'severity': 2}
        ]
        risk = self.predictor.assess_injury_risk(injury_history)
        
        self.assertIsInstance(risk, float)
        self.assertGreaterEqual(risk, 0)
        self.assertLessEqual(risk, 1)
    
    def test_predict_performance(self):
        """Test performance prediction."""
        prediction = self.predictor.predict_performance(self.sample_player)
        
        # Check required fields
        self.assertIn('player_id', prediction)
        self.assertIn('predicted_score', prediction)
        self.assertIn('confidence', prediction)
        self.assertIn('form_score', prediction)
        self.assertIn('injury_risk', prediction)
        
        # Check value ranges
        self.assertGreaterEqual(prediction['confidence'], 0)
        self.assertLessEqual(prediction['confidence'], 1)
        self.assertGreaterEqual(prediction['predicted_score'], 0)
    
    def test_calculate_confidence(self):
        """Test confidence calculation."""
        # Test with good data
        player_good_data = PlayerMetrics(
            player_id=1, name="Test", position="Halfback", team="Test",
            price=750000, recent_scores=[70, 65, 80, 75, 72],
            season_points=1200, games_played=20, ownership=45.0,
            injury_history=[], fixture_difficulty=[3.0]
        )
        
        confidence = self.predictor._calculate_confidence(player_good_data)
        self.assertGreaterEqual(confidence, 0.5)
        
        # Test with poor data
        player_poor_data = PlayerMetrics(
            player_id=2, name="Test2", position="Halfback", team="Test",
            price=750000, recent_scores=[], season_points=100,
            games_played=2, ownership=5.0, injury_history=[], fixture_difficulty=[]
        )
        
        confidence_poor = self.predictor._calculate_confidence(player_poor_data)
        self.assertLess(confidence_poor, confidence)

class TestTeamOptimizationAgent(unittest.TestCase):
    """Test cases for TeamOptimizationAgent."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.predictor = PlayerPerformancePredictor()
        self.optimizer = TeamOptimizationAgent(self.predictor)
        
        # Create sample players
        self.sample_players = [
            PlayerMetrics(
                player_id=i,
                name=f"Player {i}",
                position=["Halfback", "Fullback", "Prop", "Hooker"][i % 4],
                team=f"Team {i % 4}",
                price=500000 + i * 50000,
                recent_scores=[60 + i, 65 + i, 70 + i],
                season_points=1000 + i * 100,
                games_played=20,
                ownership=30.0 + i,
                injury_history=[],
                fixture_difficulty=[3.0]
            )
            for i in range(10)
        ]
    
    def test_optimize_team_balanced(self):
        """Test balanced team optimization."""
        teams = self.optimizer.optimize_team(
            self.sample_players, 
            budget=5000000, 
            strategy="balanced"
        )
        
        self.assertIsInstance(teams, list)
        if teams:
            team = teams[0]
            self.assertIsInstance(team, TeamComposition)
            self.assertEqual(team.strategy, "balanced")
            self.assertLessEqual(team.total_cost, 5000000)
    
    def test_optimize_team_conservative(self):
        """Test conservative team optimization."""
        teams = self.optimizer.optimize_team(
            self.sample_players,
            budget=5000000,
            strategy="conservative"
        )
        
        if teams:
            team = teams[0]
            self.assertEqual(team.strategy, "conservative")
    
    def test_calculate_team_risk(self):
        """Test team risk calculation."""
        risk = self.optimizer._calculate_team_risk(self.sample_players[:5])
        
        self.assertIsInstance(risk, float)
        self.assertGreaterEqual(risk, 0)
        self.assertLessEqual(risk, 1)
    
    def test_calculate_team_balance(self):
        """Test team balance calculation."""
        position_counts = {
            'Halfback': 2,
            'Fullback': 2,
            'Prop': 4,
            'Hooker': 1,
            'Winger': 0,
            'Centre': 0,
            'Five-eighth': 0,
            'Lock': 0,
            'Second-row': 0
        }
        
        balance = self.optimizer._calculate_team_balance(position_counts)
        
        self.assertIsInstance(balance, float)
        self.assertGreaterEqual(balance, 0)
        self.assertLessEqual(balance, 1)

class TestTradeAnalysisAgent(unittest.TestCase):
    """Test cases for TradeAnalysisAgent."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.predictor = PlayerPerformancePredictor()
        self.trade_analyzer = TradeAnalysisAgent(self.predictor)
        
        self.current_team = [
            PlayerMetrics(
                player_id=1, name="Current Player 1", position="Halfback",
                team="Team A", price=750000, recent_scores=[70, 65, 80],
                season_points=1200, games_played=20, ownership=45.0,
                injury_history=[], fixture_difficulty=[3.0]
            )
        ]
        
        self.available_players = [
            PlayerMetrics(
                player_id=2, name="Available Player 1", position="Halfback",
                team="Team B", price=800000, recent_scores=[75, 70, 85],
                season_points=1300, games_played=20, ownership=40.0,
                injury_history=[], fixture_difficulty=[2.5]
            )
        ]
    
    def test_analyze_trade_opportunities(self):
        """Test trade opportunity analysis."""
        trades = self.trade_analyzer.analyze_trade_opportunities(
            self.current_team,
            self.available_players,
            budget=100000
        )
        
        self.assertIsInstance(trades, list)
        
        if trades:
            trade = trades[0]
            self.assertIn('player_out', trade)
            self.assertIn('player_in', trade)
            self.assertIn('price_difference', trade)
            self.assertIn('points_difference', trade)
            self.assertIn('recommendation', trade)
    
    def test_generate_trade_recommendation(self):
        """Test trade recommendation generation."""
        # Test strong buy scenario
        recommendation = self.trade_analyzer._generate_trade_recommendation(
            points_diff=10, price_diff=50000, value_ratio=5.0, risk_change=0.0
        )
        
        self.assertEqual(recommendation['action'], 'strong_buy')
        self.assertEqual(recommendation['priority'], 'high')
        
        # Test avoid scenario
        recommendation = self.trade_analyzer._generate_trade_recommendation(
            points_diff=-5, price_diff=100000, value_ratio=-1.0, risk_change=0.3
        )
        
        self.assertEqual(recommendation['action'], 'avoid')

class TestIntegration(unittest.TestCase):
    """Integration tests for AI agents working together."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        self.predictor = PlayerPerformancePredictor()
        self.optimizer = TeamOptimizationAgent(self.predictor)
        self.trade_analyzer = TradeAnalysisAgent(self.predictor)
        
        # Create a realistic player set
        self.players = [
            PlayerMetrics(
                player_id=i,
                name=f"Player {i}",
                position=["Halfback", "Fullback", "Prop", "Hooker", "Winger"][i % 5],
                team=f"Team {i % 8}",
                price=400000 + i * 100000,
                recent_scores=[50 + i*2, 55 + i*2, 60 + i*2],
                season_points=800 + i * 50,
                games_played=15 + i,
                ownership=20.0 + i * 2,
                injury_history=[],
                fixture_difficulty=[2.0 + i * 0.2]
            )
            for i in range(20)
        ]
    
    def test_full_analysis_pipeline(self):
        """Test complete analysis pipeline."""
        # 1. Predict performance for all players
        predictions = {}
        for player in self.players:
            pred = self.predictor.predict_performance(player)
            predictions[player.player_id] = pred
            
            # Verify prediction structure
            self.assertIn('predicted_score', pred)
            self.assertIn('confidence', pred)
        
        # 2. Optimize team
        optimized_teams = self.optimizer.optimize_team(
            self.players[:15],  # Use subset for faster testing
            budget=9500000,
            strategy="balanced"
        )
        
        self.assertGreater(len(optimized_teams), 0)
        
        # 3. Analyze trades
        if optimized_teams:
            team = optimized_teams[0]
            current_team_players = [
                p for p in self.players if p.player_id in team.players[:5]
            ]
            available_players = [
                p for p in self.players if p.player_id not in team.players
            ]
            
            trades = self.trade_analyzer.analyze_trade_opportunities(
                current_team_players,
                available_players[:5],  # Limit for performance
                budget=500000
            )
            
            # Should return some trade opportunities
            self.assertIsInstance(trades, list)
    
    def test_performance_consistency(self):
        """Test that predictions are consistent across multiple runs."""
        player = self.players[0]
        
        # Run prediction multiple times
        predictions = []
        for _ in range(5):
            pred = self.predictor.predict_performance(player)
            predictions.append(pred['predicted_score'])
        
        # All predictions should be identical (deterministic)
        self.assertTrue(all(p == predictions[0] for p in predictions))

def run_tests():
    """Run all tests and return results."""
    print("🧪 Running FantasyPro AI Agents Test Suite...")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestPlayerPerformancePredictor,
        TestTeamOptimizationAgent,
        TestTradeAnalysisAgent,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            error_msg = traceback.split('AssertionError: ')[-1].split('\n')[0]
            print(f"  - {test}: {error_msg}")

    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            error_msg = traceback.split('\n')[-2]
            print(f"  - {test}: {error_msg}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
