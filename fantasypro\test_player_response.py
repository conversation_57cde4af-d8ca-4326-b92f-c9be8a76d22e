#!/usr/bin/env python3
"""
Test PlayerResponse conversion
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.supercoach_db import supercoach_db
from api.supercoach_endpoints import PlayerResponse

def test_player_response():
    """Test PlayerResponse conversion"""
    print("🔍 Testing PlayerResponse conversion...")
    
    try:
        # Get a player from database
        with supercoach_db.get_session() as session:
            from database.supercoach_models import SuperCoachPlayer
            player = session.query(SuperCoachPlayer).first()
            
            if not player:
                print("❌ No players found in database")
                return False
            
            print(f"  🏈 Testing with player: {player.name}")
            print(f"  🏈 Team object: {player.team}")
            print(f"  🏈 Team name: {player.team.name if player.team else 'None'}")
            
            # Test PlayerResponse conversion
            print("  📊 Testing PlayerResponse.from_player...")
            player_response = PlayerResponse.from_player(player)
            print(f"    ✅ Converted successfully!")
            print(f"    📊 Name: {player_response.name}")
            print(f"    🏈 Team: {player_response.team}")
            print(f"    📊 Position: {player_response.position}")
            print(f"    💰 Price: {player_response.current_price}")
            print(f"    📊 Breakeven: {player_response.current_breakeven}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing PlayerResponse: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_player_response()
