/**
 * SportRadar API Real Test
 * Test actual API endpoints with your trial key
 */

const API_KEY = 'aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN';

// Common SportRadar API patterns based on their documentation
const POSSIBLE_ENDPOINTS = [
  // Competition/League endpoints
  'https://api.sportradar.com/rugby-league/trial/v3/competitions.json',
  'https://api.sportradar.com/rugby-league/trial/v3/seasons.json',
  
  // Team endpoints  
  'https://api.sportradar.com/rugby-league/trial/v3/teams.json',
  
  // Player endpoints
  'https://api.sportradar.com/rugby-league/trial/v3/players.json',
  
  // Schedule/Fixtures
  'https://api.sportradar.com/rugby-league/trial/v3/schedules.json',
  'https://api.sportradar.com/rugby-league/trial/v3/fixtures.json',
  
  // Injuries
  'https://api.sportradar.com/rugby-league/trial/v3/injuries.json',
  
  // Alternative patterns
  'https://api.sportradar.com/rugby-league/trial/v3/competitions/sr:competition:1/info.json',
  'https://api.sportradar.com/rugby-league/trial/v3/competitions/sr:competition:1/standings.json',
];

export async function testSportRadarAPI() {
  console.log('🧪 TESTING SPORTRADAR NRL API');
  console.log('============================');
  console.log('🔑 API Key:', API_KEY);
  console.log('');

  const results = [];

  for (const endpoint of POSSIBLE_ENDPOINTS) {
    const url = `${endpoint}?api_key=${API_KEY}`;
    
    try {
      console.log(`📡 Testing: ${endpoint}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ SUCCESS! Data keys:`, Object.keys(data));
        console.log(`   📊 Sample data:`, JSON.stringify(data, null, 2).substring(0, 200) + '...');
        
        results.push({
          endpoint,
          success: true,
          status: response.status,
          data: data
        });
      } else {
        const errorText = await response.text();
        console.log(`   ❌ FAILED: ${errorText}`);
        
        results.push({
          endpoint,
          success: false,
          status: response.status,
          error: errorText
        });
      }
      
    } catch (error) {
      console.log(`   💥 ERROR:`, error);
      
      results.push({
        endpoint,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
    
    console.log('');
    
    // Rate limiting - wait 1 second between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Summary
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log('📊 SUMMARY');
  console.log('==========');
  console.log(`✅ Successful: ${successful.length}`);
  console.log(`❌ Failed: ${failed.length}`);
  console.log('');
  
  if (successful.length > 0) {
    console.log('🎉 WORKING ENDPOINTS:');
    successful.forEach(result => {
      console.log(`   ✅ ${result.endpoint}`);
    });
    console.log('');
  }
  
  if (failed.length > 0) {
    console.log('💥 FAILED ENDPOINTS:');
    failed.forEach(result => {
      console.log(`   ❌ ${result.endpoint} - ${result.error || result.status}`);
    });
  }

  return results;
}

// Quick test for a specific known pattern
export async function testSpecificEndpoint() {
  // This is a common SportRadar pattern for competitions
  const url = `https://api.sportradar.com/rugby-league/trial/v3/competitions.json?api_key=${API_KEY}`;
  
  console.log('🎯 TESTING SPECIFIC ENDPOINT');
  console.log('============================');
  console.log('📡 URL:', url);
  
  try {
    const response = await fetch(url);
    console.log('📊 Status:', response.status);
    console.log('📋 Headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ SUCCESS!');
      console.log('📊 Data:', data);
      return data;
    } else {
      const errorText = await response.text();
      console.log('❌ FAILED:', errorText);
      return null;
    }
  } catch (error) {
    console.log('💥 ERROR:', error);
    return null;
  }
}

// Test CORS specifically
export async function testCORS() {
  console.log('🌐 TESTING CORS POLICY');
  console.log('======================');
  
  const url = `https://api.sportradar.com/rugby-league/trial/v3/competitions.json?api_key=${API_KEY}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });
    
    console.log('✅ CORS is working!');
    return true;
  } catch (error) {
    if (error instanceof Error && error.message.includes('CORS')) {
      console.log('❌ CORS blocked - need server-side proxy');
      return false;
    }
    console.log('💥 Other error:', error);
    return false;
  }
}

// Test using our server-side proxy
export async function testViaProxy() {
  console.log('🔄 TESTING VIA SERVER-SIDE PROXY');
  console.log('=================================');

  const endpoints = [
    'competitions.json',
    'teams.json',
    'players.json',
    'schedules.json',
  ];

  const results = [];

  for (const endpoint of endpoints) {
    const url = `/api/sportradar-proxy?endpoint=${encodeURIComponent(endpoint)}`;

    try {
      console.log(`📡 Testing proxy: ${endpoint}`);

      const response = await fetch(url);
      console.log(`   Status: ${response.status}`);

      if (response.ok) {
        const result = await response.json();
        console.log(`   ✅ SUCCESS! Data keys:`, Object.keys(result.data || {}));

        results.push({
          endpoint,
          success: true,
          data: result.data,
          via: 'proxy'
        });
      } else {
        const error = await response.json();
        console.log(`   ❌ FAILED:`, error.error);

        results.push({
          endpoint,
          success: false,
          error: error.error,
          via: 'proxy'
        });
      }

    } catch (error) {
      console.log(`   💥 ERROR:`, error);

      results.push({
        endpoint,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        via: 'proxy'
      });
    }

    console.log('');
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  return results;
}

export default testSportRadarAPI;
