"""
Weather Analysis Tools

Tools for collecting weather data and analyzing its impact on NRL player
performance and fantasy scoring.
"""

import logging
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class WeatherAPI:
    """Tool for fetching weather data for NRL venues."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.base_url = "http://api.openweathermap.org/data/2.5"
        
        # NRL venue locations
        self.venues = {
            'ANZ Stadium': {'lat': -33.8479, 'lon': 151.0631, 'city': 'Sydney'},
            'Allianz Stadium': {'lat': -33.8886, 'lon': 151.2314, 'city': 'Sydney'},
            'Suncorp Stadium': {'lat': -27.4648, 'lon': 153.0099, 'city': 'Brisbane'},
            'AAMI Park': {'lat': -37.8255, 'lon': 144.9816, 'city': 'Melbourne'},
            'BlueBet Stadium': {'lat': -33.7581, 'lon': 150.7861, 'city': 'Penrith'},
            'Commbank Stadium': {'lat': -33.8479, 'lon': 151.0631, 'city': 'Parramatta'},
            'McDonald Jones Stadium': {'lat': -32.9173, 'lon': 151.7028, 'city': 'Newcastle'},
            'WIN Stadium': {'lat': -34.4278, 'lon': 150.8931, 'city': 'Wollongong'},
            'Cbus Super Stadium': {'lat': -28.0023, 'lon': 153.4145, 'city': 'Gold Coast'},
            'Sunshine Coast Stadium': {'lat': -26.6509, 'lon': 153.0818, 'city': 'Sunshine Coast'}
        }
    
    def get_current_weather(self, venue: str) -> Dict[str, Any]:
        """Get current weather for a venue."""
        if venue not in self.venues:
            logger.warning(f"Unknown venue: {venue}")
            return {}
        
        try:
            # Mock weather data for development
            venue_info = self.venues[venue]
            
            # Simulate different weather conditions
            import random
            conditions = ['clear', 'cloudy', 'rain', 'storm']
            condition = random.choice(conditions)
            
            mock_weather = {
                'venue': venue,
                'city': venue_info['city'],
                'timestamp': datetime.now().isoformat(),
                'temperature': round(random.uniform(10, 30), 1),
                'humidity': random.randint(40, 90),
                'wind_speed': round(random.uniform(5, 25), 1),
                'wind_direction': random.choice(['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW']),
                'condition': condition,
                'precipitation': round(random.uniform(0, 10), 1) if condition in ['rain', 'storm'] else 0,
                'visibility': round(random.uniform(5, 15), 1),
                'pressure': round(random.uniform(1000, 1020), 1)
            }
            
            logger.info(f"Retrieved weather for {venue}: {condition}, {mock_weather['temperature']}°C")
            return mock_weather
            
        except Exception as e:
            logger.error(f"Error fetching weather for {venue}: {e}")
            return {}
    
    def get_forecast(self, venue: str, days: int = 5) -> List[Dict[str, Any]]:
        """Get weather forecast for a venue."""
        if venue not in self.venues:
            logger.warning(f"Unknown venue: {venue}")
            return []
        
        try:
            forecast = []
            venue_info = self.venues[venue]
            
            for day in range(days):
                date = datetime.now() + timedelta(days=day)
                
                # Mock forecast data
                import random
                conditions = ['clear', 'cloudy', 'rain', 'storm']
                condition = random.choice(conditions)
                
                day_forecast = {
                    'venue': venue,
                    'city': venue_info['city'],
                    'date': date.strftime('%Y-%m-%d'),
                    'day_of_week': date.strftime('%A'),
                    'temperature_high': round(random.uniform(15, 30), 1),
                    'temperature_low': round(random.uniform(5, 20), 1),
                    'humidity': random.randint(40, 90),
                    'wind_speed': round(random.uniform(5, 25), 1),
                    'condition': condition,
                    'precipitation_chance': random.randint(0, 100),
                    'precipitation_amount': round(random.uniform(0, 15), 1) if condition in ['rain', 'storm'] else 0
                }
                
                forecast.append(day_forecast)
            
            logger.info(f"Retrieved {days}-day forecast for {venue}")
            return forecast
            
        except Exception as e:
            logger.error(f"Error fetching forecast for {venue}: {e}")
            return []

class WeatherImpactAnalyzer:
    """Analyzes weather impact on NRL player performance."""
    
    def __init__(self):
        # Weather impact factors based on historical analysis
        self.impact_factors = {
            'temperature': {
                'optimal_range': (18, 25),  # Celsius
                'performance_dropoff': 0.02  # per degree outside optimal
            },
            'wind': {
                'threshold': 20,  # km/h
                'kicking_impact': 0.15,  # reduction in kicking accuracy
                'passing_impact': 0.05   # reduction in passing accuracy
            },
            'rain': {
                'light_threshold': 2,    # mm
                'heavy_threshold': 10,   # mm
                'handling_impact': 0.1,  # increase in errors
                'speed_impact': 0.05     # reduction in speed
            },
            'humidity': {
                'threshold': 80,         # percentage
                'fatigue_impact': 0.08   # increase in fatigue
            }
        }
    
    def analyze_weather_impact(self, weather: Dict[str, Any], 
                             player_position: str = None) -> Dict[str, Any]:
        """Analyze weather impact on player performance."""
        if not weather:
            return {'impact_score': 0.0, 'factors': []}
        
        impact_factors = []
        total_impact = 0.0
        
        # Temperature impact
        temp = weather.get('temperature', 20)
        optimal_min, optimal_max = self.impact_factors['temperature']['optimal_range']
        
        if temp < optimal_min:
            temp_impact = (optimal_min - temp) * self.impact_factors['temperature']['performance_dropoff']
            impact_factors.append(f"Cold temperature ({temp}°C) may reduce performance")
            total_impact -= temp_impact
        elif temp > optimal_max:
            temp_impact = (temp - optimal_max) * self.impact_factors['temperature']['performance_dropoff']
            impact_factors.append(f"Hot temperature ({temp}°C) may reduce performance")
            total_impact -= temp_impact
        
        # Wind impact
        wind_speed = weather.get('wind_speed', 0)
        if wind_speed > self.impact_factors['wind']['threshold']:
            if player_position in ['Halfback', 'Five-eighth', 'Fullback']:
                wind_impact = self.impact_factors['wind']['kicking_impact']
                impact_factors.append(f"Strong winds ({wind_speed} km/h) will affect kicking")
                total_impact -= wind_impact
            else:
                wind_impact = self.impact_factors['wind']['passing_impact']
                impact_factors.append(f"Strong winds ({wind_speed} km/h) may affect passing")
                total_impact -= wind_impact
        
        # Rain impact
        precipitation = weather.get('precipitation', 0)
        if precipitation > self.impact_factors['rain']['heavy_threshold']:
            rain_impact = self.impact_factors['rain']['handling_impact']
            impact_factors.append(f"Heavy rain ({precipitation}mm) will increase handling errors")
            total_impact -= rain_impact
        elif precipitation > self.impact_factors['rain']['light_threshold']:
            rain_impact = self.impact_factors['rain']['handling_impact'] * 0.5
            impact_factors.append(f"Light rain ({precipitation}mm) may affect ball handling")
            total_impact -= rain_impact
        
        # Humidity impact
        humidity = weather.get('humidity', 50)
        if humidity > self.impact_factors['humidity']['threshold']:
            humidity_impact = self.impact_factors['humidity']['fatigue_impact']
            impact_factors.append(f"High humidity ({humidity}%) may increase fatigue")
            total_impact -= humidity_impact
        
        # Positive weather conditions
        if (optimal_min <= temp <= optimal_max and 
            wind_speed < self.impact_factors['wind']['threshold'] and
            precipitation == 0 and 
            humidity < self.impact_factors['humidity']['threshold']):
            impact_factors.append("Ideal weather conditions for optimal performance")
            total_impact += 0.05
        
        # Normalize impact score (-1 to 1)
        impact_score = max(-1.0, min(1.0, total_impact))
        
        return {
            'impact_score': round(impact_score, 3),
            'impact_level': self._categorize_impact(impact_score),
            'factors': impact_factors,
            'weather_summary': self._summarize_weather(weather),
            'recommendations': self._generate_recommendations(impact_score, impact_factors)
        }
    
    def _categorize_impact(self, score: float) -> str:
        """Categorize weather impact level."""
        if score > 0.02:
            return 'positive'
        elif score < -0.1:
            return 'very_negative'
        elif score < -0.05:
            return 'negative'
        else:
            return 'neutral'
    
    def _summarize_weather(self, weather: Dict[str, Any]) -> str:
        """Create a human-readable weather summary."""
        temp = weather.get('temperature', 'N/A')
        condition = weather.get('condition', 'unknown')
        wind = weather.get('wind_speed', 0)
        
        summary = f"{condition.title()}, {temp}°C"
        
        if wind > 15:
            summary += f", windy ({wind} km/h)"
        
        if weather.get('precipitation', 0) > 0:
            summary += f", {weather['precipitation']}mm rain"
        
        return summary
    
    def _generate_recommendations(self, impact_score: float, 
                                factors: List[str]) -> List[str]:
        """Generate fantasy recommendations based on weather impact."""
        recommendations = []
        
        if impact_score < -0.1:
            recommendations.append("Consider avoiding players heavily dependent on kicking/passing")
            recommendations.append("Favor forwards and defensive players in poor conditions")
        elif impact_score < -0.05:
            recommendations.append("Slight preference for consistent performers over high-risk players")
        elif impact_score > 0.02:
            recommendations.append("Ideal conditions for attacking players and high scorers")
            recommendations.append("Consider captaining skill-based players")
        
        if any('wind' in factor.lower() for factor in factors):
            recommendations.append("Avoid goal kickers and long-range passers")
        
        if any('rain' in factor.lower() for factor in factors):
            recommendations.append("Favor forwards and players with strong ball security")
        
        return recommendations
    
    def analyze_venue_weather_history(self, venue: str, 
                                    historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze historical weather patterns for a venue."""
        if not historical_data:
            return {'venue': venue, 'analysis': 'No historical data available'}
        
        # Calculate averages
        avg_temp = sum(d.get('temperature', 20) for d in historical_data) / len(historical_data)
        avg_wind = sum(d.get('wind_speed', 0) for d in historical_data) / len(historical_data)
        avg_humidity = sum(d.get('humidity', 50) for d in historical_data) / len(historical_data)
        
        # Count weather conditions
        conditions = [d.get('condition', 'unknown') for d in historical_data]
        condition_counts = {cond: conditions.count(cond) for cond in set(conditions)}
        
        # Rain frequency
        rainy_days = sum(1 for d in historical_data if d.get('precipitation', 0) > 0)
        rain_frequency = rainy_days / len(historical_data)
        
        return {
            'venue': venue,
            'data_points': len(historical_data),
            'averages': {
                'temperature': round(avg_temp, 1),
                'wind_speed': round(avg_wind, 1),
                'humidity': round(avg_humidity, 1)
            },
            'conditions_frequency': condition_counts,
            'rain_frequency': round(rain_frequency, 2),
            'venue_characteristics': self._analyze_venue_characteristics(
                avg_temp, avg_wind, avg_humidity, rain_frequency
            )
        }
    
    def _analyze_venue_characteristics(self, avg_temp: float, avg_wind: float, 
                                     avg_humidity: float, rain_freq: float) -> List[str]:
        """Analyze venue characteristics based on weather patterns."""
        characteristics = []
        
        if avg_temp > 25:
            characteristics.append("Generally hot venue - fatigue factor")
        elif avg_temp < 15:
            characteristics.append("Generally cool venue - may affect handling")
        
        if avg_wind > 15:
            characteristics.append("Windy venue - affects kicking games")
        
        if avg_humidity > 75:
            characteristics.append("High humidity venue - increased fatigue risk")
        
        if rain_freq > 0.3:
            characteristics.append("Wet weather venue - handling errors more likely")
        elif rain_freq < 0.1:
            characteristics.append("Dry venue - ideal for skill-based play")
        
        return characteristics

def main():
    """Demo function to test weather analysis tools."""
    print("🌤️  Testing FantasyPro Weather Analysis Tools...")
    print("=" * 60)
    
    # Test weather API
    weather_api = WeatherAPI()
    
    print("🏟️  Getting current weather for venues...")
    venues = ['ANZ Stadium', 'Suncorp Stadium', 'AAMI Park']
    
    for venue in venues:
        weather = weather_api.get_current_weather(venue)
        if weather:
            print(f"   {venue}: {weather['condition']}, {weather['temperature']}°C")
    
    print("\n📅 Getting weather forecast...")
    forecast = weather_api.get_forecast('ANZ Stadium', days=3)
    for day in forecast:
        print(f"   {day['day_of_week']}: {day['condition']}, {day['temperature_high']}°C")
    
    # Test weather impact analysis
    print("\n🎯 Analyzing weather impact...")
    analyzer = WeatherImpactAnalyzer()
    
    # Test with different weather conditions
    test_weather = {
        'temperature': 28,
        'wind_speed': 25,
        'precipitation': 5,
        'humidity': 85,
        'condition': 'rain'
    }
    
    impact = analyzer.analyze_weather_impact(test_weather, 'Halfback')
    print(f"   Impact Score: {impact['impact_score']}")
    print(f"   Impact Level: {impact['impact_level']}")
    print(f"   Weather: {impact['weather_summary']}")
    
    if impact['factors']:
        print("   Factors:")
        for factor in impact['factors']:
            print(f"     • {factor}")
    
    if impact['recommendations']:
        print("   Recommendations:")
        for rec in impact['recommendations']:
            print(f"     • {rec}")
    
    print("\n✅ Weather analysis tools test completed!")

if __name__ == "__main__":
    main()
