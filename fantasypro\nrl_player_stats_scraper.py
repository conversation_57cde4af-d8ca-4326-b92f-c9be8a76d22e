#!/usr/bin/env python3
"""
NRL Player Stats Scraper
Deep scrapes nrlsupercoachlive.com and nrlsupercoachstats.com for granular player data
"""

import requests
from bs4 import BeautifulSoup
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path
import re
import time

logger = logging.getLogger(__name__)

class NRLPlayerStatsScraper:
    """Comprehensive NRL player statistics scraper"""
    
    def __init__(self, cache_hours: int = 6):
        self.cache_hours = cache_hours
        self.cache_dir = Path("data/nrl_player_cache")
        self.cache_dir.mkdir(exist_ok=True)
        
        # Headers to mimic real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-AU,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # Target sites with enhanced URLs for granular stats
        self.sites = {
            'nrlsupercoachlive': {
                'base_url': 'https://nrlsupercoachlive.com',
                'players_url': 'https://nrlsupercoachlive.com/players',
                'stats_url': 'https://nrlsupercoachlive.com/stats',
                'detailed_stats_urls': [
                    'https://nrlsupercoachlive.com/stats/tries',
                    'https://nrlsupercoachlive.com/stats/tackles',
                    'https://nrlsupercoachlive.com/stats/runs',
                    'https://nrlsupercoachlive.com/stats/metres',
                    'https://nrlsupercoachlive.com/stats/assists',
                    'https://nrlsupercoachlive.com/stats/linebreaks',
                    'https://nrlsupercoachlive.com/stats/offloads'
                ]
            },
            'nrlsupercoachstats': {
                'base_url': 'https://www.nrlsupercoachstats.com',
                'players_url': 'https://www.nrlsupercoachstats.com/players',
                'stats_url': 'https://www.nrlsupercoachstats.com/stats',
                'detailed_stats_urls': [
                    'https://www.nrlsupercoachstats.com/stats/scoring',
                    'https://www.nrlsupercoachstats.com/stats/attacking',
                    'https://www.nrlsupercoachstats.com/stats/defensive',
                    'https://www.nrlsupercoachstats.com/stats/kicking',
                    'https://www.nrlsupercoachstats.com/stats/discipline'
                ]
            }
        }
    
    def scrape_all_player_data(self) -> Dict[str, Any]:
        """Scrape comprehensive player data from all sources"""
        try:
            logger.info("🏈 Starting comprehensive NRL player data collection")
            
            all_data = {
                'collection_timestamp': datetime.now().isoformat(),
                'sources': {},
                'consolidated_players': {}
            }
            
            # Scrape from each site
            for site_name, site_config in self.sites.items():
                try:
                    logger.info(f"📊 Scraping {site_name}")
                    site_data = self._scrape_site(site_name, site_config)
                    if site_data:
                        all_data['sources'][site_name] = site_data
                        logger.info(f"✅ {site_name}: {len(site_data.get('players', []))} players")
                    else:
                        logger.warning(f"⚠️ {site_name}: No data collected")
                        
                    time.sleep(2)  # Rate limiting
                    
                except Exception as e:
                    logger.error(f"❌ Error scraping {site_name}: {e}")
                    continue
            
            # Consolidate player data
            consolidated = self._consolidate_player_data(all_data['sources'])
            all_data['consolidated_players'] = consolidated
            
            # Save comprehensive data
            self._save_comprehensive_data(all_data)
            
            logger.info(f"🎯 Collection complete: {len(consolidated)} unique players")
            return all_data
            
        except Exception as e:
            logger.error(f"Error in comprehensive data collection: {e}")
            return {}
    
    def _scrape_site(self, site_name: str, site_config: Dict[str, str]) -> Dict[str, Any]:
        """Scrape data from a specific site"""
        try:
            site_data = {
                'site_name': site_name,
                'extracted_at': datetime.now().isoformat(),
                'players': [],
                'stats_tables': [],
                'metadata': {}
            }
            
            # Try different URLs for player data including detailed stats
            urls_to_try = [
                site_config.get('players_url'),
                site_config.get('stats_url'),
                site_config.get('base_url'),
                f"{site_config.get('base_url')}/nrl",
                f"{site_config.get('base_url')}/supercoach"
            ]

            # Add detailed stats URLs
            detailed_urls = site_config.get('detailed_stats_urls', [])
            urls_to_try.extend(detailed_urls)
            
            for url in urls_to_try:
                if not url:
                    continue
                    
                try:
                    logger.info(f"🔍 Trying URL: {url}")
                    response = self.session.get(url, timeout=15)
                    response.raise_for_status()
                    
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Extract player data
                    players = self._extract_players_from_page(soup, url)
                    if players:
                        site_data['players'].extend(players)
                        logger.info(f"✅ Found {len(players)} players on {url}")
                    
                    # Extract stats tables
                    tables = self._extract_stats_tables(soup)
                    if tables:
                        site_data['stats_tables'].extend(tables)
                        logger.info(f"✅ Found {len(tables)} stats tables on {url}")
                    
                    # If we found data, we can break
                    if players or tables:
                        site_data['successful_url'] = url
                        break
                        
                except Exception as e:
                    logger.debug(f"URL {url} failed: {e}")
                    continue
            
            return site_data
            
        except Exception as e:
            logger.error(f"Error scraping site {site_name}: {e}")
            return {}
    
    def _extract_players_from_page(self, soup: BeautifulSoup, url: str) -> List[Dict[str, Any]]:
        """Extract player data from a page"""
        try:
            players = []
            
            # Try different selectors for player data
            player_selectors = [
                '.player-card',
                '.player-row',
                '.player-item',
                '.player',
                'tr[data-player]',
                '.sc-player',
                '[data-player-id]'
            ]
            
            for selector in player_selectors:
                try:
                    elements = soup.select(selector)
                    if elements:
                        logger.info(f"Found {len(elements)} elements with selector: {selector}")
                        
                        for element in elements[:200]:  # Limit to prevent overload
                            player_data = self._extract_single_player(element, url)
                            if player_data:
                                players.append(player_data)
                        
                        if players:
                            break
                            
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            # If no players found with specific selectors, try table rows
            if not players:
                players = self._extract_players_from_tables(soup, url)
            
            return players
            
        except Exception as e:
            logger.error(f"Error extracting players from page: {e}")
            return []
    
    def _extract_single_player(self, element, url: str) -> Optional[Dict[str, Any]]:
        """Extract data for a single player"""
        try:
            player = {
                'source_url': url,
                'extracted_at': datetime.now().isoformat()
            }
            
            # Extract player name
            name_selectors = [
                '.player-name', '.name', 'h3', 'h4', 'h5',
                '[data-name]', '.sc-name', '.player-title'
            ]
            
            for selector in name_selectors:
                try:
                    name_elem = element.select_one(selector)
                    if name_elem:
                        player['name'] = name_elem.get_text(strip=True)
                        break
                except:
                    continue
            
            # Extract team
            team_selectors = [
                '.team', '.club', '[data-team]', '.sc-team',
                '.player-team', '.team-name'
            ]
            
            for selector in team_selectors:
                try:
                    team_elem = element.select_one(selector)
                    if team_elem:
                        player['team'] = team_elem.get_text(strip=True)
                        break
                except:
                    continue
            
            # Extract position
            pos_selectors = [
                '.position', '.pos', '[data-position]', '.sc-position',
                '.player-position'
            ]
            
            for selector in pos_selectors:
                try:
                    pos_elem = element.select_one(selector)
                    if pos_elem:
                        player['position'] = pos_elem.get_text(strip=True)
                        break
                except:
                    continue
            
            # Extract price/cost
            price_selectors = [
                '.price', '.cost', '[data-price]', '.sc-price',
                '.player-price', '.salary'
            ]
            
            for selector in price_selectors:
                try:
                    price_elem = element.select_one(selector)
                    if price_elem:
                        player['price'] = self._extract_price(price_elem.get_text())
                        break
                except:
                    continue
            
            # Extract statistics from element text
            element_text = element.get_text()
            stats = self._extract_stats_from_text(element_text)
            if stats:
                player.update(stats)
            
            # Extract any data attributes
            for attr in element.attrs:
                if attr.startswith('data-'):
                    try:
                        value = element.attrs[attr]
                        if value and value.isdigit():
                            player[attr.replace('data-', '')] = int(value)
                        elif value:
                            player[attr.replace('data-', '')] = value
                    except:
                        continue
            
            return player if player.get('name') else None
            
        except Exception as e:
            logger.debug(f"Error extracting single player: {e}")
            return None
    
    def _extract_players_from_tables(self, soup: BeautifulSoup, url: str) -> List[Dict[str, Any]]:
        """Extract player data from tables"""
        try:
            players = []
            
            tables = soup.find_all('table')
            for table in tables:
                try:
                    # Get headers
                    header_row = table.find('thead') or table.find('tr')
                    if not header_row:
                        continue
                    
                    headers = []
                    for th in header_row.find_all(['th', 'td']):
                        headers.append(th.get_text(strip=True).lower())
                    
                    if not headers or 'name' not in ' '.join(headers):
                        continue
                    
                    # Get data rows
                    rows = table.find_all('tr')[1:]  # Skip header
                    for row in rows[:100]:  # Limit rows
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= len(headers):
                            player_data = {
                                'source_url': url,
                                'extracted_at': datetime.now().isoformat()
                            }
                            
                            for i, header in enumerate(headers):
                                if i < len(cells):
                                    cell_text = cells[i].get_text(strip=True)
                                    if cell_text:
                                        # Clean up header name
                                        clean_header = re.sub(r'[^\w]', '_', header.lower())
                                        player_data[clean_header] = cell_text
                            
                            if player_data.get('name') or player_data.get('player'):
                                players.append(player_data)
                
                except Exception as e:
                    logger.debug(f"Error extracting table: {e}")
                    continue
            
            return players
            
        except Exception as e:
            logger.error(f"Error extracting players from tables: {e}")
            return []
    
    def _extract_stats_tables(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract statistics tables from page"""
        try:
            tables_data = []
            
            tables = soup.find_all('table')
            for i, table in enumerate(tables):
                try:
                    table_data = {
                        'table_index': i,
                        'headers': [],
                        'rows': [],
                        'extracted_at': datetime.now().isoformat()
                    }
                    
                    # Extract headers
                    header_row = table.find('thead') or table.find('tr')
                    if header_row:
                        for th in header_row.find_all(['th', 'td']):
                            table_data['headers'].append(th.get_text(strip=True))
                    
                    # Extract rows
                    rows = table.find_all('tr')[1:]  # Skip header
                    for row in rows[:50]:  # Limit rows
                        row_data = []
                        for cell in row.find_all(['td', 'th']):
                            row_data.append(cell.get_text(strip=True))
                        if row_data:
                            table_data['rows'].append(row_data)
                    
                    if table_data['headers'] and table_data['rows']:
                        tables_data.append(table_data)
                
                except Exception as e:
                    logger.debug(f"Error extracting table {i}: {e}")
                    continue
            
            return tables_data
            
        except Exception as e:
            logger.error(f"Error extracting stats tables: {e}")
            return []
    
    def _extract_stats_from_text(self, text: str) -> Dict[str, Any]:
        """Extract comprehensive statistical data from text"""
        try:
            stats = {}

            # Enhanced SuperCoach stat patterns for granular breakdowns
            patterns = {
                # Basic stats
                'average': r'avg[:\s]*(\d+\.?\d*)',
                'points': r'pts?[:\s]*(\d+)',
                'price': r'\$(\d+,?\d*)',
                'breakeven': r'be[:\s]*(\d+)',
                'ownership': r'(\d+\.?\d*)%',
                'games_played': r'games?[:\s]*(\d+)',
                'minutes': r'min[:\s]*(\d+)',

                # Attacking stats
                'tries': r'tries?[:\s]*(\d+)',
                'try_assists': r'try[\s\-]?assists?[:\s]*(\d+)',
                'linebreaks': r'line[\s\-]?breaks?[:\s]*(\d+)',
                'linebreak_assists': r'linebreak[\s\-]?assists?[:\s]*(\d+)',
                'offloads': r'offloads?[:\s]*(\d+)',
                'runs': r'runs?[:\s]*(\d+)',
                'metres': r'metres?[:\s]*(\d+)',
                'post_contact_metres': r'post[\s\-]?contact[\s\-]?metres?[:\s]*(\d+)',

                # Defensive stats
                'tackles': r'tackles?[:\s]*(\d+)',
                'missed_tackles': r'missed[\s\-]?tackles?[:\s]*(\d+)',
                'tackle_efficiency': r'tackle[\s\-]?efficiency[:\s]*(\d+\.?\d*)%?',
                'intercepts': r'intercepts?[:\s]*(\d+)',

                # Kicking stats
                'kicks': r'kicks?[:\s]*(\d+)',
                'kick_metres': r'kick[\s\-]?metres?[:\s]*(\d+)',
                'forced_dropouts': r'forced[\s\-]?dropouts?[:\s]*(\d+)',
                '40_20s': r'40[\s\-]?20s?[:\s]*(\d+)',

                # Discipline
                'errors': r'errors?[:\s]*(\d+)',
                'penalties': r'penalties?[:\s]*(\d+)',
                'sin_bins': r'sin[\s\-]?bins?[:\s]*(\d+)',
                'send_offs': r'send[\s\-]?offs?[:\s]*(\d+)',

                # Fantasy specific
                'fantasy_points': r'fantasy[\s\-]?points?[:\s]*(\d+)',
                'supercoach_points': r'supercoach[\s\-]?points?[:\s]*(\d+)',
                'nrl_fantasy_points': r'nrl[\s\-]?fantasy[\s\-]?points?[:\s]*(\d+)',

                # Form indicators
                'last_3_avg': r'last[\s\-]?3[\s\-]?avg[:\s]*(\d+\.?\d*)',
                'last_5_avg': r'last[\s\-]?5[\s\-]?avg[:\s]*(\d+\.?\d*)',
                'home_avg': r'home[\s\-]?avg[:\s]*(\d+\.?\d*)',
                'away_avg': r'away[\s\-]?avg[:\s]*(\d+\.?\d*)'
            }

            text_lower = text.lower()
            for stat_name, pattern in patterns.items():
                try:
                    match = re.search(pattern, text_lower)
                    if match:
                        value = match.group(1).replace(',', '')
                        # Handle percentage values
                        if '%' in pattern and not value.endswith('%'):
                            if '.' in value:
                                stats[stat_name] = float(value)
                            else:
                                stats[stat_name] = int(value)
                        else:
                            if '.' in value:
                                stats[stat_name] = float(value)
                            else:
                                stats[stat_name] = int(value)
                except:
                    continue

            return stats

        except Exception as e:
            logger.debug(f"Error extracting stats from text: {e}")
            return {}
    
    def _extract_price(self, text: str) -> Optional[int]:
        """Extract price from text"""
        try:
            # Remove $ and commas, extract number
            clean_text = re.sub(r'[$,]', '', text)
            match = re.search(r'(\d+)', clean_text)
            return int(match.group(1)) if match else None
        except:
            return None
    
    def _consolidate_player_data(self, sources_data: Dict[str, Any]) -> Dict[str, Any]:
        """Consolidate player data from multiple sources"""
        try:
            consolidated = {}
            
            for source_name, source_data in sources_data.items():
                players = source_data.get('players', [])
                
                for player in players:
                    name = player.get('name', '').strip()
                    if not name:
                        continue
                    
                    # Normalize name for matching
                    normalized_name = re.sub(r'[^\w\s]', '', name.lower()).strip()
                    
                    if normalized_name not in consolidated:
                        consolidated[normalized_name] = {
                            'name': name,
                            'sources': {},
                            'consolidated_stats': {}
                        }
                    
                    # Add source data
                    consolidated[normalized_name]['sources'][source_name] = player
                    
                    # Merge stats
                    for key, value in player.items():
                        if key not in ['name', 'source_url', 'extracted_at']:
                            if key not in consolidated[normalized_name]['consolidated_stats']:
                                consolidated[normalized_name]['consolidated_stats'][key] = value
            
            return consolidated
            
        except Exception as e:
            logger.error(f"Error consolidating player data: {e}")
            return {}
    
    def _save_comprehensive_data(self, data: Dict[str, Any]):
        """Save comprehensive data to cache"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            cache_file = self.cache_dir / f"nrl_player_data_{timestamp}.json"
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Also save as latest
            latest_file = self.cache_dir / "nrl_player_latest.json"
            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Saved NRL player data to {cache_file}")
            
        except Exception as e:
            logger.error(f"Error saving data: {e}")

def main():
    """Test the NRL player stats scraper"""
    print("🏈 Testing NRL Player Stats Scraper")
    
    scraper = NRLPlayerStatsScraper()
    data = scraper.scrape_all_player_data()
    
    if data:
        print("✅ Data collection successful!")
        
        sources = data.get('sources', {})
        consolidated = data.get('consolidated_players', {})
        
        print(f"📊 Sources scraped: {len(sources)}")
        for source_name, source_data in sources.items():
            player_count = len(source_data.get('players', []))
            table_count = len(source_data.get('stats_tables', []))
            print(f"  - {source_name}: {player_count} players, {table_count} tables")
        
        print(f"🎯 Consolidated players: {len(consolidated)}")
    else:
        print("❌ Data collection failed")

if __name__ == "__main__":
    main()
