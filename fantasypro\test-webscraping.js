/**
 * Simple test for web scraping NRL data
 * Run with: node test-webscraping.js
 */

const https = require('https');
const cheerio = require('cheerio');

// Rate limiting
let lastRequestTime = 0;
const REQUEST_DELAY = 2000; // 2 seconds between requests

async function rateLimitedFetch(url) {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < REQUEST_DELAY) {
    await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY - timeSinceLastRequest));
  }
  
  lastRequestTime = Date.now();

  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

function extractNumber(text) {
  if (!text) return 0;
  const cleaned = text.replace(/[,$%]/g, '').trim();
  const match = cleaned.match(/[\d.]+/);
  return match ? parseFloat(match[0]) : 0;
}

function generatePlayerId(name, team) {
  return `${name.toLowerCase().replace(/\s+/g, '_')}_${team.toLowerCase().replace(/\s+/g, '_')}`;
}

async function testNRLSuperCoachStats() {
  console.log('🕷️ Testing nrlsupercoachstats.com...');

  try {
    // Use the correct URL structure we found
    const url = 'https://www.nrlsupercoachstats.com/stats.php?year=2025';
    console.log(`📡 Fetching: ${url}`);
    
    const response = await rateLimitedFetch(url);
    console.log(`📊 Status: ${response.status}`);
    console.log(`📄 Content length: ${response.data.length} characters`);
    
    if (response.status !== 200) {
      console.log(`❌ HTTP Error: ${response.status}`);
      console.log(`📄 Response: ${response.data.substring(0, 500)}...`);
      return { success: false, error: `HTTP ${response.status}` };
    }

    const $ = cheerio.load(response.data);
    console.log('✅ HTML parsed successfully');

    // Log page title and basic structure
    const title = $('title').text();
    console.log(`📋 Page title: ${title}`);

    // Try to find any tables
    const tables = $('table');
    console.log(`📊 Found ${tables.length} tables`);

    // Try to find any player-related elements
    const playerElements = $('[class*="player"], [id*="player"], [data-player]');
    console.log(`👤 Found ${playerElements.length} player-related elements`);

    // Look for common data patterns
    const rows = $('tr, .row, .item');
    console.log(`📋 Found ${rows.length} potential data rows`);

    // Sample some content
    console.log('\n📄 SAMPLE CONTENT:');
    console.log('==================');
    
    // Get first few table rows if they exist
    if (tables.length > 0) {
      console.log('📊 First table structure:');
      const firstTable = tables.first();
      const headers = firstTable.find('th, thead td');
      console.log(`   Headers (${headers.length}):`, headers.map((i, el) => $(el).text().trim()).get().slice(0, 10));
      
      const dataRows = firstTable.find('tbody tr, tr').slice(0, 3);
      console.log(`   Sample rows (${dataRows.length}):`);
      dataRows.each((i, row) => {
        const cells = $(row).find('td, th');
        const cellData = cells.map((j, cell) => $(cell).text().trim()).get();
        console.log(`     Row ${i + 1}:`, cellData.slice(0, 6));
      });
    }

    // Look for any text that might be player names
    const bodyText = $('body').text();
    const playerNamePattern = /[A-Z][a-z]+ [A-Z][a-z]+/g;
    const potentialNames = bodyText.match(playerNamePattern) || [];
    const uniqueNames = [...new Set(potentialNames)].slice(0, 10);
    console.log(`🔍 Potential player names found: ${uniqueNames.join(', ')}`);

    return {
      success: true,
      data: {
        title,
        tables: tables.length,
        playerElements: playerElements.length,
        rows: rows.length,
        sampleNames: uniqueNames
      }
    };

  } catch (error) {
    console.error('❌ Error testing nrlsupercoachstats.com:', error.message);
    return { success: false, error: error.message };
  }
}

async function testNRLSuperCoachLive() {
  console.log('\n🕷️ Testing footystatistics.com (ownership data)...');

  try {
    // Use footystatistics.com for ownership data instead
    const url = 'https://www.footystatistics.com/ownership.php';
    console.log(`📡 Fetching: ${url}`);
    
    const response = await rateLimitedFetch(url);
    console.log(`📊 Status: ${response.status}`);
    console.log(`📄 Content length: ${response.data.length} characters`);
    
    if (response.status !== 200) {
      console.log(`❌ HTTP Error: ${response.status}`);
      console.log(`📄 Response: ${response.data.substring(0, 500)}...`);
      return { success: false, error: `HTTP ${response.status}` };
    }

    const $ = cheerio.load(response.data);
    console.log('✅ HTML parsed successfully');

    // Log page title and basic structure
    const title = $('title').text();
    console.log(`📋 Page title: ${title}`);

    // Try to find any tables
    const tables = $('table');
    console.log(`📊 Found ${tables.length} tables`);

    // Try to find ownership-related elements
    const ownershipElements = $('[class*="ownership"], [class*="owned"], [class*="percent"]');
    console.log(`📈 Found ${ownershipElements.length} ownership-related elements`);

    // Look for percentage patterns in text
    const bodyText = $('body').text();
    const percentagePattern = /\d+\.?\d*%/g;
    const percentages = bodyText.match(percentagePattern) || [];
    const uniquePercentages = [...new Set(percentages)].slice(0, 10);
    console.log(`📊 Percentages found: ${uniquePercentages.join(', ')}`);

    return {
      success: true,
      data: {
        title,
        tables: tables.length,
        ownershipElements: ownershipElements.length,
        percentages: uniquePercentages
      }
    };

  } catch (error) {
    console.error('❌ Error testing nrlsupercoachlive.com:', error.message);
    return { success: false, error: error.message };
  }
}

async function testAlternativeURLs() {
  console.log('\n🔍 Testing alternative URLs...');
  
  const urls = [
    'https://www.nrlsupercoachstats.com/',
    'https://www.nrlsupercoachstats.com/stats.php?year=2025',
    'https://www.footystatistics.com/',
    'https://www.footystatistics.com/ownership.php',
    'https://www.footystatistics.com/breakevens.php'
  ];

  for (const url of urls) {
    try {
      console.log(`\n📡 Testing: ${url}`);
      const response = await rateLimitedFetch(url);
      console.log(`   Status: ${response.status}`);
      
      if (response.status === 200) {
        const $ = cheerio.load(response.data);
        const title = $('title').text();
        console.log(`   ✅ Success! Title: ${title}`);
      } else {
        console.log(`   ❌ Failed: HTTP ${response.status}`);
      }
      
    } catch (error) {
      console.log(`   💥 Error: ${error.message}`);
    }
  }
}

async function runTests() {
  console.log('🚀 STARTING NRL WEB SCRAPING TESTS');
  console.log('==================================');
  
  const results = [];

  // Test main sites
  const statsResult = await testNRLSuperCoachStats();
  results.push({ site: 'nrlsupercoachstats.com', ...statsResult });

  const liveResult = await testNRLSuperCoachLive();
  results.push({ site: 'nrlsupercoachlive.com', ...liveResult });

  // Test alternative URLs
  await testAlternativeURLs();

  // Summary
  console.log('\n📊 SUMMARY');
  console.log('==========');
  
  results.forEach(result => {
    if (result.success) {
      console.log(`✅ ${result.site}: SUCCESS`);
      if (result.data) {
        console.log(`   - Title: ${result.data.title || 'N/A'}`);
        console.log(`   - Tables: ${result.data.tables || 0}`);
        console.log(`   - Data elements: ${result.data.playerElements || result.data.ownershipElements || 0}`);
      }
    } else {
      console.log(`❌ ${result.site}: FAILED - ${result.error}`);
    }
  });

  const successCount = results.filter(r => r.success).length;
  console.log(`\n🎯 Overall: ${successCount}/${results.length} sites accessible`);

  if (successCount > 0) {
    console.log('\n🚀 NEXT STEPS:');
    console.log('- Web scraping is possible!');
    console.log('- Need to analyze HTML structure for data extraction');
    console.log('- Implement proper selectors based on site structure');
    console.log('- Add data validation and cleaning');
  } else {
    console.log('\n⚠️ ISSUES:');
    console.log('- Sites may be blocking requests');
    console.log('- URLs might have changed');
    console.log('- Consider using different approach or sources');
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testNRLSuperCoachStats, testNRLSuperCoachLive };
