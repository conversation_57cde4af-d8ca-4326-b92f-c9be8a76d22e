#!/usr/bin/env python3
"""
FantasyPro Deployment Script

Automated deployment pipeline for FantasyPro platform including
Docker containerization, environment setup, and health checks.
"""

import os
import sys
import subprocess
import json
import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import yaml

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logging_config import setup_logging

logger = setup_logging(__name__)

class DeploymentManager:
    """Manages deployment pipeline for FantasyPro platform."""
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        self.project_root = Path(__file__).parent.parent
        self.deployment_config = self._load_deployment_config()
        
    def _load_deployment_config(self) -> Dict[str, Any]:
        """Load deployment configuration."""
        config_file = self.project_root / "deployment" / f"{self.environment}.yml"
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        
        # Default configuration
        return {
            'environment': self.environment,
            'services': {
                'api': {
                    'image': 'fantasypro-api',
                    'port': 8000,
                    'replicas': 1,
                    'health_check': '/health'
                },
                'web': {
                    'image': 'fantasypro-web',
                    'port': 3000,
                    'replicas': 1,
                    'health_check': '/'
                },
                'database': {
                    'image': 'postgres:15',
                    'port': 5432,
                    'replicas': 1
                },
                'redis': {
                    'image': 'redis:7-alpine',
                    'port': 6379,
                    'replicas': 1
                }
            },
            'monitoring': {
                'enabled': True,
                'prometheus_port': 9090,
                'grafana_port': 3001
            }
        }
    
    def build_images(self) -> bool:
        """Build Docker images for all services."""
        logger.info("Building Docker images...")
        
        try:
            # Build API image
            logger.info("Building API image...")
            result = subprocess.run([
                'docker', 'build',
                '-f', str(self.project_root / 'docker' / 'Dockerfile.api'),
                '-t', 'fantasypro-api:latest',
                str(self.project_root)
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to build API image: {result.stderr}")
                return False
            
            # Build Web image
            logger.info("Building Web image...")
            result = subprocess.run([
                'docker', 'build',
                '-f', str(self.project_root / 'docker' / 'Dockerfile.web'),
                '-t', 'fantasypro-web:latest',
                str(self.project_root)
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to build Web image: {result.stderr}")
                return False
            
            # Build Agents image
            logger.info("Building Agents image...")
            result = subprocess.run([
                'docker', 'build',
                '-f', str(self.project_root / 'docker' / 'Dockerfile.agents'),
                '-t', 'fantasypro-agents:latest',
                str(self.project_root)
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to build Agents image: {result.stderr}")
                return False
            
            logger.info("All Docker images built successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error building images: {e}")
            return False
    
    def deploy_services(self) -> bool:
        """Deploy all services using Docker Compose."""
        logger.info(f"Deploying services for {self.environment} environment...")
        
        try:
            # Use docker-compose to deploy
            compose_file = self.project_root / "docker-compose.yml"
            override_file = self.project_root / "docker-compose.override.yml"
            
            cmd = ['docker-compose', '-f', str(compose_file)]
            
            if override_file.exists():
                cmd.extend(['-f', str(override_file)])
            
            cmd.extend(['up', '-d'])
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode != 0:
                logger.error(f"Failed to deploy services: {result.stderr}")
                return False
            
            logger.info("Services deployed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error deploying services: {e}")
            return False
    
    def wait_for_services(self, timeout: int = 300) -> bool:
        """Wait for all services to be healthy."""
        logger.info("Waiting for services to be healthy...")
        
        services = self.deployment_config['services']
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            all_healthy = True
            
            for service_name, service_config in services.items():
                if 'health_check' not in service_config:
                    continue
                
                port = service_config['port']
                health_endpoint = service_config['health_check']
                
                try:
                    import requests
                    response = requests.get(
                        f"http://localhost:{port}{health_endpoint}",
                        timeout=5
                    )
                    
                    if response.status_code != 200:
                        logger.debug(f"Service {service_name} not ready (status: {response.status_code})")
                        all_healthy = False
                        break
                        
                except Exception as e:
                    logger.debug(f"Service {service_name} not ready: {e}")
                    all_healthy = False
                    break
            
            if all_healthy:
                logger.info("All services are healthy!")
                return True
            
            time.sleep(10)
        
        logger.error(f"Services did not become healthy within {timeout} seconds")
        return False
    
    def run_health_checks(self) -> Dict[str, bool]:
        """Run comprehensive health checks on all services."""
        logger.info("Running health checks...")
        
        health_status = {}
        services = self.deployment_config['services']
        
        for service_name, service_config in services.items():
            try:
                if 'health_check' not in service_config:
                    health_status[service_name] = True  # Assume healthy if no check
                    continue
                
                port = service_config['port']
                health_endpoint = service_config['health_check']
                
                import requests
                response = requests.get(
                    f"http://localhost:{port}{health_endpoint}",
                    timeout=10
                )
                
                health_status[service_name] = response.status_code == 200
                
                if health_status[service_name]:
                    logger.info(f"✅ {service_name} is healthy")
                else:
                    logger.warning(f"❌ {service_name} is unhealthy (status: {response.status_code})")
                
            except Exception as e:
                logger.error(f"❌ {service_name} health check failed: {e}")
                health_status[service_name] = False
        
        return health_status
    
    def rollback_deployment(self) -> bool:
        """Rollback to previous deployment."""
        logger.info("Rolling back deployment...")
        
        try:
            # Stop current services
            result = subprocess.run([
                'docker-compose', 'down'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode != 0:
                logger.error(f"Failed to stop services: {result.stderr}")
                return False
            
            # TODO: Implement proper rollback logic with versioned images
            logger.info("Rollback completed (basic implementation)")
            return True
            
        except Exception as e:
            logger.error(f"Error during rollback: {e}")
            return False
    
    def generate_deployment_report(self, health_status: Dict[str, bool]) -> Dict[str, Any]:
        """Generate deployment report."""
        total_services = len(health_status)
        healthy_services = sum(1 for status in health_status.values() if status)
        
        report = {
            'deployment_time': datetime.now().isoformat(),
            'environment': self.environment,
            'total_services': total_services,
            'healthy_services': healthy_services,
            'success_rate': (healthy_services / total_services * 100) if total_services > 0 else 0,
            'service_status': health_status,
            'deployment_successful': healthy_services == total_services
        }
        
        return report
    
    def deploy(self) -> bool:
        """Execute complete deployment pipeline."""
        logger.info(f"Starting deployment pipeline for {self.environment}")
        
        try:
            # Step 1: Build images
            if not self.build_images():
                logger.error("Image build failed, aborting deployment")
                return False
            
            # Step 2: Deploy services
            if not self.deploy_services():
                logger.error("Service deployment failed, aborting")
                return False
            
            # Step 3: Wait for services to be ready
            if not self.wait_for_services():
                logger.error("Services failed to become healthy, considering rollback")
                # Optionally rollback here
                return False
            
            # Step 4: Run health checks
            health_status = self.run_health_checks()
            
            # Step 5: Generate report
            report = self.generate_deployment_report(health_status)
            
            # Save deployment report
            report_file = self.project_root / "deployment" / f"deployment_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_file.parent.mkdir(exist_ok=True)
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Deployment report saved to {report_file}")
            
            if report['deployment_successful']:
                logger.info("🎉 Deployment completed successfully!")
                return True
            else:
                logger.error("❌ Deployment completed with issues")
                return False
                
        except Exception as e:
            logger.error(f"Deployment pipeline failed: {e}")
            return False

class MonitoringSetup:
    """Sets up monitoring and alerting for FantasyPro platform."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        
    def setup_prometheus(self) -> bool:
        """Set up Prometheus monitoring."""
        logger.info("Setting up Prometheus monitoring...")
        
        try:
            # Create Prometheus configuration
            prometheus_config = {
                'global': {
                    'scrape_interval': '15s'
                },
                'scrape_configs': [
                    {
                        'job_name': 'fantasypro-api',
                        'static_configs': [
                            {'targets': ['localhost:8000']}
                        ],
                        'metrics_path': '/metrics'
                    },
                    {
                        'job_name': 'fantasypro-agents',
                        'static_configs': [
                            {'targets': ['localhost:8001']}
                        ],
                        'metrics_path': '/metrics'
                    }
                ]
            }
            
            config_dir = self.project_root / "monitoring"
            config_dir.mkdir(exist_ok=True)
            
            with open(config_dir / "prometheus.yml", 'w') as f:
                yaml.dump(prometheus_config, f)
            
            logger.info("Prometheus configuration created")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup Prometheus: {e}")
            return False
    
    def setup_grafana(self) -> bool:
        """Set up Grafana dashboards."""
        logger.info("Setting up Grafana dashboards...")
        
        try:
            # Create basic dashboard configuration
            dashboard_config = {
                'dashboard': {
                    'title': 'FantasyPro Platform Monitoring',
                    'panels': [
                        {
                            'title': 'API Response Time',
                            'type': 'graph',
                            'targets': [
                                {'expr': 'http_request_duration_seconds'}
                            ]
                        },
                        {
                            'title': 'Active Users',
                            'type': 'stat',
                            'targets': [
                                {'expr': 'active_users_total'}
                            ]
                        },
                        {
                            'title': 'AI Agent Performance',
                            'type': 'graph',
                            'targets': [
                                {'expr': 'ai_prediction_accuracy'}
                            ]
                        }
                    ]
                }
            }
            
            config_dir = self.project_root / "monitoring" / "grafana"
            config_dir.mkdir(parents=True, exist_ok=True)
            
            with open(config_dir / "dashboard.json", 'w') as f:
                json.dump(dashboard_config, f, indent=2)
            
            logger.info("Grafana dashboard configuration created")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup Grafana: {e}")
            return False
    
    def setup_alerting(self) -> bool:
        """Set up alerting rules."""
        logger.info("Setting up alerting rules...")
        
        try:
            # Create alerting rules
            alert_rules = {
                'groups': [
                    {
                        'name': 'fantasypro_alerts',
                        'rules': [
                            {
                                'alert': 'HighAPILatency',
                                'expr': 'http_request_duration_seconds > 2',
                                'for': '5m',
                                'labels': {
                                    'severity': 'warning'
                                },
                                'annotations': {
                                    'summary': 'High API latency detected'
                                }
                            },
                            {
                                'alert': 'ServiceDown',
                                'expr': 'up == 0',
                                'for': '1m',
                                'labels': {
                                    'severity': 'critical'
                                },
                                'annotations': {
                                    'summary': 'Service is down'
                                }
                            }
                        ]
                    }
                ]
            }
            
            config_dir = self.project_root / "monitoring"
            with open(config_dir / "alert_rules.yml", 'w') as f:
                yaml.dump(alert_rules, f)
            
            logger.info("Alerting rules created")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup alerting: {e}")
            return False

def main():
    """Main deployment function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='FantasyPro Deployment Pipeline')
    parser.add_argument('--environment', '-e', default='development',
                       choices=['development', 'staging', 'production'],
                       help='Deployment environment')
    parser.add_argument('--build-only', action='store_true',
                       help='Only build images, do not deploy')
    parser.add_argument('--health-check', action='store_true',
                       help='Only run health checks')
    parser.add_argument('--setup-monitoring', action='store_true',
                       help='Setup monitoring and alerting')
    
    args = parser.parse_args()
    
    print(f"🚀 FantasyPro Deployment Pipeline - {args.environment.upper()}")
    print("=" * 60)
    
    if args.setup_monitoring:
        monitoring = MonitoringSetup()
        monitoring.setup_prometheus()
        monitoring.setup_grafana()
        monitoring.setup_alerting()
        print("✅ Monitoring setup completed!")
        return
    
    deployment = DeploymentManager(args.environment)
    
    if args.build_only:
        success = deployment.build_images()
        print("✅ Build completed!" if success else "❌ Build failed!")
        return
    
    if args.health_check:
        health_status = deployment.run_health_checks()
        all_healthy = all(health_status.values())
        print("✅ All services healthy!" if all_healthy else "❌ Some services unhealthy!")
        return
    
    # Full deployment
    success = deployment.deploy()
    
    if success:
        print("\n🎉 Deployment completed successfully!")
        print("📊 Access the platform:")
        print("   • API: http://localhost:8000")
        print("   • Web: http://localhost:3000")
        print("   • API Docs: http://localhost:8000/docs")
    else:
        print("\n❌ Deployment failed!")
        print("Check logs for details and consider rollback if needed.")

if __name__ == "__main__":
    main()
