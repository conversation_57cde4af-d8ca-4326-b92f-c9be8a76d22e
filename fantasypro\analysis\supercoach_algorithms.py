#!/usr/bin/env python3
"""
SuperCoach Analysis Algorithms

Advanced algorithms for price prediction, form analysis, trade recommendations,
and strategic decision making using scraped NRL SuperCoach data.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import logging
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.supercoach_db import supercoach_db, SuperCoachPlayer
from utils.logging_config import setup_logging

logger = setup_logging(__name__)

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"

class FormTrend(Enum):
    RISING = "rising"
    STABLE = "stable"
    DECLINING = "declining"
    VOLATILE = "volatile"

@dataclass
class PricePrediction:
    """Price prediction result"""
    player_id: int
    current_price: float
    predicted_price: float
    price_change: float
    confidence: float
    breakeven_required: int
    probability_increase: float
    probability_decrease: float

@dataclass
class FormAnalysis:
    """Player form analysis result"""
    player_id: int
    current_form: float
    trend: FormTrend
    consistency: float
    recent_average: float
    season_average: float
    momentum_score: float
    risk_level: RiskLevel

@dataclass
class TradeRecommendation:
    """Trade recommendation result"""
    player_out_id: int
    player_in_id: int
    price_difference: float
    expected_points_gain: float
    risk_score: float
    confidence: float
    reasoning: str
    priority: int

class SuperCoachAnalyzer:
    """Main analysis engine for SuperCoach data"""
    
    def __init__(self):
        self.db = supercoach_db
        
    def predict_price_changes(self, rounds_ahead: int = 1) -> List[PricePrediction]:
        """Predict price changes for all players"""
        predictions = []
        
        try:
            players = self.db.get_all_players()
            
            for player in players:
                if not player.current_price or not player.current_breakeven:
                    continue
                
                prediction = self._calculate_price_prediction(player, rounds_ahead)
                if prediction:
                    predictions.append(prediction)
            
            # Sort by expected price change magnitude
            predictions.sort(key=lambda x: abs(x.price_change), reverse=True)
            
        except Exception as e:
            logger.error(f"Error predicting price changes: {e}")
        
        return predictions
    
    def _calculate_price_prediction(self, player: SuperCoachPlayer, rounds_ahead: int) -> Optional[PricePrediction]:
        """Calculate price prediction for a single player"""
        try:
            current_price = player.current_price
            breakeven = player.current_breakeven
            recent_form = player.form_rating or 0
            season_avg = player.season_average or 0
            
            # Estimate expected score based on form and historical data
            if player.recent_scores:
                recent_scores = player.recent_scores[-3:]  # Last 3 games
                recent_avg = sum(recent_scores) / len(recent_scores)
            else:
                recent_avg = season_avg
            
            # Weight recent form more heavily
            expected_score = (recent_avg * 0.6) + (season_avg * 0.4)
            
            # Adjust for form trend
            if recent_form > 8.0:
                expected_score *= 1.1  # Boost for excellent form
            elif recent_form < 6.0:
                expected_score *= 0.9  # Penalty for poor form
            
            # Calculate price change probability
            score_vs_breakeven = expected_score - breakeven
            
            # Price change formula (simplified SuperCoach algorithm)
            if score_vs_breakeven > 0:
                price_change = min(score_vs_breakeven * 3000, 50000)  # Cap at $50k
                prob_increase = min(0.8, score_vs_breakeven / 20)
                prob_decrease = max(0.1, 0.3 - prob_increase)
            else:
                price_change = max(score_vs_breakeven * 3000, -50000)  # Cap at -$50k
                prob_decrease = min(0.8, abs(score_vs_breakeven) / 20)
                prob_increase = max(0.1, 0.3 - prob_decrease)
            
            # Calculate confidence based on data quality
            confidence = 0.5  # Base confidence
            if player.games_played and player.games_played > 5:
                confidence += 0.2
            if player.consistency_rating and player.consistency_rating > 0.7:
                confidence += 0.2
            if len(player.recent_scores or []) >= 3:
                confidence += 0.1
            
            confidence = min(confidence, 0.95)
            
            return PricePrediction(
                player_id=player.id,
                current_price=current_price,
                predicted_price=current_price + price_change,
                price_change=price_change,
                confidence=confidence,
                breakeven_required=breakeven,
                probability_increase=prob_increase,
                probability_decrease=prob_decrease
            )
            
        except Exception as e:
            logger.error(f"Error calculating price prediction for player {player.id}: {e}")
            return None
    
    def analyze_player_form(self, player_id: int) -> Optional[FormAnalysis]:
        """Analyze player form and trends"""
        try:
            with self.db.get_session() as session:
                player = session.query(SuperCoachPlayer).filter_by(id=player_id).first()
                if not player:
                    return None
                
                return self._calculate_form_analysis(player)
                
        except Exception as e:
            logger.error(f"Error analyzing form for player {player_id}: {e}")
            return None
    
    def _calculate_form_analysis(self, player: SuperCoachPlayer) -> FormAnalysis:
        """Calculate comprehensive form analysis"""
        recent_scores = player.recent_scores or []
        season_avg = player.season_average or 0
        current_form = player.form_rating or 5.0
        
        # Calculate recent average
        if len(recent_scores) >= 3:
            recent_avg = sum(recent_scores[-3:]) / 3
        else:
            recent_avg = season_avg
        
        # Determine trend
        if len(recent_scores) >= 3:
            trend_scores = recent_scores[-3:]
            if trend_scores[-1] > trend_scores[0] and recent_avg > season_avg:
                trend = FormTrend.RISING
            elif trend_scores[-1] < trend_scores[0] and recent_avg < season_avg:
                trend = FormTrend.DECLINING
            elif max(trend_scores) - min(trend_scores) > 30:
                trend = FormTrend.VOLATILE
            else:
                trend = FormTrend.STABLE
        else:
            trend = FormTrend.STABLE
        
        # Calculate consistency
        if len(recent_scores) >= 5:
            consistency = 1.0 - (np.std(recent_scores) / max(np.mean(recent_scores), 1))
            consistency = max(0, min(1, consistency))
        else:
            consistency = player.consistency_rating or 0.5
        
        # Calculate momentum score
        momentum = 0.5  # Base momentum
        if trend == FormTrend.RISING:
            momentum += 0.3
        elif trend == FormTrend.DECLINING:
            momentum -= 0.3
        
        if recent_avg > season_avg:
            momentum += 0.2
        elif recent_avg < season_avg:
            momentum -= 0.2
        
        momentum = max(0, min(1, momentum))
        
        # Determine risk level
        if consistency > 0.8 and trend in [FormTrend.RISING, FormTrend.STABLE]:
            risk_level = RiskLevel.LOW
        elif consistency > 0.6 and trend != FormTrend.VOLATILE:
            risk_level = RiskLevel.MEDIUM
        elif trend == FormTrend.VOLATILE or consistency < 0.4:
            risk_level = RiskLevel.HIGH
        else:
            risk_level = RiskLevel.MEDIUM
        
        return FormAnalysis(
            player_id=player.id,
            current_form=current_form,
            trend=trend,
            consistency=consistency,
            recent_average=recent_avg,
            season_average=season_avg,
            momentum_score=momentum,
            risk_level=risk_level
        )
    
    def generate_trade_recommendations(self, 
                                     budget: float = 0, 
                                     position: Optional[str] = None,
                                     limit: int = 10) -> List[TradeRecommendation]:
        """Generate intelligent trade recommendations"""
        recommendations = []
        
        try:
            players = self.db.get_all_players(position=position)
            
            # Get price predictions and form analysis
            price_predictions = {p.player_id: p for p in self.predict_price_changes()}
            
            # Find potential trades
            for player_out in players:
                if not player_out.current_price:
                    continue
                
                # Find suitable replacements
                for player_in in players:
                    if (player_in.id == player_out.id or 
                        not player_in.current_price or
                        player_in.position != player_out.position):
                        continue
                    
                    price_diff = player_in.current_price - player_out.current_price
                    
                    # Check budget constraint
                    if budget > 0 and price_diff > budget:
                        continue
                    
                    # Calculate trade value
                    trade_rec = self._evaluate_trade(player_out, player_in, price_predictions)
                    if trade_rec and trade_rec.expected_points_gain > 0:
                        recommendations.append(trade_rec)
            
            # Sort by expected points gain and confidence
            recommendations.sort(key=lambda x: x.expected_points_gain * x.confidence, reverse=True)
            
            # Assign priorities
            for i, rec in enumerate(recommendations[:limit]):
                rec.priority = i + 1
            
            return recommendations[:limit]
            
        except Exception as e:
            logger.error(f"Error generating trade recommendations: {e}")
            return []
    
    def _evaluate_trade(self, 
                       player_out: SuperCoachPlayer, 
                       player_in: SuperCoachPlayer,
                       price_predictions: Dict[int, PricePrediction]) -> Optional[TradeRecommendation]:
        """Evaluate a potential trade"""
        try:
            price_diff = player_in.current_price - player_out.current_price
            
            # Get form analysis
            form_out = self._calculate_form_analysis(player_out)
            form_in = self._calculate_form_analysis(player_in)
            
            # Calculate expected points difference
            out_expected = form_out.recent_average
            in_expected = form_in.recent_average
            points_gain = in_expected - out_expected
            
            # Adjust for form trends
            if form_in.trend == FormTrend.RISING:
                points_gain += 5
            elif form_in.trend == FormTrend.DECLINING:
                points_gain -= 5
            
            if form_out.trend == FormTrend.DECLINING:
                points_gain += 3
            elif form_out.trend == FormTrend.RISING:
                points_gain -= 3
            
            # Calculate risk score
            risk_score = 0.5
            if form_in.risk_level == RiskLevel.HIGH:
                risk_score += 0.3
            elif form_in.risk_level == RiskLevel.LOW:
                risk_score -= 0.2
            
            if form_out.risk_level == RiskLevel.HIGH:
                risk_score -= 0.2
            
            risk_score = max(0.1, min(0.9, risk_score))
            
            # Calculate confidence
            confidence = (form_in.consistency + form_out.consistency) / 2
            
            # Only recommend if there's significant benefit
            if points_gain < 2 and price_diff > 0:
                return None
            
            # Generate reasoning
            reasoning = self._generate_trade_reasoning(player_out, player_in, form_out, form_in, points_gain)
            
            return TradeRecommendation(
                player_out_id=player_out.id,
                player_in_id=player_in.id,
                price_difference=price_diff,
                expected_points_gain=points_gain,
                risk_score=risk_score,
                confidence=confidence,
                reasoning=reasoning,
                priority=0  # Will be set later
            )
            
        except Exception as e:
            logger.error(f"Error evaluating trade: {e}")
            return None
    
    def _generate_trade_reasoning(self, 
                                player_out: SuperCoachPlayer, 
                                player_in: SuperCoachPlayer,
                                form_out: FormAnalysis,
                                form_in: FormAnalysis,
                                points_gain: float) -> str:
        """Generate human-readable reasoning for trade recommendation"""
        reasons = []
        
        # Form-based reasons
        if form_in.trend == FormTrend.RISING:
            reasons.append(f"{player_in.name} is in rising form")
        if form_out.trend == FormTrend.DECLINING:
            reasons.append(f"{player_out.name} form is declining")
        
        # Performance-based reasons
        if form_in.recent_average > form_in.season_average:
            reasons.append(f"{player_in.name} averaging {form_in.recent_average:.1f} recently vs {form_in.season_average:.1f} season")
        
        # Value-based reasons
        price_diff = player_in.current_price - player_out.current_price
        if price_diff < 0:
            reasons.append(f"Save ${abs(price_diff):,.0f} in the trade")
        elif price_diff > 0:
            reasons.append(f"Worth the ${price_diff:,.0f} upgrade")
        
        # Points expectation
        if points_gain > 5:
            reasons.append(f"Expected +{points_gain:.1f} points per round")
        
        return ". ".join(reasons) if reasons else "Strategic positional upgrade"


# Global analyzer instance
supercoach_analyzer = SuperCoachAnalyzer()
