#!/usr/bin/env python3
"""
SuperCoach Official Data Scraper
Authenticates with SuperCoach.com.au and extracts comprehensive data for ML enhancement
"""

import requests
from bs4 import BeautifulSoup
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import os
import time
from pathlib import Path
import re
from urllib.parse import urljoin, urlparse
import base64

logger = logging.getLogger(__name__)

class SuperCoachScraper:
    """Comprehensive SuperCoach data scraper with authentication"""
    
    def __init__(self, email: Optional[str] = None, password: Optional[str] = None, cache_hours: int = 6):
        # Credentials (will be encrypted in production)
        self.email = email or self._get_encrypted_email()
        self.password = password or self._get_encrypted_password()
        
        self.cache_hours = cache_hours
        self.cache_dir = Path("data/supercoach_cache")
        self.cache_dir.mkdir(exist_ok=True)
        
        # Session for maintaining login with enhanced stealth
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-AU,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-CH-UA': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"'
        })
        
        self.base_url = 'https://www.supercoach.com.au'
        self.is_authenticated = False
        
        # Key pages to scrape
        self.target_pages = {
            'ladder': '/nrl/ladder',
            'players': '/nrl/players',
            'stats': '/nrl/stats',
            'teams': '/nrl/teams',
            'fixtures': '/nrl/fixtures',
            'trades': '/nrl/trades',
            'prices': '/nrl/prices',
            'breakevens': '/nrl/breakevens',
            'ownership': '/nrl/ownership',
            'form': '/nrl/form',
            'projections': '/nrl/projections'
        }
    
    def _get_encrypted_email(self) -> str:
        """Get encrypted email (base64 for now, will use proper encryption in production)"""
        # Base64 encoded email for basic obfuscation
        encoded = "************************"
        return base64.b64decode(encoded).decode('utf-8')
    
    def _get_encrypted_password(self) -> str:
        """Get encrypted password (base64 for now, will use proper encryption in production)"""
        # Base64 encoded password for basic obfuscation
        encoded = "U3VwZXJLZW5ueTEyMyE="
        return base64.b64decode(encoded).decode('utf-8')
    
    def authenticate(self) -> bool:
        """Authenticate with SuperCoach.com.au"""
        try:
            logger.info("Authenticating with SuperCoach.com.au")

            # Step 1: First visit the main site to establish session
            time.sleep(2)  # Rate limiting
            main_response = self.session.get(self.base_url, timeout=15)
            logger.info(f"Main site response: {main_response.status_code}")

            if main_response.status_code == 403:
                logger.error("Main site blocked with 403 - likely anti-bot protection")
                return False

            time.sleep(3)  # Additional delay

            # Step 2: Try different login URLs
            login_urls = [
                f"{self.base_url}/login",
                f"{self.base_url}/auth/login",
                f"{self.base_url}/nrl/login",
                f"{self.base_url}/account/login"
            ]

            login_response = None
            for login_url in login_urls:
                try:
                    logger.info(f"Trying login URL: {login_url}")
                    response = self.session.get(login_url, timeout=15)
                    logger.info(f"Login page response: {response.status_code}")

                    if response.status_code == 200:
                        login_response = response
                        self.login_url = login_url
                        break
                    elif response.status_code == 403:
                        logger.warning(f"403 Forbidden for {login_url} - trying next")
                        continue

                except Exception as e:
                    logger.warning(f"Error accessing {login_url}: {e}")
                    continue

            if not login_response:
                logger.error("Could not access any login page")
                return False

            soup = BeautifulSoup(login_response.content, 'html.parser')

            # Debug: Print form information
            forms = soup.find_all('form')
            logger.info(f"Found {len(forms)} forms on login page")

            for i, form in enumerate(forms):
                action = form.get('action', 'No action')
                method = form.get('method', 'GET')
                logger.info(f"Form {i}: {method} {action}")

                inputs = form.find_all('input')
                for inp in inputs:
                    name = inp.get('name', 'No name')
                    type_attr = inp.get('type', 'text')
                    logger.info(f"  Input: {name} ({type_attr})")

            # Extract CSRF token or other required fields
            csrf_token = None
            csrf_selectors = [
                'input[name="_token"]',
                'input[name="csrf_token"]',
                'input[name="authenticity_token"]',
                'meta[name="csrf-token"]'
            ]

            for selector in csrf_selectors:
                csrf_elem = soup.select_one(selector)
                if csrf_elem:
                    csrf_token = csrf_elem.get('value') or csrf_elem.get('content')
                    if csrf_token:
                        logger.info(f"Found CSRF token with selector: {selector}")
                        break

            # Find the login form
            login_form = None
            for form in forms:
                # Look for forms with email/password fields
                if form.find('input', {'type': 'email'}) or form.find('input', {'name': 'email'}):
                    login_form = form
                    break
                elif form.find('input', {'type': 'password'}):
                    login_form = form
                    break

            if not login_form:
                logger.error("Could not find login form")
                return False

            # Get form action
            form_action = login_form.get('action', '/login')
            if not form_action.startswith('http'):
                form_action = f"{self.base_url}{form_action}"

            logger.info(f"Submitting to: {form_action}")

            # Prepare login data
            login_data = {
                'email': self.email,
                'password': self.password
            }

            if csrf_token:
                login_data['_token'] = csrf_token

            # Add any hidden fields from the form
            for hidden_input in login_form.find_all('input', {'type': 'hidden'}):
                name = hidden_input.get('name')
                value = hidden_input.get('value', '')
                if name and name not in login_data:
                    login_data[name] = value
                    logger.info(f"Added hidden field: {name} = {value}")

            # Step 3: Submit login form
            login_submit_response = self.session.post(
                form_action,
                data=login_data,
                timeout=15,
                allow_redirects=True
            )

            logger.info(f"Login submit response: {login_submit_response.status_code}")
            logger.info(f"Final URL: {login_submit_response.url}")

            # Step 4: Check if login was successful
            if self._check_authentication(login_submit_response):
                self.is_authenticated = True
                logger.info("Successfully authenticated with SuperCoach")
                return True
            else:
                logger.error("Authentication failed")
                # Debug: Save response for analysis
                with open('debug_login_response.html', 'w', encoding='utf-8') as f:
                    f.write(login_submit_response.text)
                logger.info("Saved login response to debug_login_response.html")
                return False

        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            return False
    
    def _check_authentication(self, response) -> bool:
        """Check if authentication was successful"""
        try:
            logger.info(f"Checking authentication for URL: {response.url}")

            # Check for redirect to dashboard or profile
            url_success_indicators = ['dashboard', 'profile', 'team', 'my-team', 'account']
            if any(indicator in response.url.lower() for indicator in url_success_indicators):
                logger.info("Authentication successful - redirected to authenticated page")
                return True

            # Check response content for success indicators
            content = response.text.lower()

            # Strong success indicators
            strong_success = ['my team', 'logout', 'my account', 'team management']
            # Failure indicators
            failure_indicators = [
                'login failed', 'invalid credentials', 'incorrect email',
                'incorrect password', 'authentication failed', 'login error',
                'please try again', 'invalid login'
            ]

            has_strong_success = any(indicator in content for indicator in strong_success)
            has_failure = any(indicator in content for indicator in failure_indicators)

            if has_failure:
                logger.warning("Authentication failed - found failure indicators")
                return False

            if has_strong_success:
                logger.info("Authentication successful - found success indicators")
                return True

            # Check for presence of login form (indicates we're still on login page)
            if 'type="password"' in content and 'email' in content:
                logger.warning("Authentication failed - still on login page")
                return False

            # If we're not on login page and no failure indicators, assume success
            if response.url != self.login_url and not has_failure:
                logger.info("Authentication likely successful - redirected away from login")
                return True

            logger.warning("Authentication status unclear")
            return False

        except Exception as e:
            logger.error(f"Error checking authentication: {e}")
            return False
    
    def scrape_all_data(self) -> Dict[str, Any]:
        """Scrape all available SuperCoach data"""
        try:
            if not self.is_authenticated and not self.authenticate():
                logger.error("Cannot scrape data without authentication")
                return {}
            
            all_data = {
                'last_updated': datetime.now().isoformat(),
                'source': 'supercoach.com.au',
                'data': {}
            }
            
            # Scrape each target page
            for page_name, page_path in self.target_pages.items():
                try:
                    logger.info(f"Scraping {page_name} data...")
                    page_data = self._scrape_page(page_path, page_name)
                    if page_data:
                        all_data['data'][page_name] = page_data
                        logger.info(f"Successfully scraped {page_name}")
                    else:
                        logger.warning(f"No data found for {page_name}")
                    
                    # Rate limiting
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"Error scraping {page_name}: {e}")
                    continue
            
            # Save comprehensive data
            self._save_comprehensive_cache(all_data)
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error scraping all data: {e}")
            return {}
    
    def _scrape_page(self, page_path: str, page_name: str) -> Optional[Dict]:
        """Scrape a specific page"""
        try:
            url = urljoin(self.base_url, page_path)
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Use specific extraction method based on page type
            if page_name == 'ladder':
                return self._extract_ladder_data(soup)
            elif page_name == 'players':
                return self._extract_players_data(soup)
            elif page_name == 'stats':
                return self._extract_stats_data(soup)
            elif page_name == 'teams':
                return self._extract_teams_data(soup)
            elif page_name == 'fixtures':
                return self._extract_fixtures_data(soup)
            elif page_name in ['trades', 'prices', 'breakevens', 'ownership', 'form']:
                return self._extract_financial_data(soup, page_name)
            else:
                return self._extract_generic_data(soup, page_name)
                
        except Exception as e:
            logger.error(f"Error scraping page {page_path}: {e}")
            return None
    
    def _extract_ladder_data(self, soup: BeautifulSoup) -> Dict:
        """Extract SuperCoach ladder/rankings data"""
        try:
            ladder_data = {
                'rankings': [],
                'total_coaches': 0,
                'extracted_at': datetime.now().isoformat()
            }
            
            # Look for ladder table
            table_selectors = ['table', '.ladder-table', '.rankings-table', '.leaderboard']
            table = None
            
            for selector in table_selectors:
                table = soup.select_one(selector)
                if table:
                    break
            
            if table:
                rows = table.find_all('tr')[1:]  # Skip header
                for i, row in enumerate(rows[:100]):  # Top 100
                    if hasattr(row, 'find_all'):
                        cells = row.find_all(['td', 'th'])
                    if len(cells) >= 3:
                        try:
                            rank = self._extract_number(cells[0].get_text())
                            coach_name = cells[1].get_text(strip=True)
                            points = self._extract_number(cells[2].get_text())
                            
                            if rank and coach_name and points:
                                ladder_data['rankings'].append({
                                    'rank': rank,
                                    'coach_name': coach_name,
                                    'total_points': points,
                                    'round_points': self._extract_number(cells[3].get_text()) if len(cells) > 3 else 0
                                })
                        except Exception as e:
                            logger.debug(f"Error extracting ladder row: {e}")
                            continue
            
            ladder_data['total_coaches'] = len(ladder_data['rankings'])
            return ladder_data
            
        except Exception as e:
            logger.error(f"Error extracting ladder data: {e}")
            return {}
    
    def _extract_players_data(self, soup: BeautifulSoup) -> Dict:
        """Extract comprehensive player data"""
        try:
            players_data = {
                'players': [],
                'extracted_at': datetime.now().isoformat()
            }
            
            # Look for player tables or cards
            player_selectors = ['.player-card', '.player-row', 'tr[data-player]', '.player-item']
            
            for selector in player_selectors:
                elements = soup.select(selector)
                if elements:
                    for element in elements[:200]:  # Limit to prevent overload
                        player_data = self._extract_single_player(element)
                        if player_data:
                            players_data['players'].append(player_data)
                    break
            
            return players_data
            
        except Exception as e:
            logger.error(f"Error extracting players data: {e}")
            return {}
    
    def _extract_single_player(self, element) -> Optional[Dict]:
        """Extract data for a single player"""
        try:
            player = {}
            
            # Extract player name
            name_selectors = ['.player-name', '.name', 'h3', 'h4', '[data-name]']
            for selector in name_selectors:
                name_elem = element.select_one(selector)
                if name_elem:
                    player['name'] = name_elem.get_text(strip=True)
                    break
            
            # Extract price
            price_selectors = ['.price', '.cost', '[data-price]', '.player-price']
            for selector in price_selectors:
                price_elem = element.select_one(selector)
                if price_elem:
                    player['price'] = self._extract_number(price_elem.get_text())
                    break
            
            # Extract position
            pos_selectors = ['.position', '.pos', '[data-position]']
            for selector in pos_selectors:
                pos_elem = element.select_one(selector)
                if pos_elem:
                    player['position'] = pos_elem.get_text(strip=True)
                    break
            
            # Extract team
            team_selectors = ['.team', '.club', '[data-team]']
            for selector in team_selectors:
                team_elem = element.select_one(selector)
                if team_elem:
                    player['team'] = team_elem.get_text(strip=True)
                    break
            
            # Extract points/stats
            stats_text = element.get_text()
            player['average'] = self._extract_average_from_text(stats_text)
            player['breakeven'] = self._extract_breakeven_from_text(stats_text)
            player['ownership'] = self._extract_ownership_from_text(stats_text)
            
            return player if player.get('name') else None
            
        except Exception as e:
            logger.debug(f"Error extracting single player: {e}")
            return None
    
    def _extract_stats_data(self, soup: BeautifulSoup) -> Dict:
        """Extract statistical data"""
        try:
            return self._extract_generic_data(soup, 'stats')
        except Exception as e:
            logger.error(f"Error extracting stats data: {e}")
            return {}
    
    def _extract_teams_data(self, soup: BeautifulSoup) -> Dict:
        """Extract team data"""
        try:
            return self._extract_generic_data(soup, 'teams')
        except Exception as e:
            logger.error(f"Error extracting teams data: {e}")
            return {}
    
    def _extract_fixtures_data(self, soup: BeautifulSoup) -> Dict:
        """Extract fixture data"""
        try:
            return self._extract_generic_data(soup, 'fixtures')
        except Exception as e:
            logger.error(f"Error extracting fixtures data: {e}")
            return {}
    
    def _extract_financial_data(self, soup: BeautifulSoup, data_type: str) -> Dict:
        """Extract financial data (prices, breakevens, etc.)"""
        try:
            return self._extract_generic_data(soup, data_type)
        except Exception as e:
            logger.error(f"Error extracting {data_type} data: {e}")
            return {}
    
    def _extract_generic_data(self, soup: BeautifulSoup, page_type: str) -> Dict:
        """Generic data extraction for any page"""
        try:
            data = {
                'page_type': page_type,
                'extracted_at': datetime.now().isoformat(),
                'tables': [],
                'lists': [],
                'key_stats': {}
            }
            
            # Extract all tables
            tables = soup.find_all('table')
            for i, table in enumerate(tables[:5]):  # Limit tables
                table_data = self._extract_table_data(table)
                if table_data:
                    data['tables'].append({
                        'table_index': i,
                        'data': table_data
                    })
            
            # Extract lists
            lists = soup.find_all(['ul', 'ol'])
            for i, list_elem in enumerate(lists[:3]):  # Limit lists
                if hasattr(list_elem, 'find_all'):
                    list_data = [li.get_text(strip=True) for li in list_elem.find_all('li')]
                    if list_data:
                        data['lists'].append({
                            'list_index': i,
                            'items': list_data
                        })
            
            # Extract key numbers from text
            text = soup.get_text()
            data['key_stats'] = self._extract_key_numbers(text)
            
            return data
            
        except Exception as e:
            logger.error(f"Error in generic data extraction: {e}")
            return {}
    
    def _extract_table_data(self, table) -> List[List[str]]:
        """Extract data from HTML table"""
        try:
            rows = []
            for row in table.find_all('tr'):
                cells = [cell.get_text(strip=True) for cell in row.find_all(['td', 'th'])]
                if cells:
                    rows.append(cells)
            return rows
        except Exception:
            return []
    
    def _extract_number(self, text: str) -> Optional[int]:
        """Extract number from text"""
        try:
            # Remove common formatting
            clean_text = re.sub(r'[,$%]', '', str(text))
            numbers = re.findall(r'\d+', clean_text)
            return int(numbers[0]) if numbers else None
        except Exception:
            return None
    
    def _extract_average_from_text(self, text: str) -> Optional[float]:
        """Extract average score from text"""
        try:
            # Look for patterns like "Avg: 85.4" or "85.4 avg"
            patterns = [r'avg[:\s]*(\d+\.?\d*)', r'(\d+\.?\d*)\s*avg']
            for pattern in patterns:
                match = re.search(pattern, text.lower())
                if match:
                    return float(match.group(1))
            return None
        except Exception:
            return None
    
    def _extract_breakeven_from_text(self, text: str) -> Optional[int]:
        """Extract breakeven from text"""
        try:
            # Look for patterns like "BE: 85" or "Breakeven 85"
            patterns = [r'be[:\s]*(\d+)', r'breakeven[:\s]*(\d+)']
            for pattern in patterns:
                match = re.search(pattern, text.lower())
                if match:
                    return int(match.group(1))
            return None
        except Exception:
            return None
    
    def _extract_ownership_from_text(self, text: str) -> Optional[float]:
        """Extract ownership percentage from text"""
        try:
            # Look for patterns like "15.2%" or "Owned: 15.2%"
            patterns = [r'(\d+\.?\d*)%', r'owned[:\s]*(\d+\.?\d*)']
            for pattern in patterns:
                match = re.search(pattern, text.lower())
                if match:
                    return float(match.group(1))
            return None
        except Exception:
            return None
    
    def _extract_key_numbers(self, text: str) -> Dict:
        """Extract key statistical numbers from text"""
        try:
            stats = {}
            
            # Common SuperCoach terms and their patterns
            patterns = {
                'total_points': r'total[:\s]*(\d+)',
                'round_score': r'round[:\s]*(\d+)',
                'rank': r'rank[:\s]*(\d+)',
                'trades_left': r'trades?[:\s]*(\d+)',
                'salary_cap': r'cap[:\s]*\$?(\d+)',
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, text.lower())
                if match:
                    stats[key] = int(match.group(1))
            
            return stats
            
        except Exception:
            return {}
    
    def _save_comprehensive_cache(self, data: Dict):
        """Save comprehensive data to cache"""
        try:
            cache_file = self.cache_dir / f"supercoach_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Also save as latest
            latest_file = self.cache_dir / "supercoach_latest.json"
            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved SuperCoach data to {cache_file}")
            
        except Exception as e:
            logger.error(f"Error saving cache: {e}")
    
    def get_cached_data(self) -> Optional[Dict]:
        """Get latest cached data"""
        try:
            latest_file = self.cache_dir / "supercoach_latest.json"
            if latest_file.exists():
                with open(latest_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading cached data: {e}")
        return None

def main():
    """Test the SuperCoach scraper"""
    print("🏆 Testing SuperCoach Scraper")
    
    scraper = SuperCoachScraper()
    
    if scraper.authenticate():
        print("✅ Authentication successful")
        
        # Test scraping
        data = scraper.scrape_all_data()
        print(f"📊 Scraped data from {len(data.get('data', {}))} pages")
        
        for page_name, page_data in data.get('data', {}).items():
            print(f"  - {page_name}: {len(str(page_data))} characters")
    else:
        print("❌ Authentication failed")

if __name__ == "__main__":
    main()
